<?php

namespace SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Database\Orm\DatabaseErrorExtractor;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\SubcategoryRepository;
use Doctrine\DBAL\Exception\DriverException;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\SubcategoryUpsertTopicContent;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;

final class SubcategoryTopicUpsertSynchronizer extends AbstractTopicSynchronizer
{
    protected const TOPIC = SynchronizableTopicName::SUBCATEGORY_UPSERT;
    private SubcategoryRepository $subcategory_repository;

    public function __construct(SubcategoryRepository $subcategory_repository)
    {
        $this->subcategory_repository = $subcategory_repository;
    }

    /** @param SubcategoryUpsertTopicContent $synchronizable_topic */
    public function synchronize(AbstractSynchronizableTopicContent $synchronizable_topic): void
    {
        try {
            $this->logger->debug(json_encode($synchronizable_topic));
            $this->subcategory_repository->upsert($this->serializer->normalize($synchronizable_topic));
        } catch (\Exception $exception) {
            $this->logger->debug(
                $exception instanceof DriverException
                    ? DatabaseErrorExtractor::extract($exception)
                    : $exception->getMessage()
            );

            throw new \RuntimeException(sprintf('Failed to synchronize topic "%s" with id %d', self::TOPIC, $synchronizable_topic->synchronizable_topic_id), 0, $exception);
        }
    }
}
