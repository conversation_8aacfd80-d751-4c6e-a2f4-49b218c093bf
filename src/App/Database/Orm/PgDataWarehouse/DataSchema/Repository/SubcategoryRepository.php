<?php

declare(strict_types=1);

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgDataWarehouseRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\Subcategory;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model\SubcategoryModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class SubcategoryRepository extends AbstractPgDataWarehouseRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = SubcategoryModel::class;
    public const ENTITY_NAME = Subcategory::class;
}
