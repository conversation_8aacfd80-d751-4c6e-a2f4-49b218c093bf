<?php

declare(strict_types=1);
/**
 * Subcategory Definition.
 *
 * Structure class for relation data.subcategory.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 */

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model;

use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation data.subcategory.
 *
 * @ORM\Model(name="data.subcategory", engine="postgresql")
 */
class SubcategoryModel
{
    /** @ORM\Column(primary_key=true) */
    public int $subcategory_id;

    /** @ORM\Column */
    public int $subcategory_name;

    /** @ORM\Column() */
    public int $parent_category_id;

    /** @ORM\Column */
    public string $parent_category_name;

    /** @ORM\Column */
    public string $subcategory_type;

    /** @ORM\Column */
    public ?int $user_id = null;

    /** @ORM\Column */
    public ?string $user_name = null;
}
