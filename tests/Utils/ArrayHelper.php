<?php

namespace App\Tests\Utils;

final class ArrayHelper
{
    public static function sortKeys(array $array): array
    {
        ksort($array);

        return $array;
    }

    /** @param $keys_to_remove string[] */
    public static function removeKeysFrom(array $array, array $keys_to_remove): array
    {
        foreach ($array as $key => $value) {
            if (in_array($key, $keys_to_remove, true)) {
                unset($array[$key]);
            }

            if (is_array($value)) {
                $array[$key] = self::removeKeysFrom($value, $keys_to_remove);
            }
        }

        return $array;
    }
}
