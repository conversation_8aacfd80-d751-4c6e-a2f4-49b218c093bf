<?php

namespace App\Tests\Utils\PHPUnit\EndToEnd;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Component\HttpFoundation\Request;

final class BasicE2eJsonRequest
{
    private KernelBrowser $client;

    public function setClient(KernelBrowser $client): self
    {
        $this->client = $client;

        return $this;
    }

    public static function with(KernelBrowser $client): self
    {
        return (new self())->setClient($client);
    }

    public function post(string $url, string $json_content = '', array $headers = []): void
    {
        $this->client->request(
            Request::METHOD_POST,
            $url,
            [],
            [],
            array_merge(
                [
                    'HTTP_AUTHORIZATION' => BearerReferential::WITHOUT_PERMISSION_TOKEN,
                    'CONTENT_TYPE' => 'application/json',
                ],
                $headers
            ),
            $json_content
        );
    }

    public function get(string $url, array $headers = []): void
    {
        $this->client->request(
            Request::METHOD_GET,
            $url,
            [],
            [],
            array_merge(
                [
                    'HTTP_AUTHORIZATION' => BearerReferential::WITHOUT_PERMISSION_TOKEN,
                    'CONTENT_TYPE' => 'application/json',
                ],
                $headers
            )
        );
    }

    public function put(string $url, string $json_content = '', array $headers = []): void
    {
        $this->client->request(
            Request::METHOD_PUT,
            $url,
            [],
            [],
            array_merge(
                [
                    'HTTP_AUTHORIZATION' => BearerReferential::WITHOUT_PERMISSION_TOKEN,
                    'CONTENT_TYPE' => 'application/json',
                ],
                $headers
            ),
            $json_content
        );
    }

    public function delete(string $url, array $headers = []): void
    {
        $this->client->request(
            Request::METHOD_DELETE,
            $url,
            [],
            [],
            array_merge(
                [
                    'HTTP_AUTHORIZATION' => BearerReferential::WITHOUT_PERMISSION_TOKEN,
                    'CONTENT_TYPE' => 'application/json',
                ],
                $headers
            )
        );
    }
}
