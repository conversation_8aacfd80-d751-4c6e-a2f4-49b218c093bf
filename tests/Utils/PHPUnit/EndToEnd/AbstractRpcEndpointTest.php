<?php

namespace App\Tests\Utils\PHPUnit\EndToEnd;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

abstract class AbstractRpcEndpointTest extends WebTestCase
{
    protected KernelBrowser $client;
    protected const RPC_ENDPOINT = '/rpc';
    protected const DISCOVER_METHOD = 'discover';
    protected const RPC_METHOD = self::class;

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();

        if (self::class === static::RPC_METHOD) {
            throw new \Exception('RPC_METHOD must be defined in the child class');
        }
    }

    /** Helper method to send RPC requests */
    protected function sendRpcRequest(string $method, array $params = []): void
    {
        $payload = [
            'jsonrpc' => '2.0',
            'id' => '1',
            'method' => $method,
            'params' => $params,
        ];

        $this->client->request(
            'POST',
            self::RPC_ENDPOINT,
            [],
            [],
            ['HTTP_CONTENT_TYPE' => 'application/json'],
            json_encode($payload, JSON_THROW_ON_ERROR)
        );
    }

    /** Helper method to get the response data */
    protected function getResponseData(): array
    {
        return json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
    }

    /** Test that the RPC endpoint exists using the discover method */
    public function test_rpc_endpoint_exists_in_discover_method(): void
    {
        $this->sendRpcRequest(self::DISCOVER_METHOD);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey(static::RPC_METHOD, $response_data['result']);
    }

    /** Test that calling the RPC with no payload returns an error */
    protected function assertRpcWithNoPayloadFails(string $method_name, string $expected_error_message): void
    {
        $this->sendRpcRequest($method_name);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertEquals($expected_error_message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }
}
