<?php

namespace App\Tests\Utils\PHPUnit;

use PommProject\Foundation\Session\ResultHandler;
use PommProject\ModelManager\Session;

trait PhpUnitPommHelperTrait
{
    protected function getPommSession(string $session = 'admin'): Session
    {
        return static::$container->get('pomm')->getSession($session);
    }

    /**
     * Create a new Pomm session, useful for find data updated on a transaction.
     *
     * @return mixed
     */
    protected function getNewPommSession()
    {
        return static::$container->get('pomm')->createSession('admin');
    }

    /** @return ResultHandler|array */
    protected function executeSqlQuery(string $sql, string $session = 'admin')
    {
        return $this->getPommSession($session)
            ->getConnection()
            ->executeAnonymousQuery($sql);
    }
}
