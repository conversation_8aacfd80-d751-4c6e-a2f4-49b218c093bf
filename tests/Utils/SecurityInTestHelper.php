<?php

namespace App\Tests\Utils;

use SonVideo\HalMiddlewareBundle\Infrastructure\Adapter\Security\Provider\UserProvider;
use Symfony\Component\Security\Core\Authentication\Token\PreAuthenticatedToken;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class SecurityInTestHelper
{
    public const ADMIN_ACCOUNT = 'admin';
    public const REGULAR_ACCOUNT = 'gege';

    /**
     * @var string[]
     *
     * @see tests/fixtures/pgsql/01-permissions.sql
     */
    private const ACCOUNTS = [
        self::ADMIN_ACCOUNT => '9e81bd23-e7ac-4ba3-842f-8da6554bc540',
        self::REGULAR_ACCOUNT => 'ed654889-b92e-4e86-9e69-b1165fb91220',
    ];

    /** @var UserProvider */
    private $user_provider;

    /** @var TokenStorageInterface */
    private $token_storage;

    public function __construct(UserProvider $user_provider, TokenStorageInterface $token_storage)
    {
        $this->user_provider = $user_provider;
        $this->token_storage = $token_storage;
    }

    public function logInAs(string $username): void
    {
        $user = $this->user_provider->loadUserByUsername(self::ACCOUNTS[$username]);
        $this->token_storage->setToken(new PreAuthenticatedToken($user, 'user-token', 'api_provider', ['ROLE_USER']));
    }

    public function logout(): void
    {
        $this->token_storage->setToken(null);
    }
}
