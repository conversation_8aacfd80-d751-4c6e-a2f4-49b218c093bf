<?php

namespace App\Tests\Utils;

use Monolog\Handler\TestHandler;
use Monolog\Logger;

final class MonologTest
{
    /** @var TestHandler */
    private static $handler;

    /** Does not use the PSR LoggerInterface, the pushHandler method is monolog specific */
    public static function initialize(Logger $logger): void
    {
        self::$handler = new TestHandler();
        $logger->pushHandler(self::$handler);
    }

    /** Will throw an exception if initialize has not been called */
    public static function handler(): TestHandler
    {
        return self::$handler;
    }
}
