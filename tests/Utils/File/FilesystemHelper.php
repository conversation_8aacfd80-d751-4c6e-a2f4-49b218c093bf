<?php

namespace App\Tests\Utils\File;

use League\Flysystem\MountManager;

class FilesystemHelper
{
    private MountManager $mount_manager;

    public function __construct(MountManager $mount_manager)
    {
        $this->mount_manager = $mount_manager;
    }

    public function moveFile(string $target_fs, string $target_name, string $source_name): void
    {
        $source_fs = $this->mount_manager->getFilesystem('mock_filesystem');
        $target_fs = $this->mount_manager->getFilesystem(sprintf('%s_filesystem', $target_fs));

        if ($target_fs->has($target_name)) {
            $target_fs->delete($target_name);
        }

        $content = $source_fs->read($source_name);
        $target_fs->write($target_name, $content);
    }
}
