<?php

namespace PHPUnit\Unit\App\Client;

use App\Client\GraphQLClient;
use App\Exception\GraphQLResponseException;
use Guzzle<PERSON>ttp\Client;
use Guzzle<PERSON>ttp\Handler\MockHandler;
use Guzzle<PERSON>ttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class GraphQLClientTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of GraphQLClient with a mock handler. */
    protected function getTestedInstance(MockHandler $mock): GraphQLClient
    {
        $handler_stack = HandlerStack::create($mock);
        $logger = $this->createMock(LoggerInterface::class);

        return new GraphQLClient('', '', $logger, new Client(['handler' => $handler_stack]));
    }

    /** Tests that the call method returns the response correctly. */
    public function test_call_returns_response_correctly(): void
    {
        // Given
        $mock = new MockHandler([new Response(200, [], '[{"foo": "bar"}]')]);
        $instance = $this->getTestedInstance($mock);

        // When
        $response = $instance->call(['query' => '']);

        // Then
        $this->assertIsArray($response);
        $this->assertCount(1, $response);
    }

    /** Tests that the call method raises an exception when errors are present. */
    public function test_call_raises_exception_with_empty_errors(): void
    {
        // Given
        $mock = new MockHandler([new Response(200, [], '{"errors": []}')]);
        $instance = $this->getTestedInstance($mock);

        // Then
        $this->expectException(GraphQLResponseException::class);
        $this->expectExceptionMessage('GraphQL call failed');

        // When
        $instance->call(['query' => '']);
    }

    /** Tests that the call method raises an exception with a more complete error message. */
    public function test_call_raises_exception_with_detailed_error_message(): void
    {
        // Given
        $mock = new MockHandler([new Response(200, [], '{"errors": [ { "message": "test" }]}')]);
        $instance = $this->getTestedInstance($mock);

        // Then
        $this->expectException(GraphQLResponseException::class);
        $this->expectExceptionMessage('GraphQL call failed : test');

        // When
        $instance->call(['query' => '']);
    }
}
