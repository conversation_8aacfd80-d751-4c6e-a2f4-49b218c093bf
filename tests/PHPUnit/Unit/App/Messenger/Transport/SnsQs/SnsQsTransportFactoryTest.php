<?php

namespace Tests\PHPUnit\App\Messenger\Transport\SnsQs;

use App\Adapter\Serializer\SerializerInterface;
use App\Messenger\Routing\RoutingConfiguration;
use App\Messenger\Transport\SnsQs\SnsQsTransport;
use App\Messenger\Transport\SnsQs\SnsQsTransportFactory;
use App\Tests\PHPUnit\TestCase;
use Aws\Sns\SnsClient;
use Aws\Sqs\SqsClient;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface as TransportSerializerInterface;

/**
 * Class SnsQsTransportFactoryTest
 * Tests for the SNS/SQS Transport Factory.
 */
class SnsQsTransportFactoryTest extends TestCase
{
    private const TEST_TOPIC = 'test-topic';
    private const TEST_QUEUE = 'test-queue';
    private const TEST_TRANSPORT = 'test_transport';
    private const FIFO_SUFFIX = '.fifo';

    /** @var SnsClient&MockObject */
    private SnsClient $sns_client;
    /** @var SqsClient&MockObject */
    private SqsClient $sqs_client;
    /** @var SerializerInterface&MockObject */
    private SerializerInterface $serializer;
    /** @var RoutingConfiguration&MockObject */
    private RoutingConfiguration $routing_configuration;
    /** @var TransportSerializerInterface&MockObject */
    private TransportSerializerInterface $transport_serializer;
    private SnsQsTransportFactory $factory;

    protected function setUp(): void
    {
        $this->sns_client = $this->createMock(SnsClient::class);
        $this->sqs_client = $this->createMock(SqsClient::class);
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->routing_configuration = $this->createMock(RoutingConfiguration::class);
        $this->transport_serializer = $this->createMock(TransportSerializerInterface::class);

        $this->factory = new SnsQsTransportFactory(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration
        );
    }

    public function transportConfigurationProvider(): array
    {
        return [
            'topic_and_queue_in_dsn' => [
                'dsn' => sprintf('sns+sqs://topic=%s&queue=%s', self::TEST_TOPIC, self::TEST_QUEUE),
                'options' => ['transport_name' => self::TEST_TRANSPORT],
            ],
            'topic_and_queue_in_options' => [
                'dsn' => 'sns+sqs://',
                'options' => [
                    'topic' => self::TEST_TOPIC,
                    'queue' => self::TEST_QUEUE,
                    'transport_name' => self::TEST_TRANSPORT,
                ],
            ],
            'topic_in_dsn_queue_in_options' => [
                'dsn' => sprintf('sns+sqs://topic=%s', self::TEST_TOPIC),
                'options' => [
                    'queue' => self::TEST_QUEUE,
                    'transport_name' => self::TEST_TRANSPORT,
                ],
            ],
        ];
    }

    public function test_supports(): void
    {
        $this->assertTrue($this->factory->supports('sns+sqs://', []), 'Should support sns+sqs protocol');
        $this->assertFalse($this->factory->supports('redis://', []), 'Should not support other protocols');
        $this->assertFalse($this->factory->supports('sqs://', []), 'Should not support sqs protocol alone');
    }

    /** @dataProvider transportConfigurationProvider */
    public function test_create_transport_with_various_configurations(string $dsn, array $options): void
    {
        $transport = $this->factory->createTransport($dsn, $options, $this->transport_serializer);
        $this->assertInstanceOf(SnsQsTransport::class, $transport);
    }

    public function test_create_transport_with_fifo_queue(): void
    {
        $dsn = sprintf(
            'sns+sqs://topic=%s%s&queue=%s%s',
            self::TEST_TOPIC,
            self::FIFO_SUFFIX,
            self::TEST_QUEUE,
            self::FIFO_SUFFIX
        );
        $options = [
            'is_fifo' => true,
            'transport_name' => self::TEST_TRANSPORT,
        ];

        $transport = $this->factory->createTransport($dsn, $options, $this->transport_serializer);
        $this->assertInstanceOf(SnsQsTransport::class, $transport);
    }

    public function invalidConfigurationProvider(): array
    {
        return [
            'invalid_fifo_configuration' => [
                'dsn' => sprintf('sns+sqs://topic=%s&queue=%s', self::TEST_TOPIC, self::TEST_QUEUE),
                'options' => [
                    'is_fifo' => true,
                    'transport_name' => self::TEST_TRANSPORT,
                ],
                'message' => 'Queue name must end with ".fifo" when is_fifo is true.',
            ],
            'missing_queue_and_topic' => [
                'dsn' => 'sns+sqs://',
                'options' => [],
                'message' => 'At least one queue or topic must be specified via DSN or options.',
            ],
        ];
    }

    /** @dataProvider invalidConfigurationProvider */
    public function test_create_transport_with_invalid_configurations(
        string $dsn,
        array $options,
        string $expected_message
    ): void {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage($expected_message);

        $this->factory->createTransport($dsn, $options, $this->transport_serializer);
    }

    public function test_create_transport_with_basic_configuration(): void
    {
        $dsn = sprintf('sns+sqs://topic=%s&queue=%s', self::TEST_TOPIC, self::TEST_QUEUE);
        $options = [
            'transport_name' => self::TEST_TRANSPORT,
            'is_fifo' => false,
        ];

        $transport = $this->factory->createTransport($dsn, $options, $this->transport_serializer);
        $this->assertInstanceOf(SnsQsTransport::class, $transport);
    }
}
