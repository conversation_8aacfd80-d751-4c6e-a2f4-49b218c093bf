<?php

namespace PHPUnit\Unit\App\Sql\Query\Operator;

use App\Sql\Query\Operator\BetweenQueryOperator;
use App\Sql\Query\Where\WhereQueryBuilder;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class BetweenQueryOperatorTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of BetweenQueryOperator. */
    protected function getTestedInstance(): BetweenQueryOperator
    {
        $wqb = self::$container->get(WhereQueryBuilder::class);
        $instance = self::$container->get(BetweenQueryOperator::class);
        $instance->setWhereQueryBuilder($wqb);

        return $instance;
    }

    /** Tests the handle method with various inputs. */
    public function test_handle(): void
    {
        // Test with standard parameters
        $result = $this->getTestedInstance()->handle('test', '_between', ['2019', '2012']);
        $this->assertSame('test BETWEEN :test_0 AND :test_1', $result);

        // Test with custom parameter name
        $result = $this->getTestedInstance()->handle('test', '_between', ['2019', '2012'], 'custom_parameter_name');
        $this->assertSame('test BETWEEN :custom_parameter_name_0 AND :custom_parameter_name_1', $result);

        // Test with invalid value (not an array)
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('The BETWEEN query operator only accepts an array as value.');
        $this->getTestedInstance()->handle('test', '_between', '2019');
    }

    /** Tests the handle method with too many parameters. */
    public function test_handle_with_too_many_parameters(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('The BETWEEN query operator should have strictly 2 parameters, "3" given');
        $this->getTestedInstance()->handle('test', '_between', ['2019', '2012', '2011']);
    }
}
