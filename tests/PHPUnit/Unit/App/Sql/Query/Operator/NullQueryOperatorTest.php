<?php

namespace PHPUnit\Unit\App\Sql\Query\Operator;

use App\Sql\Query\Operator\NullQueryOperator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class NullQueryOperatorTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of NullQueryOperator. */
    protected function getTestedInstance(): NullQueryOperator
    {
        return self::$container->get(NullQueryOperator::class);
    }

    /** Tests the handle method with various inputs. */
    public function test_handle(): void
    {
        // Test with true value (IS NULL)
        $result = $this->getTestedInstance()->handle('test', '_null', true);
        $this->assertSame('test IS NULL', $result);

        // Test with 1 value (IS NULL)
        $result = $this->getTestedInstance()->handle('test1', '_null', 1);
        $this->assertSame('test1 IS NULL', $result);

        // Test with false value (IS NOT NULL)
        $result = $this->getTestedInstance()->handle('test2', '_null', false);
        $this->assertSame('test2 IS NOT NULL', $result);

        // Test with 0 value (IS NOT NULL)
        $result = $this->getTestedInstance()->handle('test3', '_null', 0);
        $this->assertSame('test3 IS NOT NULL', $result);

        // Test with custom parameter name
        $result = $this->getTestedInstance()->handle('test4', '_null', 0, 'custom_parameter_name');
        $this->assertSame('test4 IS NOT NULL', $result);
    }

    /** Tests the handle method with invalid input. */
    public function test_handle_with_invalid_input(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage(
            'The NULL query operator only accepts booleans or the integers 0 and 1, "2" given'
        );
        $this->getTestedInstance()->handle('test', '_null', 2);
    }
}
