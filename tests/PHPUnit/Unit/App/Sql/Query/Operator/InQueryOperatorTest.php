<?php

namespace PHPUnit\Unit\App\Sql\Query\Operator;

use App\Sql\Query\Operator\InQueryOperator;
use App\Sql\Query\Where\WhereQueryBuilder;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class InQueryOperatorTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of InQueryOperator. */
    protected function getTestedInstance(): InQueryOperator
    {
        $wqb = self::$container->get(WhereQueryBuilder::class);
        $instance = self::$container->get(InQueryOperator::class);
        $instance->setWhereQueryBuilder($wqb);

        return $instance;
    }

    /** Tests the handle method with various inputs. */
    public function test_handle(): void
    {
        // Test with IN operator
        $result = $this->getTestedInstance()->handle('test', '_in', ['2019', '2012', '2011']);
        $this->assertSame('test IN (:test_0, :test_1, :test_2)', $result);

        // Test with NOT IN operator
        $result = $this->getTestedInstance()->handle('test', '_nin', ['2019', '2012', '2011']);
        $this->assertSame('test NOT IN (:test_3, :test_4, :test_5)', $result);

        // Test with custom parameter name
        $result = $this->getTestedInstance()->handle('test', '_nin', ['2019', '2012', '2011'], 'custom_parameter_name');
        $this->assertSame(
            'test NOT IN (:custom_parameter_name_0, :custom_parameter_name_1, :custom_parameter_name_2)',
            $result
        );

        // Test with invalid value (not an array)
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('The IN query operator only accepts an array as value.');
        $this->getTestedInstance()->handle('test', '_in', '2019');
    }
}
