<?php

namespace PHPUnit\Unit\App\Sql\Query\Where;

use App\Sql\Query\Where\WhereQueryBuilder;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class WhereQueryBuilderTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of WhereQueryBuilder. */
    protected function getTestedInstance(): WhereQueryBuilder
    {
        return self::$container->get(WhereQueryBuilder::class);
    }

    /** Tests the build method. */
    public function test_build(): void
    {
        // Test simple build
        $result = $this->getTestedInstance()->build([
            'test' => [
                '_eq' => 'value',
            ],
            'test2' => [
                '_eq' => 'value2',
            ],
        ]);

        $this->assertStringContainsString('test = :test', $result);
        $this->assertStringContainsString('AND test2 = :test2', $result);

        // Test with OR condition
        $result = $this->getTestedInstance()->build([
            '_or' => [
                'test3' => [
                    '_eq' => 'value',
                ],
                'test4' => [
                    '_eq' => 'value',
                ],
            ],
            'test5' => [
                '_eq' => 'value2',
            ],
        ]);

        $this->assertStringContainsString(
            '(test3 = :test3_0 OR test4 = :test4_0) AND test5 = :test5_0',
            str_replace("\n", '', $result)
        );
    }

    /** Tests the parse method. */
    public function test_parse(): void
    {
        $result = $this->getTestedInstance()->parse([
            'test' => [
                '_eq' => 'value',
            ],
            'test2' => [
                '_eq' => 'value2',
            ],
        ]);

        $this->assertCount(2, $result);
    }

    /** Tests the handle method. */
    public function test_handle(): void
    {
        $result = $this->getTestedInstance()->handle(1, [
            'test' => ['_eq' => 'value'],
        ]);

        $this->assertEquals('test = :test_0', $result);
        $this->assertEquals(['test_0' => 'value'], $this->getTestedInstance()->getBindedParameters());
    }

    /** Tests the handle method with invalid key. */
    public function test_handle_with_invalid_key(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Cannot bind key "test" as no handler was specified.');

        $this->getTestedInstance()->handle('test', 'whatever');
    }

    /** Tests the handle method with multiple handlers. */
    public function test_handle_with_multiple_handlers(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Key "nein" should contain exactly one handler, "2" were given.');

        $this->getTestedInstance()->handle('nein', [[1], [2]]);
    }

    /** Tests the handle method with multiple bind values. */
    public function test_handle_with_multiple_bind_values(): void
    {
        $result = $this->getTestedInstance()->handle(1, [
            '_or' => [['test' => ['_eq' => 'value']], ['test' => ['_eq' => 'other_value']]],
        ]);

        $this->assertEquals('(test = :test_0 OR test = :test_1)', $result);
        $this->assertEquals(
            ['test_0' => 'value', 'test_1' => 'other_value'],
            $this->getTestedInstance()->getBindedParameters()
        );
    }
}
