<?php

namespace PHPUnit\Unit\App\Sql\Helper;

use App\Sql\Helper\Pager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PagerTest extends KernelTestCase
{
    /** Tests the default values of the class. */
    public function test_defaults(): void
    {
        $pager = new Pager();

        $this->assertEquals(50, $pager->getLimit());
        $this->assertEquals(0, $pager->getOffset());
        $this->assertEmpty($pager->getResults());
        $this->assertEquals(
            [
                'from' => 0,
                'to' => 0,
                'total' => 0,
                'page' => 1,
                'limit' => 50,
                'last_page' => 1,
            ],
            $pager->getPagination()
        );

        // Test JSON serialization
        $this->assertEquals(
            json_encode($pager->getPagination(), JSON_THROW_ON_ERROR),
            json_encode($pager, JSON_THROW_ON_ERROR)
        );
    }

    /** Tests the computing methods of the class. */
    public function test_computing(): void
    {
        // Test constructor with page, limit and total
        $pager = new Pager(3, 20, 110);

        $this->assertEquals(20, $pager->getLimit());
        $this->assertEquals(40, $pager->getOffset());
        $this->assertEquals(
            [
                'from' => 41,
                'to' => 60,
                'total' => 110,
                'page' => 3,
                'limit' => 20,
                'last_page' => 6,
            ],
            $pager->getPagination()
        );

        // Test pagination on last page
        $pager = new Pager(6, 20, 110);

        $this->assertEquals(20, $pager->getLimit());
        $this->assertEquals(100, $pager->getOffset());
        $this->assertEquals(
            [
                'from' => 101,
                'to' => 110,
                'total' => 110,
                'page' => 6,
                'limit' => 20,
                'last_page' => 6,
            ],
            $pager->getPagination()
        );

        // Test using max between number of results or total as total of items
        $pager = new Pager(1, 20);
        $pager->setResults([1, 2, 3]);

        $this->assertEquals(20, $pager->getLimit());
        $this->assertEquals(0, $pager->getOffset());
        $this->assertEquals(
            [
                'from' => 1,
                'to' => 3,
                'total' => 3,
                'page' => 1,
                'limit' => 20,
                'last_page' => 1,
            ],
            $pager->getPagination()
        );

        // Test setting total
        $pager->setTotal(100);

        $this->assertEquals(
            [
                'from' => 1,
                'to' => 20,
                'total' => 100,
                'page' => 1,
                'limit' => 20,
                'last_page' => 5,
            ],
            $pager->getPagination()
        );
    }

    /** Tests the exceptions thrown by the class. */
    public function test_exceptions(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('page must be greater than 0');

        new Pager(0);
    }
}
