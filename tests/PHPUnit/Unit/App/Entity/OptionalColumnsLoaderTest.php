<?php

namespace PHPUnit\Unit\App\Entity;

use App\Entity\OptionalColumnDefinition;
use App\Entity\OptionalColumnsLoader;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class OptionalColumnsLoaderTest extends KernelTestCase
{
    public const ORIGINAL_COLLECTION = [
        [
            'id' => 1,
            'internal_id' => 3,
            'mapped' => null,
        ],
        [
            'id' => 2,
            'internal_id' => 4,
            'mapped' => null,
        ],
        [
            'id' => 5,
            'internal_id' => 6,
            'mapped' => null,
        ],
    ];

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of OptionalColumnsLoader. */
    protected function getTestedInstance(): OptionalColumnsLoader
    {
        return self::$container->get(OptionalColumnsLoader::class);
    }

    /** Tests mapping a collection with a global pivot. */
    public function test_map_collection_with_global_pivot(): void
    {
        // Given
        $instance = $this->getTestedInstance();
        $instance->addColumn(
            new OptionalColumnDefinition('mapped', function (): array {
                return $this::getGlobalCollection();
            })
        );

        // When - Map nothing when no included dependencies are specified
        $result = $instance->load(static::ORIGINAL_COLLECTION, 'id', []);

        // Then
        $this->assertCount(3, $result);
        $this->assertNull($result[0]['mapped']);
        $this->assertNull($result[1]['mapped']);
        $this->assertNull($result[2]['mapped']);

        // When - Map a collection successfully with the global pivot
        $result = $instance->load(static::ORIGINAL_COLLECTION, 'id', ['mapped']);

        // Then
        $this->assertCount(3, $result);
        $this->assertCount(2, $result[0]['mapped']);

        $uniqueIds = array_unique(array_column($result[0]['mapped'], 'id'));
        $this->assertCount(1, $uniqueIds);
        $this->assertContains(1, $uniqueIds);

        $uniqueDummies = array_unique(array_column($result[0]['mapped'], 'dummy'));
        $this->assertCount(2, $uniqueDummies);
        $this->assertContains('foo', $uniqueDummies);
        $this->assertContains('bar', $uniqueDummies);

        $this->assertCount(1, $result[1]['mapped']);

        $uniqueIds = array_unique(array_column($result[1]['mapped'], 'id'));
        $this->assertCount(1, $uniqueIds);
        $this->assertContains(2, $uniqueIds);

        $uniqueDummies = array_unique(array_column($result[1]['mapped'], 'dummy'));
        $this->assertCount(1, $uniqueDummies);
        $this->assertContains('baz', $uniqueDummies);

        $this->assertCount(0, $result[2]['mapped']);
    }

    /** Tests mapping a collection as an object with a global pivot. */
    public function test_map_collection_as_object_with_global_pivot(): void
    {
        // Given
        $instance = $this->getTestedInstance();
        $instance->addColumn(
            (new OptionalColumnDefinition('mapped', function (): array {
                return $this::getGlobalCollection();
            }))->asObject()
        );

        // When - Map nothing when no included dependencies are specified
        $result = $instance->load(static::ORIGINAL_COLLECTION, 'id', []);

        // Then
        $this->assertCount(3, $result);
        $this->assertNull($result[0]['mapped']);
        $this->assertNull($result[1]['mapped']);
        $this->assertNull($result[2]['mapped']);

        // When - Map a collection as object successfully with the global pivot
        $result = $instance->load(static::ORIGINAL_COLLECTION, 'id', ['mapped']);

        // Then
        $this->assertCount(3, $result);
        $this->assertCount(2, $result[0]['mapped']);
        $this->assertEquals(1, $result[0]['mapped']['id']);
        $this->assertEquals('bar', $result[0]['mapped']['dummy']);

        $this->assertCount(2, $result[1]['mapped']);
        $this->assertEquals(2, $result[1]['mapped']['id']);
        $this->assertEquals('baz', $result[1]['mapped']['dummy']);

        $this->assertCount(0, $result[2]['mapped']);
    }

    /** Tests mapping a collection with an internal pivot. */
    public function test_map_collection_with_internal_pivot(): void
    {
        // Given
        $instance = $this->getTestedInstance();
        $instance->addColumn(
            (new OptionalColumnDefinition('mapped', function (): array {
                return $this::getInternalCollection();
            }))->withInternalPivot('internal_id')
        );

        // When - Map nothing when no included dependencies are specified
        $result = $instance->load(static::ORIGINAL_COLLECTION, 'id', []);

        // Then
        $this->assertCount(3, $result);
        $this->assertNull($result[0]['mapped']);
        $this->assertNull($result[1]['mapped']);
        $this->assertNull($result[2]['mapped']);

        // When - Map a collection successfully with the internal pivot
        $result = $instance->load(static::ORIGINAL_COLLECTION, 'id', ['mapped']);

        // Then
        $this->assertCount(3, $result);
        $this->assertCount(0, $result[0]['mapped']);
        $this->assertCount(1, $result[1]['mapped']);

        $uniqueInternalIds = array_unique(array_column($result[1]['mapped'], 'internal_id'));
        $this->assertCount(1, $uniqueInternalIds);
        $this->assertContains(4, $uniqueInternalIds);

        $uniqueKeys = array_unique(array_column($result[1]['mapped'], 'key'));
        $this->assertCount(1, $uniqueKeys);
        $this->assertContains('alea', $uniqueKeys);

        $this->assertCount(2, $result[2]['mapped']);

        $uniqueInternalIds = array_unique(array_column($result[2]['mapped'], 'internal_id'));
        $this->assertCount(1, $uniqueInternalIds);
        $this->assertContains(6, $uniqueInternalIds);

        $uniqueKeys = array_unique(array_column($result[2]['mapped'], 'key'));
        $this->assertCount(2, $uniqueKeys);
        $this->assertContains('jacta', $uniqueKeys);
        $this->assertContains('est', $uniqueKeys);
    }

    /** Tests mapping a collection as an object with an internal pivot. */
    public function test_map_collection_as_object_with_internal_pivot(): void
    {
        // Given
        $instance = $this->getTestedInstance();
        $instance->addColumn(
            (new OptionalColumnDefinition('mapped', function (): array {
                return $this::getInternalCollection();
            }))
                ->asObject()
                ->withInternalPivot('internal_id')
        );

        // When - Map nothing when no included dependencies are specified
        $result = $instance->load(static::ORIGINAL_COLLECTION, 'id', []);

        // Then
        $this->assertCount(3, $result);
        $this->assertNull($result[0]['mapped']);
        $this->assertNull($result[1]['mapped']);
        $this->assertNull($result[2]['mapped']);

        // When - Map a collection as object successfully with the internal pivot
        $result = $instance->load(static::ORIGINAL_COLLECTION, 'id', ['mapped']);

        // Then
        $this->assertCount(3, $result);
        $this->assertCount(0, $result[0]['mapped']);
        $this->assertCount(2, $result[1]['mapped']);
        $this->assertEquals(4, $result[1]['mapped']['internal_id']);
        $this->assertEquals('alea', $result[1]['mapped']['key']);

        $this->assertCount(2, $result[2]['mapped']);
        $this->assertEquals(6, $result[2]['mapped']['internal_id']);
        $this->assertEquals('jacta', $result[2]['mapped']['key']);
    }

    /** Returns a collection for global pivot testing. */
    public static function getGlobalCollection(): array
    {
        return [
            [
                'id' => 1,
                'dummy' => 'foo',
            ],
            [
                'id' => 1,
                'dummy' => 'bar',
            ],
            [
                'id' => 2,
                'dummy' => 'baz',
            ],
        ];
    }

    /** Returns a collection for internal pivot testing. */
    public static function getInternalCollection(): array
    {
        return [
            [
                'internal_id' => 4,
                'key' => 'alea',
            ],
            [
                'internal_id' => 6,
                'key' => 'jacta',
            ],
            [
                'internal_id' => 6,
                'key' => 'est',
            ],
        ];
    }
}
