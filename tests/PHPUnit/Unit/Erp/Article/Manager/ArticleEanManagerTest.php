<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Manager\ArticleEanManager;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ArticleEanManagerTest extends KernelTestCase
{
    private const GOOD_EANS = ['***********', '************', '*************', '************', '***********'];
    private const BAD_EANS = [
        // incorrect length but good crc
        '********',
        '00000********',
        '**************',
        // correct length but bad crc
        '***********',
        '************',
        '************',
        '************',
        '***********',
    ];

    private const DUPLICATE_EANS = ['************', '*************'];

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/put_article.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticleEanManager. */
    protected function getTestedInstance(): ArticleEanManager
    {
        return self::$container->get(ArticleEanManager::class);
    }

    /** Tests the DTO validation. */
    public function test_dto_validation(): void
    {
        $article_ean_manager = $this->getTestedInstance();
        $validator = self::$container->get(ValidatorInterface::class);

        // Bad eans fails validation
        $dto = $article_ean_manager->createDto(13895, self::BAD_EANS);
        $error_list = $validator->validate($dto);
        $errors = ConstraintMessageFormatter::extract($error_list);

        $this->assertCount(count(self::BAD_EANS), $errors);
        $this->assertStringContainsString('[key:ean_invalid]', $errors['eans[0]']);
        $this->assertStringContainsString('[key:ean_invalid]', $errors['eans[6]']);

        // Duplicate eans fails validation
        $dto = $article_ean_manager->createDto(13895, self::DUPLICATE_EANS);
        $error_list = $validator->validate($dto);
        $errors = ConstraintMessageFormatter::extract($error_list);

        $this->assertCount(count(self::DUPLICATE_EANS), $errors);
        $this->assertStringContainsString('[key:ean_duplicate]', $errors['eans[0]']);

        // Good eans pass validation
        $dto = $article_ean_manager->createDto(13895, self::GOOD_EANS);
        $error_list = $validator->validate($dto);
        $errors = ConstraintMessageFormatter::extract($error_list);

        $this->assertCount(0, $errors);
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        $article_ean_manager = $this->getTestedInstance();
        $users = $this->getUsers();

        // Valid data update system_events
        $old_dto = $article_ean_manager->createDto(13895, ['***********', '************']);
        $article_ean_manager->update($old_dto, $users['gege']);

        $new_dto = $article_ean_manager->createDto(13895, ['***********', '***********']);
        $updated = $article_ean_manager->update($new_dto, $users['gege']);

        $this->assertEquals(2, $updated);

        $system_event = $this->fetchLastSystemEvents();
        $this->assertIsArray($system_event);
        $this->assertEquals('article.update.eans', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 13895}, "data": {"eans": {"added": {"1": "00***********"}, "deleted": {"1": "0************"}}}, "meta": {"updated_by": {"user_id": 2, "lastname": "Manvussa", "username": "gege", "firstname": "Gérard"}}}',
            $system_event['payload']
        );

        $eans = $this->fetchArticleEans(13895);
        $this->assertCount(2, $eans);
        $this->assertEquals('00***********', $eans[0]['ean']);
        $this->assertEquals('00***********', $eans[1]['ean']);

        // Identical data updates nothing
        $old_dto = $article_ean_manager->createDto(13895, ['***********', '************']);
        $article_ean_manager->update($old_dto, $users['gege']);

        $system_event = $this->fetchLastSystemEvents();
        $id = $system_event['event_id'];

        $new_dto = $article_ean_manager->createDto(13895, ['***********', '************']);
        $updated = $article_ean_manager->update($new_dto, $users['gege']);

        $this->assertEquals(0, $updated);

        $system_event = $this->fetchLastSystemEvents();
        $this->assertEquals($id, $system_event['event_id']);

        $eans = $this->fetchArticleEans(13895);
        $this->assertCount(2, $eans);
        $this->assertEquals('00***********', $eans[0]['ean']);
        $this->assertEquals('0************', $eans[1]['ean']);
    }

    /** Gets the users for testing. */
    private function getUsers(): array
    {
        $users = [];
        foreach (['admin', 'gege'] as $user_name) {
            $user = self::$container->get(AccountQueryRepository::class)->getUser($user_name);

            $user_array = $user->toArray();
            $user_array['username'] = '9e81bd23-e7ac-4ba3-842f-8da6554bc540';
            $users[$user_name] = $user->fromArray($user_array);
        }

        return $users;
    }

    /** Fetches the last system events. */
    private function fetchLastSystemEvents(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql);
    }

    /** Fetches article EANs by article ID. */
    private function fetchArticleEans(int $article_id): array
    {
        $sql = <<<SQL
        SELECT bcpae.ean AS ean
        FROM backOffice.article a
            LEFT JOIN backOffice.BO_CTG_PDT_ART_ean bcpae ON a.id_produit = bcpae.BO_CTG_PDT_ART_article_id
        WHERE a.id_produit = :article_id
        SQL;

        return $this->getPdo()->fetchAll($sql, ['article_id' => $article_id]);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
