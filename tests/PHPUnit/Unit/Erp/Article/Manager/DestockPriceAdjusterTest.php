<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Manager\ArticleEventLogger;
use SonVideo\Erp\Article\Manager\ArticleSalesChannelManager;
use SonVideo\Erp\Article\Manager\DestockPriceAdjuster;
use SonVideo\Erp\Article\Mysql\Repository\ArticleWeightedCostAdjustmentRepository;
use SonVideo\Erp\Filesystem\Manager\ExportedFile;
use SonVideo\Erp\Mailing\Manager\Internal\InternalEmailDispatcher;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class DestockPriceAdjusterTest extends KernelTestCase
{
    public const PUBLISHED_ARTICLES_ID = [13895, 81078, 81123];

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/destock_price_adjuster.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of DestockPriceAdjuster. */
    protected function getTestedInstance(): DestockPriceAdjuster
    {
        $email_dispatcher_mock = $this->createMock(InternalEmailDispatcher::class);
        $email_dispatcher_mock->method('dispatch')->willReturn(null);

        return new DestockPriceAdjuster(
            self::$container->get(ArticleWeightedCostAdjustmentRepository::class),
            self::$container->get(SerializerInterface::class),
            self::$container->get(AccountQueryRepository::class),
            self::$container->get(ArticleEventLogger::class),
            self::$container->get(ExportedFile::class),
            $email_dispatcher_mock,
            self::$container->get(ArticleSalesChannelManager::class),
            'test'
        );
    }

    /** Tests the adjust method. */
    public function test_adjust(): void
    {
        $old_articles_price = $this->fetchArticleSellingPrice();

        $result = $this->getTestedInstance()->adjust();

        $this->assertEquals(3, $result);

        // Only the two published products have changed price
        $new_articles_price = $this->fetchArticleSellingPrice();
        foreach ($new_articles_price as $new_article_price) {
            if (in_array($new_article_price['id_produit'], self::PUBLISHED_ARTICLES_ID)) {
                $this->assertNotEquals(
                    $old_articles_price[$new_article_price['id_produit']]['prix_vente'],
                    (float) $new_article_price['prix_vente']
                );
            } else {
                $this->assertEquals(
                    $old_articles_price[$new_article_price['id_produit']]['prix_vente'],
                    (float) $new_article_price['prix_vente']
                );
            }
        }

        // Check adjustment
        $adjustments = $this->fetchAdjustment();
        $this->assertCount(3, $adjustments);
        $this->assertEquals(13895, $adjustments[0]['article_id']);
        $this->assertEquals('devaluation', $adjustments[0]['type']);
        $this->assertEquals(13.4, (float) $adjustments[0]['amount']);
        $this->assertEquals(UserEntity::SYSTEM_ID, $adjustments[0]['created_by']);
        $this->assertEquals(81078, $adjustments[1]['article_id']);
        $this->assertEquals('devaluation', $adjustments[1]['type']);
        $this->assertEquals(6.7, (float) $adjustments[1]['amount']);
        $this->assertEquals(UserEntity::SYSTEM_ID, $adjustments[1]['created_by']);

        foreach (self::PUBLISHED_ARTICLES_ID as $article_id) {
            $events = $this->fetchSystemEventsForArticle($article_id);

            // other events are side effects of the adjustment
            $this->assertContains('article.create.weighted_cost_adjustment', array_column($events, 'name'));
        }

        // Test 4 eligible products but 0 are published
        $result = $this->getTestedInstance()->adjust();
        $this->assertEquals(0, $result);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches article selling prices. */
    private function fetchArticleSellingPrice(): array
    {
        $sql = <<<SQL
            SELECT id_produit, prix_vente
            FROM backOffice.article
        SQL;

        return $this->getPdo()->fetchAssoc($sql);
    }

    /** Fetches system events for an article. */
    private function fetchSystemEventsForArticle($article_id): array
    {
        $sql = <<<SQL
        SELECT name, payload
        FROM backOffice.system_event
        WHERE main_id = :article_id
        ORDER BY event_id
        SQL;

        return $this->getPdo()->fetchAll($sql, ['article_id' => $article_id]);
    }

    /** Fetches adjustments. */
    private function fetchAdjustment(): array
    {
        $sql = <<<SQL
        SELECT article_id, type, amount, created_by
        FROM backOffice.BO_STK_weighted_cost_adjustment
        ORDER BY id
        SQL;

        return $this->getPdo()->fetchAll($sql);
    }
}
