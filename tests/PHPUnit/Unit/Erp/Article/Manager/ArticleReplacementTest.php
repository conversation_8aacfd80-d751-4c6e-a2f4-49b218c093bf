<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use SonVideo\Erp\Article\Contract\ArticleReplacementCommandInterface;
use SonVideo\Erp\Article\Dto\ArticleReplacementCommandResultDto;
use SonVideo\Erp\Article\Manager\ArticleReplacement;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleReplacementTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticleReplacement. */
    protected function getTestedInstance(SystemEventLogger $mocked_logger): ArticleReplacement
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        $container = self::$container;

        $mocked_repo = new class() implements ArticleReplacementCommandInterface {
            public function save(
                string $replaced_id_or_sku,
                string $replaced_by_id_or_sku
            ): ArticleReplacementCommandResultDto {
                $dto = new ArticleReplacementCommandResultDto();
                $dto->id_replaced = 1;
                $dto->id_replaced_by = 2;

                return $dto;
            }

            public function remove(string $replaced_id_or_sku): ArticleReplacementCommandResultDto
            {
                $dto = new ArticleReplacementCommandResultDto();
                $dto->id_replaced = 3;
                $dto->id_replaced_by = 4;

                return $dto;
            }
        };

        return new ArticleReplacement($container->get(CurrentUser::class), $mocked_repo, $mocked_logger);
    }

    /** Creates a mocked logger for testing. */
    private function getMockedLogger(): SystemEventLogger
    {
        return new class() extends SystemEventLogger {
            public function __construct()
            {
            }

            /** @var LoggableSystemEvent[] */
            public $logs = [];

            public function log(?LoggableSystemEvent $loggable_system_event): void
            {
                $this->logs[] = $loggable_system_event;
            }
        };
    }

    /** Tests the logged events when saving. */
    public function test_logged_event_when_saving(): void
    {
        $mocked_logger = $this->getMockedLogger();
        $this->getTestedInstance($mocked_logger)->save('IRRELEVANT', 'IRRELEVANT_BY');

        $current_user = self::$container->get(CurrentUser::class)->entity();

        $events = $mocked_logger->logs;

        // First event
        $this->assertEquals('article.create.replacement', $events[0]->getName());
        $this->assertEquals(
            [
                '_rel' => ['article' => 1],
                'data' => [
                    'replaced_by' => 2,
                ],
                'meta' => [
                    'created_by' => [
                        'user_id' => $current_user->id_utilisateur,
                        'username' => $current_user->utilisateur,
                        'firstname' => $current_user->prenom,
                        'lastname' => $current_user->nom,
                    ],
                ],
            ],
            $events[0]->getPayload()
        );

        // Second event
        $this->assertEquals('article.create.replacement', $events[1]->getName());
        $this->assertEquals(
            [
                '_rel' => ['article' => 2],
                'data' => [
                    'replace' => 1,
                ],
                'meta' => [
                    'created_by' => [
                        'user_id' => $current_user->id_utilisateur,
                        'username' => $current_user->utilisateur,
                        'firstname' => $current_user->prenom,
                        'lastname' => $current_user->nom,
                    ],
                ],
            ],
            $events[1]->getPayload()
        );
    }

    /** Tests the logged events when removing. */
    public function test_logged_event_when_removing(): void
    {
        $mocked_logger = $this->getMockedLogger();
        $this->getTestedInstance($mocked_logger)->remove('IRRELEVANT');

        $current_user = self::$container->get(CurrentUser::class)->entity();

        $events = $mocked_logger->logs;

        // First event
        $this->assertEquals('article.delete.replacement', $events[0]->getName());
        $this->assertEquals(
            [
                '_rel' => ['article' => 3],
                'data' => [
                    'replaced_by' => 4,
                ],
                'meta' => [
                    'deleted_by' => [
                        'user_id' => $current_user->id_utilisateur,
                        'username' => $current_user->utilisateur,
                        'firstname' => $current_user->prenom,
                        'lastname' => $current_user->nom,
                    ],
                ],
            ],
            $events[0]->getPayload()
        );

        // Second event
        $this->assertEquals('article.delete.replacement', $events[1]->getName());
        $this->assertEquals(
            [
                '_rel' => ['article' => 4],
                'data' => [
                    'replace' => 3,
                ],
                'meta' => [
                    'deleted_by' => [
                        'user_id' => $current_user->id_utilisateur,
                        'username' => $current_user->utilisateur,
                        'firstname' => $current_user->prenom,
                        'lastname' => $current_user->nom,
                    ],
                ],
            ],
            $events[1]->getPayload()
        );
    }
}
