<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleLogisticHavreUpdateContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleSellingPlatformsUpdateContextDto;
use SonVideo\Erp\Article\Manager\ArticleManager;
use SonVideo\Erp\Referential\ArticleStatus;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ArticleManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/put_article.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticleManager. */
    protected function getTestedInstance(): ArticleManager
    {
        return self::$container->get(ArticleManager::class);
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        $article_manager = $this->getTestedInstance();
        $users = $this->getUsers();

        // Unknown article throw exception
        $dtos = $article_manager->createDtos(123456, 'status', ['status' => ArticleStatus::OUI]);
        $user = $users['admin'];

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessageMatches('/No article found with id 123456./');
        $article_manager->update($user, 'status', $dtos);
    }

    /** Tests the update method with valid data. */
    public function test_update_with_valid_data(): void
    {
        $article_manager = $this->getTestedInstance();
        $users = $this->getUsers();

        // Valid data update system_events
        $old_dtos = $article_manager->createDtos(81078, 'status', ['status' => ArticleStatus::TODO]);
        $article_manager->update($users['gege'], 'status', $old_dtos);

        $new_dtos = $article_manager->createDtos(81078, 'status', ['status' => ArticleStatus::OUI]);
        $updated = $article_manager->update($users['gege'], 'status', $new_dtos);

        $this->assertEquals(1, $updated);

        $system_event = $this->fetchLastSystemEvents();
        $this->assertIsArray($system_event);
        $this->assertEquals('article.update.status', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 81078}, "data": {"status": {"new": "oui", "old": "todo"}}, "meta": {"updated_by": {"user_id": 2, "lastname": "Manvussa", "username": "gege", "firstname": "Gérard"}}}',
            $system_event['payload']
        );
    }

    /** Tests the update method with status DTO. */
    public function test_update_with_status_dto(): void
    {
        $article_manager = $this->getTestedInstance();
        $validator = self::$container->get(ValidatorInterface::class);

        // Empty status fails validation
        $dtos = $article_manager->createDtos(81078, 'status', []);
        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(1, $errors);

        $tests = [
            [
                'product_id' => 81079,
                'expected_error' => '[key:unauthorized_for_destock]',
                'statuses' => [ArticleStatus::NON, ArticleStatus::OUI, ArticleStatus::TMP, ArticleStatus::A_VOIR],
            ],
            [
                'product_id' => 81080,
                'expected_error' => '[key:unauthorized_when_no_name]',
                'statuses' => [ArticleStatus::TODO, ArticleStatus::OUI, ArticleStatus::LAST, ArticleStatus::TMP],
            ],
            [
                'product_id' => 81081,
                'expected_error' => '[key:unauthorized_when_no_selling_price]',
                'statuses' => [ArticleStatus::TODO, ArticleStatus::OUI, ArticleStatus::LAST, ArticleStatus::TMP],
            ],
            [
                'product_id' => 81082,
                'expected_error' => '[key:unauthorized_when_no_purchase_price]',
                'statuses' => [ArticleStatus::TODO, ArticleStatus::OUI, ArticleStatus::LAST, ArticleStatus::TMP],
            ],
            [
                'product_id' => 81083,
                'expected_error' => '[key:unauthorized_when_retail_price_too_high]',
                'statuses' => [ArticleStatus::TODO, ArticleStatus::OUI, ArticleStatus::LAST, ArticleStatus::TMP],
            ],
            [
                'product_id' => 81084,
                'expected_error' => '[key:unauthorized_when_no_weight]',
                'statuses' => [ArticleStatus::TODO, ArticleStatus::OUI, ArticleStatus::LAST, ArticleStatus::TMP],
            ],
        ];

        foreach ($tests as $test) {
            foreach ($test['statuses'] as $status) {
                // Setting status on an article with specific error fails validation
                $dtos = $article_manager->createDtos($test['product_id'], 'status', ['status' => $status]);
                $this->assertCount(1, $dtos);

                $error_list = $validator->validate($dtos[0]);
                $errors = ConstraintMessageFormatter::extract($error_list);
                $this->assertArrayHasKey('status', $errors);
                $this->assertStringContainsString($test['expected_error'], $errors['status']);
            }
        }

        // Can update status
        foreach ([ArticleStatus::TODO, ArticleStatus::TMP, ArticleStatus::OUI] as $status) {
            $dtos = $article_manager->createDtos(81078, 'status', ['status' => $status]);
            $this->assertCount(1, $dtos);

            $error_list = $validator->validate($dtos[0]);
            $errors = ConstraintMessageFormatter::extract($error_list);
            $this->assertCount(0, $errors);
        }
    }

    /** Tests the update method with suppliers DTO. */
    public function test_update_with_suppliers_dto(): void
    {
        $article_manager = $this->getTestedInstance();
        $validator = self::$container->get(ValidatorInterface::class);

        // Setting a suppliers not related to the article brand fails validation
        $dtos = $article_manager->createDtos(81078, 'suppliers', [
            'supplier_id' => 1,
            'supplier_reference' => 'RBLINK2',
        ]);

        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertArrayHasKey('supplier_id', $errors);
        $this->assertStringContainsString('[key:brand_not_related_to_supplier]', $errors['supplier_id']);

        // Can update suppliers
        $dtos = $article_manager->createDtos(81078, 'suppliers', [
            'supplier_id' => 400,
            'supplier_reference' => 'RBLINK2',
        ]);

        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);
    }

    /** Tests the update method with supplier references DTO. */
    public function test_update_with_supplier_references_dto(): void
    {
        $article_manager = $this->getTestedInstance();
        $supplierReferencesToTest = [
            'RbLInoPHY7IIEnDUdnZ9ZNZDINzYBSP1IDZNny79trUNZD76DZ',
            '*RBLINK2',
            'RBLINK2&',
            '+',
            'IJDZ]',
        ];

        $supplierValidReferences = ['RBLINK2', 'PL3', 'IZ9DNIZZ5XNIAn', ''];

        $this->validateInvalidReferences(
            $article_manager,
            $supplierReferencesToTest,
            'Setting a supplier reference with invalid characters fails validation'
        );

        $this->validateValidReferences(
            $article_manager,
            $supplierValidReferences,
            'Setting a supplier reference with valid characters should pass validation'
        );
    }

    /** Tests the update method with prices DTO. */
    public function test_update_with_prices_dto(): void
    {
        $article_manager = $this->getTestedInstance();
        $validator = self::$container->get(ValidatorInterface::class);

        // Too expensive selling price fails validation
        $dtos = $article_manager->createDtos(81078, 'prices', [
            'selling_price' => 200.45,
            'pvgc' => 200.44,
            'ecotax' => 0,
            'sorecop' => 0,
            'tariff_tax_excluded' => 123,
            'initial_selling_price' => 0,
        ]);

        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertArrayHasKey('selling_price', $errors);
        $this->assertStringContainsString('[key:selling_price_too_high]', $errors['selling_price']);

        // Package selling price above packaged articles price fails validation
        $dtos = $article_manager->createDtos(13895, 'prices', [
            'selling_price' => 249.0 * 3 + 0.1,
            'pvgc' => 249.0 * 3 + 0.2,
            'ecotax' => 0,
            'sorecop' => 0,
            'tariff_tax_excluded' => 123,
            'initial_selling_price' => 0,
        ]);

        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertArrayHasKey('selling_price', $errors);
        $this->assertStringContainsString('[key:package_selling_price_too_high]', $errors['selling_price']);

        // Negative margin fails validation
        // weighted cost = 131.58 + vat
        $dtos = $article_manager->createDtos(81078, 'prices', [
            'selling_price' => 132,
            'pvgc' => 123.46,
            'ecotax' => 0,
            'sorecop' => 0,
            'tariff_tax_excluded' => 999,
            'initial_selling_price' => 0,
        ]);

        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertArrayHasKey('selling_price', $errors);
        $this->assertStringContainsString('[key:negative_margin]', $errors['selling_price']);

        // Not found initial selling price fails validation
        // weighted cost = 131.58 + vat
        $dtos = $article_manager->createDtos(13895, 'prices', [
            'selling_price' => 132,
            'pvgc' => 123.46,
            'ecotax' => 0,
            'sorecop' => 0,
            'tariff_tax_excluded' => 999,
            'initial_selling_price' => 44.44,
        ]);

        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertArrayHasKey('initial_selling_price', $errors);
        $this->assertStringContainsString('[key:not_sold_at_this_price]', $errors['initial_selling_price']);

        // Changing selling price of an on sale article fails validation
        $dtos = $article_manager->createDtos(81085, 'prices', [
            'selling_price' => 440.0,
            'pvgc' => 249.0,
            'ecotax' => 0,
            'sorecop' => 0,
            'tariff_tax_excluded' => 131.58,
            'initial_selling_price' => 0,
        ]);

        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertArrayHasKey('selling_price', $errors);
        $this->assertStringContainsString('[key:article_on_sale]', $errors['selling_price']);

        // Can update article prices
        $dtos = $article_manager->createDtos(81078, 'prices', [
            'selling_price' => 150,
            'pvgc' => 151,
            'ecotax' => 0,
            'sorecop' => 0,
            'tariff_tax_excluded' => 123,
            'initial_selling_price' => 0,
        ]);

        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);

        // Can update package prices
        $dtos = $article_manager->createDtos(13895, 'prices', [
            'selling_price' => 249.0 * 3 - 0.1,
            'pvgc' => 249.0 * 3,
            'ecotax' => 0,
            'sorecop' => 0,
            'tariff_tax_excluded' => 90,
            'initial_selling_price' => 55.55,
        ]);

        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);
    }

    /** Tests the update method with general information DTO. */
    public function test_update_with_general_information_dto(): void
    {
        $article_manager = $this->getTestedInstance();
        $validator = self::$container->get(ValidatorInterface::class);

        // Can update general information
        $dtos = $article_manager->createDtos(13895, 'general_information', [
            'subcategory_id' => 173,
            'basket_description' => 'Mercurochrome le pansement des héros ! Mercurochrome le pansement des héros ! Mercurochrome le pansement des héros ! Mercurochrome le pansement des héros ! Mercurochrome le pansement des héros ! Mercurochrome le pansement des héros ! ',
            'short_description' => 'Mercurochrome le pansement des héros ! ',
            'marketplace_description' => 'Les marketplaces : où trouver le parfait équilibre entre choix infini et confusion totale',
            'packages' => 2,
            'brand_id' => 989,
            'color_id' => 1,
            'model' => 'Pansement',
            'embargo_date' => '1977-01-30',
            'manufacturer_warranty_years' => 3,
        ]);

        $this->assertCount(2, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);

        $error_list = $validator->validate($dtos[1]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);
    }

    /** Tests the update method with selling platforms DTO. */
    public function test_update_with_selling_platforms_dto(): void
    {
        $article_manager = $this->getTestedInstance();
        $validator = self::$container->get(ValidatorInterface::class);

        // DTO is not valid when values are blank
        $dtos = $article_manager->createDtos(13895, 'selling_platforms', [
            'comparator' => '',
        ]);

        $this->assertCount(1, $dtos);
        $this->assertInstanceOf(ArticleSellingPlatformsUpdateContextDto::class, $dtos[0]);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(1, $errors);

        // DTO is created successfully
        $dtos = $article_manager->createDtos(13895, 'selling_platforms', [
            'comparator' => 'oui',
        ]);

        $this->assertCount(1, $dtos);
        $this->assertInstanceOf(ArticleSellingPlatformsUpdateContextDto::class, $dtos[0]);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);
    }

    /** Tests the update method with logistic information DTO. */
    public function test_update_with_logistic_information_dto(): void
    {
        $article_manager = $this->getTestedInstance();
        $validator = self::$container->get(ValidatorInterface::class);

        $valid_payload = [
            'weight' => 0.3,
            'package_unit' => 3,
            'weight_tmp' => 'Y',
            'is_packaged' => 'Y',
            'customs_code' => '*********',
            'source_country_id' => 19,
            'number_of_packages' => 2,
            'ecotax_code' => '09876',
        ];

        foreach (['todo' => 128418, 'oui' => 81078, 'last' => 128417, 'tmp' => 128416] as $status => $article_id) {
            // Setting weight to zero on an article with status fails validation
            $bad_payload = $valid_payload;
            $bad_payload['weight'] = 0.0;

            $dtos = $article_manager->createDtos($article_id, 'logistic_information', $bad_payload);
            $this->assertCount(1, $dtos);

            $error_list = $validator->validate($dtos[0]);
            $errors = ConstraintMessageFormatter::extract($error_list);
            $this->assertCount(1, $errors);
            $this->assertStringContainsString('[key:unauthorized_no_weight]', $errors['weight']);
        }

        foreach (['a', '0123456789123', ' '] as $customs_code) {
            // Setting customs code to invalid value fails validation
            $bad_payload = $valid_payload;
            $bad_payload['customs_code'] = $customs_code;

            $dtos = $article_manager->createDtos(81078, 'logistic_information', $bad_payload);
            $this->assertCount(1, $dtos);

            $error_list = $validator->validate($dtos[0]);
            $errors = ConstraintMessageFormatter::extract($error_list);
            $this->assertCount(1, $errors);
            $this->assertStringContainsString('[key:customs_code_format_error]', $errors['customs_code']);
        }

        // Can update logistic information
        $dtos = $article_manager->createDtos(81085, 'logistic_information', $valid_payload);
        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);

        // Can update logistic information with zero weight on an article with status "yapu"
        $payload = $valid_payload;
        $payload['weight'] = 0.0;

        $dtos = $article_manager->createDtos(81084, 'logistic_information', $payload);
        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);

        // Can update logistic information with a null customs code
        $payload = $valid_payload;
        $payload['customs_code'] = null;

        $dtos = $article_manager->createDtos(81085, 'logistic_information', $payload);
        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);

        // Can update logistic information with a null ecotax code
        $payload = $valid_payload;
        $payload['ecotax_code'] = null;

        $dtos = $article_manager->createDtos(81085, 'logistic_information', $payload);
        $this->assertCount(1, $dtos);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);
    }

    /** Tests the update method with logistic havre information DTO. */
    public function test_update_with_logistic_havre_information_dto(): void
    {
        $article_manager = $this->getTestedInstance();
        $validator = self::$container->get(ValidatorInterface::class);

        // DTO is created successfully
        $dtos = $article_manager->createDtos(13895, 'havre', [
            'sku_havre' => 'toto',
            'is_active_havre' => true,
            'package_unit_havre' => 1,
        ]);

        $this->assertCount(1, $dtos);
        $this->assertInstanceOf(ArticleLogisticHavreUpdateContextDto::class, $dtos[0]);

        $error_list = $validator->validate($dtos[0]);
        $errors = ConstraintMessageFormatter::extract($error_list);
        $this->assertCount(0, $errors);
    }

    /** Validates invalid references. */
    private function validateInvalidReferences(
        ArticleManager $article_manager,
        array $supplierReferences,
        string $assertionMessage
    ): void {
        $validator = self::$container->get(ValidatorInterface::class);

        foreach ($supplierReferences as $supplierReference) {
            $dtos = $article_manager->createDtos(81078, 'suppliers', [
                'supplier_id' => 400,
                'supplier_reference' => $supplierReference,
            ]);

            $this->assertCount(1, $dtos);

            $error_list = $validator->validate($dtos[0]);
            $errors = ConstraintMessageFormatter::extract($error_list);
            $this->assertArrayHasKey('supplier_reference', $errors);
            $this->assertStringContainsString('This value is not valid.', $errors['supplier_reference']);
        }
    }

    /** Validates valid references. */
    private function validateValidReferences(
        ArticleManager $article_manager,
        array $supplierReferences,
        string $assertionMessage
    ): void {
        $validator = self::$container->get(ValidatorInterface::class);

        foreach ($supplierReferences as $supplierReference) {
            $dtos = $article_manager->createDtos(81078, 'suppliers', [
                'supplier_id' => 400,
                'supplier_reference' => $supplierReference,
            ]);

            $this->assertCount(1, $dtos);

            $error_list = $validator->validate($dtos[0]);
            $errors = ConstraintMessageFormatter::extract($error_list);
            $this->assertCount(0, $errors);
        }
    }

    /** Gets the users for testing. */
    private function getUsers(): array
    {
        $users = [];
        foreach (['admin', 'gege'] as $user_name) {
            $user = self::$container->get(AccountQueryRepository::class)->getUser($user_name);

            $user_array = $user->toArray();
            $user_array['username'] = '9e81bd23-e7ac-4ba3-842f-8da6554bc540';
            $users[$user_name] = $user->fromArray($user_array);
        }

        return $users;
    }

    /** Fetches the last system events. */
    private function fetchLastSystemEvents(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
