<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Sql\LegacyReadonlyPdo;
use App\Sql\Query\QueryBuilder;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use Psr\Log\NullLogger;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Manager\ArticlePlannedPriceApplier;
use SonVideo\Erp\Article\Manager\ArticleSalesChannelManager;
use SonVideo\Erp\Article\Mysql\Repository\ArticlePlannedPriceRepository;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyProductRepository;
use SonVideo\Erp\Task\Entity\TaskEntity;
use SonVideo\Erp\Task\Mysql\Repository\TaskRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticlePlannedPriceApplierTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();

        MySqlDatabase::clearDatabases();
        PgDatabase::reloadFixtures();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/article_planned_price_applier.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticlePlannedPriceApplier. */
    protected function getTestedInstance(): ArticlePlannedPriceApplier
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        $tested_class = new ArticlePlannedPriceApplier(
            self::$container->get(ArticlePlannedPriceRepository::class),
            self::$container->get(SerializerInterface::class),
            self::$container->get(QueryBuilder::class),
            self::$container->get(TaskRepository::class),
            self::$container->get(AccountQueryRepository::class),
            self::$container->get(ArticleSalesChannelManager::class),
            self::$container->get(PricingStrategyProductRepository::class),
            self::$container->get(ArticleRepository::class)
        );

        $tested_class->setLogger(new NullLogger());

        $tested_class->setConnections($this->getPdo(), self::$container->get(LegacyReadonlyPdo::class));

        return $tested_class;
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests the update_article_price method. */
    public function test_update_article_price(): void
    {
        // Test update article price with cron
        $article_planned_prices_to_active = $this->fetchArticlePlannedPricesToActive();
        $article_planned_prices_to_deactivated = $this->fetchArticlePlannedPricesToDeactivated();

        $this->assertCount(3, $article_planned_prices_to_active);
        $this->assertEquals(1, $article_planned_prices_to_active[0]['article_planned_price_id']);
        $this->assertEquals(81078, $article_planned_prices_to_active[0]['product_id']);
        $this->assertEquals(4, $article_planned_prices_to_active[1]['article_planned_price_id']);
        $this->assertEquals(72216, $article_planned_prices_to_active[1]['product_id']);
        $this->assertEquals(6, $article_planned_prices_to_active[2]['article_planned_price_id']);
        $this->assertEquals(13895, $article_planned_prices_to_active[2]['product_id']);

        $this->assertCount(1, $article_planned_prices_to_deactivated);
        $this->assertEquals(5, $article_planned_prices_to_deactivated[0]['article_planned_price_id']);

        $this->getTestedInstance()->updateArticlePrices();

        // Activation
        $this->assertEquals('199.50', $this->fetchArticleSellingPrice(81078, 1)['selling_price']);
        $this->assertEquals('249.00', $this->fetchArticleSellingPrice(81078, 2)['selling_price']); // No change, product under strategy
        $this->assertEquals('199.50', $this->fetchArticleSellingPrice(81078, 3)['selling_price']);
        $this->assertEquals('249.00', $this->fetchArticleSellingPrice(81078, 4)['selling_price']); // No change, channel not configured
        $this->assertCount(0, $this->fetchTasksByType(TaskEntity::TYPE_ERROR_UPDATE_PRICE, 81078));

        $this->assertEquals('199.00', $this->fetchArticleSellingPrice(72216, 1)['selling_price']); // No change, product on sale
        $this->assertEquals('150.00', $this->fetchArticleSellingPrice(72216, 2)['selling_price']);
        $this->assertEquals('150.00', $this->fetchArticleSellingPrice(72216, 3)['selling_price']);
        $this->assertCount(0, $this->fetchTasksByType(TaskEntity::TYPE_ERROR_UPDATE_PRICE, 72216));

        $this->assertEquals('45.00', $this->fetchArticleSellingPrice(13895, 1)['selling_price']); // No change, price too low
        $task = $this->fetchTasksByType(TaskEntity::TYPE_ERROR_UPDATE_PRICE, 13895);
        $this->assertCount(1, $task);
        $this->assertEquals(
            "La mise à jour du prix de vente de l'article 13895 au prix de 1€ a échoué",
            $task[0]['description']
        );
        $this->assertEquals('13895', $task[0]['id_produit']);
        $this->assertEquals('1033', $task[0]['id_utilisateur']);

        // Deactivation
        $this->assertEquals('1000.00', $this->fetchArticleSellingPrice(118230, 1)['selling_price']);
        $this->assertEquals('899.00', $this->fetchArticleSellingPrice(118230, 2)['selling_price']); // No change, channel not configured
        $this->assertCount(0, $this->fetchTasksByType(TaskEntity::TYPE_ERROR_UPDATE_PRICE, 118230));

        $article_planned_prices_to_active = $this->fetchArticlePlannedPricesToActive();
        $article_planned_prices_to_deactivated = $this->fetchArticlePlannedPricesToDeactivated();

        $this->assertCount(0, $article_planned_prices_to_active);
        $this->assertCount(0, $article_planned_prices_to_deactivated);

        $system_event = $this->fetchSystemEventsByArticleId(81078);
        $this->assertCount(3, $system_event);

        $this->assertEquals('article.update.sales_channel_product.prices', $system_event[0]['name']);
        $this->assertEquals(
            ['old' => 249, 'new' => 199.5],
            json_decode($system_event[0]['payload'], true, 512, JSON_THROW_ON_ERROR)['data']['selling_price']
        );

        $this->assertEquals('article.update.selling_price', $system_event[1]['name']);
        $this->assertEquals(
            ['selling_price' => ['old' => 249, 'new' => 199.5]],
            json_decode($system_event[1]['payload'], true, 512, JSON_THROW_ON_ERROR)['data']
        );

        $this->assertEquals('article.update.sales_channel_product.prices', $system_event[2]['name']);
        $this->assertEquals(
            ['old' => 249, 'new' => 199.5],
            json_decode($system_event[2]['payload'], true, 512, JSON_THROW_ON_ERROR)['data']['selling_price']
        );
    }

    /** Fetches system events by article ID. */
    private function fetchSystemEventsByArticleId($main_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        WHERE main_id = :main_id
        ORDER BY event_id DESC
        SQL;

        return $this->getPdo()->fetchAll($sql, ['main_id' => $main_id]);
    }

    /** Fetches article selling price. */
    private function fetchArticleSellingPrice(int $article_id, int $sales_channel_id): array
    {
        $sql = <<<SQL
            SELECT selling_price
            FROM backOffice.sales_channel_product
            WHERE product_id = :article_id
                AND sales_channel_id = :sales_channel_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['article_id' => $article_id, 'sales_channel_id' => $sales_channel_id]);
    }

    /** Fetches tasks by type. */
    private function fetchTasksByType($type_id, $product_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.TC_tache
        WHERE id_type = :type_id AND id_produit = :product_id
        ORDER BY date_creation
        SQL;

        return $this->getPdo()->fetchAll($sql, [
            'type_id' => $type_id,
            'product_id' => $product_id,
        ]);
    }

    /** Fetches article planned prices to activate. */
    private function fetchArticlePlannedPricesToActive(): array
    {
        $sql = <<<SQL
        SELECT *
          FROM backOffice.article_planned_price
          WHERE
            starts_at <= now()
            AND ends_at > now()
            AND applied_at IS NULL
          ORDER BY article_planned_price_id
        SQL;

        return $this->getPdo()->fetchAll($sql);
    }

    /** Fetches article planned prices to deactivate. */
    private function fetchArticlePlannedPricesToDeactivated(): array
    {
        $sql = <<<SQL
        SELECT *
          FROM backOffice.article_planned_price
          WHERE
            ends_at < now()
          ORDER BY article_planned_price_id
        SQL;

        return $this->getPdo()->fetchAll($sql);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
