<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Dto\CreationContext\PackagedArticleContextDto;
use SonVideo\Erp\Article\Entity\PackagedArticleEntity;
use SonVideo\Erp\Article\Manager\PackageManager;
use SonVideo\Erp\Article\Manager\Validator\PackageValidator;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PackageManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/packaged_articles.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of PackageManager. */
    protected function getTestedInstance(): PackageManager
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        return self::$container->get(PackageManager::class);
    }

    /** Gets the package validator. */
    protected function getPackageValidator(): PackageValidator
    {
        return self::$container->get(PackageValidator::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /**
     * Tests the validate_package method.
     *
     * @dataProvider validatePackageDataProvider
     */
    public function test_validate_package(
        PackagedArticleContextDto $dto,
        string $exception,
        string $error_message
    ): void {
        $package_validator = $this->getPackageValidator();

        $this->expectException($exception);
        $this->expectExceptionMessage($error_message);

        $package_validator->checkIfCanAddArticle($dto);
    }

    /** Data provider for test_validate_package. */
    public function validatePackageDataProvider(): array
    {
        self::bootKernel();

        $data = [];
        $dto_values = ['article_id' => 72216, 'package_id' => 123, 'quantity' => 1];
        $data['package not found'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Article not found with id "123".',
        ];
        $dto_values = ['article_id' => 123, 'package_id' => 72218, 'quantity' => 1];
        $data['article not found'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Article not found with id "123".',
        ];
        $dto_values = ['article_id' => 72216, 'package_id' => 72215, 'quantity' => 1];
        $data['article is not a package'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Article with id 72215 is not a package.',
        ];
        $dto_values = ['article_id' => 78226, 'package_id' => 78404, 'quantity' => 1];
        $data['article already in package'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Invalid parameters', // exception context is tested in post_packaged_article.feature
        ];
        $dto_values = ['article_id' => 118223, 'package_id' => 72218, 'quantity' => 1];
        $data['package cannot have more than 8 articles'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Invalid parameters', // exception context is tested in post_packaged_article.feature
        ];
        $dto_values = ['article_id' => 78404, 'package_id' => 78404, 'quantity' => 1];
        $data['article cannot be in own package'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'An article cannot be part of its own package.',
        ];

        return $data;
    }

    /**
     * Tests the format_packaged_article method.
     *
     * @dataProvider formatPackagedArticleDataProvider
     */
    public function test_format_packaged_article(
        PackagedArticleContextDto $dto,
        array $expected_packaged_articles
    ): void {
        $packaged_article_manager = $this->getTestedInstance();
        $packaged_articles = $packaged_article_manager->explodeNewPackagedArticles($dto);

        usort($packaged_articles, function ($a, $b): bool {
            return self::sortArticles($a, $b);
        });

        usort($expected_packaged_articles, function ($a, $b): bool {
            return self::sortArticles($a, $b);
        });

        $this->assertCount(count($expected_packaged_articles), $packaged_articles);
        $this->assertEquals($expected_packaged_articles, $packaged_articles);
    }

    /** Helper method to sort articles. */
    public static function sortArticles($a, $b): bool
    {
        return $a['article_id'] > $b['article_id'];
    }

    /** Data provider for test_format_packaged_article. */
    public function formatPackagedArticleDataProvider(): array
    {
        self::bootKernel();

        $data = [];
        $dto_values = ['article_id' => 72216, 'package_id' => 72215, 'quantity' => 1];
        $data['normal article'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleContextDto::class),
            'expected_packaged_articles' => [['quantity' => 1, 'package_id' => 72215, 'article_id' => 72216]],
        ];

        $dto_values = ['article_id' => 72218, 'package_id' => 13895, 'quantity' => 3];
        $data['package'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleContextDto::class),
            'expected_packaged_articles' => [
                ['quantity' => 6, 'package_id' => 13895, 'article_id' => 72215],
                ['quantity' => 3, 'package_id' => 13895, 'article_id' => 72216],
                ['quantity' => 3, 'package_id' => 13895, 'article_id' => 78226],
                ['quantity' => 3, 'package_id' => 13895, 'article_id' => 118223],
                ['quantity' => 3, 'package_id' => 13895, 'article_id' => 118230],
                ['quantity' => 3, 'package_id' => 13895, 'article_id' => 81078],
                ['quantity' => 3, 'package_id' => 13895, 'article_id' => 81123],
                ['quantity' => 3, 'package_id' => 13895, 'article_id' => 128416],
            ],
        ];

        return $data;
    }

    /** Tests the add_article method. */
    public function test_add_article(): void
    {
        $dto = $this->getSerializer()->denormalize(
            ['package_id' => 13895, 'article_id' => 72216, 'quantity' => 1],
            PackagedArticleContextDto::class
        );

        $package_manager = $this->getTestedInstance();
        $packaged_articles = $package_manager->addNewArticle($dto);

        $this->assertCount(1, $packaged_articles);

        $article = $packaged_articles[0];
        $this->assertInstanceOf(PackagedArticleEntity::class, $article);
        $this->assertEquals(
            [
                'packaged_article_id' => 119545,
                'package_id' => 13895,
                'article_id' => 72216,
                'sku' => 'ELIPSPRESTIGE_C_2I',
                'name' => 'Prestige C2i Calvados',
                'short_description' => 'Elipson Prestige C2i Calvados',
                'image' => '/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_300_square.jpg',
                'quantity' => 1,
                'stock' => 15,
                'stock_available' => 15,
                'unit_selling_price' => 199.0,
                'delay' => 30,
                'status' => 'oui',
            ],
            $article->toArray()
        );

        $system_event = $this->fetchLastSystemEvents();
        $this->assertIsArray($system_event);
        $this->assertEquals('article.update.package', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 13895}, "data": {"packaged_article": {"added": [{"quantity": 1, "article_id": 72216}], "deleted": []}}, "meta": {"updated_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /** Gets the user for testing. */
    private function getUser(): UserEntity
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $user_array = $user->toArray();
        $user_array['username'] = '9e81bd23-e7ac-4ba3-842f-8da6554bc540';

        return $user->fromArray($user_array);
    }

    /** Fetches the last system events. */
    private function fetchLastSystemEvents(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
