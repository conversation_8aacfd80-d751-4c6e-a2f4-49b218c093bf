<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use DateInterval;
use Psr\Log\NullLogger;
use SonVideo\Erp\Article\Manager\ArticleReferencePriceCalculator;
use SonVideo\Erp\Article\Mysql\Repository\ArticlePriceRepository;
use SonVideo\Erp\PromoOffer\Entity\ActiveMarketingOperation;
use SonVideo\Erp\PromoOffer\Manager\PromoOfferManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleReferencePriceCalculatorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/article_reference_price.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /**
     * Creates a test instance of ArticleReferencePriceCalculator.
     *
     * @param ActiveMarketingOperation[] $marketing_operations
     */
    protected function getTestedInstance(array $marketing_operations = []): ArticleReferencePriceCalculator
    {
        $promo_offer_manager = $this->createMock(PromoOfferManager::class);
        $promo_offer_manager->method('getActiveMarketingOperations')->willReturn($marketing_operations);

        $tested_class = new ArticleReferencePriceCalculator(
            self::$container->get(ArticlePriceRepository::class),
            $promo_offer_manager
        );
        $tested_class->setLogger(new NullLogger());

        return $tested_class;
    }

    /** Tests the calculate method. */
    public function test_calculate(): void
    {
        // Update reference price with the lowest price of the product since 30 days
        $marketing_operation = new ActiveMarketingOperation();
        $marketing_operation->start_at = (new \DateTime())->sub(DateInterval::createFromDateString('20 day'));
        $marketing_operation->computed_article_ids = [2];

        $this->getTestedInstance([$marketing_operation])->calculateNewReferencePrice();

        $this->assertEquals(
            [
                [
                    'product_id' => '1',
                    'sales_channel_id' => '1',
                    'reference_price' => '50.00',
                ],
                [
                    'product_id' => '2',
                    'sales_channel_id' => '1',
                    'reference_price' => '150.00',
                ],
                [
                    'product_id' => '3',
                    'sales_channel_id' => '1',
                    'reference_price' => '100.00',
                ],
                [
                    'product_id' => '4',
                    'sales_channel_id' => '1',
                    'reference_price' => '400.00',
                ],
            ],
            $this->getData()
        );
    }

    /** Gets data from the database. */
    private function getData(): array
    {
        /** @var LegacyPdo $legacy_pdo */
        $legacy_pdo = self::$container->get(LegacyPdo::class);

        return $legacy_pdo->fetchAll(
            <<<SQL
                SELECT product_id, sales_channel_id, reference_price
                FROM backOffice.sales_channel_product
            SQL
        );
    }
}
