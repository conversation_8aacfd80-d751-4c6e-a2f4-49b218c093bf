<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Sql\LegacyReadonlyPdo;
use App\Sql\Query\QueryBuilder;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use Psr\Log\NullLogger;
use SonVideo\Erp\Article\Dto\CreationContext\ArticlePlannedPriceCreationContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticlePlannedPriceUpdateContextDto;
use SonVideo\Erp\Article\Manager\ArticleEventLogger;
use SonVideo\Erp\Article\Manager\ArticlePlannedPriceManager;
use SonVideo\Erp\Article\Manager\ArticleSalesChannelManager;
use SonVideo\Erp\Article\Mysql\Repository\ArticlePlannedPriceRepository;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ArticlePlannedPriceManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();

        MySqlDatabase::clearDatabases();
        PgDatabase::reloadFixtures();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/article_planned_price.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticlePlannedPriceManager. */
    protected function getTestedInstance(): ArticlePlannedPriceManager
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        $tested_class = new ArticlePlannedPriceManager(
            self::$container->get(ArticlePlannedPriceRepository::class),
            self::$container->get(SerializerInterface::class),
            self::$container->get(ValidatorInterface::class),
            self::$container->get(QueryBuilder::class),
            self::$container->get(ArticleEventLogger::class),
            self::$container->get(CurrentUser::class),
            self::$container->get(ArticleSalesChannelManager::class)
        );

        $tested_class->setLogger(new NullLogger());

        $tested_class->setConnections($this->getPdo(), self::$container->get(LegacyReadonlyPdo::class));

        return $tested_class;
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Gets the validator. */
    private function getValidator(): ValidatorInterface
    {
        return self::$container->get('validator');
    }

    /**
     * Tests the create method.
     *
     * @throws InternalServerErrorException
     * @throws NotFoundException
     * @throws ExceptionInterface
     * @throws InternalErrorException
     * @throws \Throwable
     */
    public function test_create(): void
    {
        $article_planned_price_manager = $this->getTestedInstance();

        $data = [
            'selling_price' => 385.5,
            'exit_selling_price' => 398,
            'starts_at' => '2024-08-08 10:39:48',
            'ends_at' => '2024-09-09 10:39:48',
            'sales_channel_ids' => [2, 5, 7],
        ];

        /** @var ArticlePlannedPriceCreationContextDto $dto */
        $dto = $this->getSerializer()->denormalize($data, ArticlePlannedPriceCreationContextDto::class);

        // Create article planned price
        $article_planned_price_id = $article_planned_price_manager->create(81123, $dto);
        $this->assertEquals(5, $article_planned_price_id);

        $article_planned_price = $this->fetchArticlePlannedPriceById($article_planned_price_id);
        $this->assertEquals(
            [
                'article_planned_price_id' => '5',
                'product_id' => '81123',
                'selling_price' => '385.50',
                'exit_selling_price' => '398.00',
                'starts_at' => '2024-08-08 10:39:48',
                'ends_at' => '2024-09-09 10:39:48',
                'applied_at' => null,
                'created_by' => '1',
                'sales_channel_ids' => '[2, 5, 7]',
            ],
            $article_planned_price
        );

        $system_event = $this->fetchLastSystemEvents(81123);
        $this->assertEquals('article.create.article_planned_price', $system_event['name']);

        $event_payload = json_decode($system_event['payload'], true, 512, JSON_THROW_ON_ERROR);
        $this->assertEquals(81123, $event_payload['_rel']['article']);
        $this->assertEquals($article_planned_price_id, $event_payload['_rel']['article_planned_price']);

        $this->assertEquals(
            [
                'user_id' => 1,
                'lastname' => 'Admin',
                'username' => 'admin',
                'firstname' => 'Seigneur',
            ],
            $event_payload['meta']['created_by']
        );

        $this->assertEquals(
            [
                'starts_at' => '2024-08-08 10:39:48',
                'ends_at' => '2024-09-09 10:39:48',
                'article_id' => 81123,
                'created_by' => 1,
                'selling_price' => 385.5,
                'exit_selling_price' => 398,
                'sales_channel_ids' => [2, 5, 7],
            ],
            $event_payload['data']
        );
    }

    /**
     * Tests the create method with errors.
     *
     * @throws ExceptionInterface
     * @throws \Throwable
     * @throws InternalErrorException
     */
    public function test_create_with_error(): void
    {
        // Test with a non-existant article
        $data = [
            'selling_price' => 385.5,
            'exit_selling_price' => 400,
            'starts_at' => '2024-08-08 10:39:48',
            'ends_at' => '2024-09-09 10:39:48',
            'sales_channel_ids' => [2, 5, 7],
        ];

        /** @var ArticlePlannedPriceCreationContextDto $dto */
        $dto = $this->getSerializer()->denormalize($data, ArticlePlannedPriceCreationContextDto::class);

        $article_planned_price_manager = $this->getTestedInstance();

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('No article found with id 666.');
        $article_planned_price_manager->create(666, $dto);
    }

    /**
     * Tests the create method with date overlap error.
     *
     * @throws ExceptionInterface
     * @throws \Throwable
     * @throws InternalErrorException
     */
    public function test_create_with_date_overlap_error(): void
    {
        // Test date overlap
        $data = [
            'selling_price' => 385.5,
            'exit_selling_price' => 400,
            'starts_at' => '2024-08-08 10:39:48',
            'ends_at' => '2001-01-01 10:11:12',
            'sales_channel_ids' => [2, 5, 7],
        ];

        /** @var ArticlePlannedPriceCreationContextDto $dto */
        $dto = $this->getSerializer()->denormalize($data, ArticlePlannedPriceCreationContextDto::class);

        $article_planned_price_manager = $this->getTestedInstance();

        try {
            $article_planned_price_manager->create(128416, $dto);
            $this->fail('Expected InternalErrorException was not thrown');
        } catch (InternalErrorException $e) {
            $this->assertEquals('Invalid parameters', $e->getMessage());
            $messages = $e->getContext()['validation_errors'];
            $this->assertEquals(
                [
                    'starts_at' => '[key:starts_at_must_be_before_ends_at] The end date must be greater than the start date.',
                    'ends_at' => '[key:starts_at_must_be_before_ends_at] The end date must be greater than the start date.',
                ],
                $messages
            );
        }
    }

    /**
     * Tests the create method with price validation error.
     *
     * @throws ExceptionInterface
     */
    public function test_create_with_price_validation_error(): void
    {
        // Test add planned price with a selling price must be below the packaged articles price
        $dto = $this->getSerializer()->denormalize(
            [
                'article_id' => 13895,
                'created_by' => 1033,
                'selling_price' => 51,
                'exit_selling_price' => 70,
                'starts_at' => '2024-08-08 10:39:48',
                'ends_at' => '2024-09-08 10:39:48',
                'sales_channel_ids' => [],
            ],
            ArticlePlannedPriceCreationContextDto::class
        );

        $errors = $this->getValidator()->validate($dto);
        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertEquals(
            '[key:package_selling_price_too_high] value "51" : The price must be below the packaged article price',
            $messages['selling_price']
        );
        $this->assertEquals(
            '[key:selling_price_too_high] value "70" : The price must be below the PVGC',
            $messages['exit_selling_price']
        );
        $this->assertEquals('This value should not be blank.', $messages['sales_channel_ids']);
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        $article_id = 128416;
        $article_planned_price_id = 2;

        // Log event correctly when updated article planned price
        $data = [
            'article_id' => $article_id,
            'article_planned_price_id' => $article_planned_price_id,
            'selling_price' => 800,
            'exit_selling_price' => 810.5,
            'starts_at' => '2024-08-08 11:11:11',
            'ends_at' => '2024-09-08 12:12:12',
            'sales_channel_ids' => [1, 2, 3],
        ];

        /** @var ArticlePlannedPriceUpdateContextDto $dto */
        $dto = $this->getSerializer()->denormalize($data, ArticlePlannedPriceUpdateContextDto::class);

        $this->getTestedInstance()->update($dto);
        $article_planned_price = $this->fetchArticlePlannedPriceById($article_planned_price_id);
        $this->assertEquals(
            [
                'article_planned_price_id' => (string) $article_planned_price_id,
                'product_id' => (string) $article_id,
                'selling_price' => '800.00',
                'exit_selling_price' => '810.50',
                'starts_at' => '2024-08-08 11:11:11',
                'ends_at' => '2024-09-08 12:12:12',
                'applied_at' => null,
                'created_by' => '1033',
                'sales_channel_ids' => '[1, 2, 3]',
            ],
            $article_planned_price
        );

        $system_event = $this->fetchLastSystemEvents($article_id);

        $this->assertEquals('article.update.article_planned_price', $system_event['name']);

        $this->assertEquals(
            [
                'starts_at' => ['old' => '2024-09-01 10:35:41', 'new' => '2024-08-08 11:11:11'],
                'ends_at' => ['old' => '2025-09-01 10:35:41', 'new' => '2024-09-08 12:12:12'],
                'selling_price' => ['old' => 802.5, 'new' => 800],
            ],
            json_decode($system_event['payload'], true, 512, JSON_THROW_ON_ERROR)['data']
        );
    }

    /**
     * Tests the update method with non-existent article.
     *
     * @throws ExceptionInterface
     * @throws \Throwable
     * @throws InternalErrorException
     */
    public function test_update_with_non_existant_article(): void
    {
        // Test update with a non-existant article
        $data = [
            'article_id' => 666,
            'article_planned_price_id' => 2,
            'selling_price' => 800,
            'exit_selling_price' => 810.5,
            'starts_at' => '2024-08-08 11:11:11',
            'ends_at' => '2024-09-08 12:12:12',
            'sales_channel_ids' => [1, 2, 3],
        ];

        /** @var ArticlePlannedPriceUpdateContextDto $dto */
        $dto = $this->getSerializer()->denormalize($data, ArticlePlannedPriceUpdateContextDto::class);

        $article_planned_price_manager = $this->getTestedInstance();

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Article planned price not found with id "666" for article with id "2".');
        $article_planned_price_manager->update($dto);
    }

    /** Tests the delete method. */
    public function test_delete(): void
    {
        // Log event correctly when deleted article planned price
        $this->getTestedInstance()->delete(3);

        $article_planned_price = $this->fetchArticlePlannedPriceById(3);
        $this->assertEquals([], $article_planned_price);

        $system_event = $this->fetchLastSystemEvents(72215);

        $this->assertEquals('article.delete.article_planned_price', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 72215, "article_planned_price": 3}, "data": {"ends_at": "2025-09-01 10:35:41", "starts_at": "2024-10-01 10:35:41", "applied_at": null, "article_id": 72215, "created_by": {"id": 1033, "fullname": "Hugo LAHUTTE"}, "selling_price": 269, "sales_channel_ids": [1, 2, 3], "exit_selling_price": 280, "article_planned_price_id": 3}, "meta": {"deleted_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /**
     * Tests the delete method with error.
     *
     * @throws ExceptionInterface
     * @throws \Throwable
     * @throws InternalErrorException
     */
    public function test_delete_with_error(): void
    {
        // Test delete with a non-existant article planned price
        $article_planned_price_id = 78;

        $article_planned_price_manager = $this->getTestedInstance();

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Article planned price not found.');
        $article_planned_price_manager->delete($article_planned_price_id);
    }

    /** Fetches the last system events for a given main ID. */
    private function fetchLastSystemEvents($main_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        WHERE main_id = :main_id
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql, ['main_id' => $main_id]);
    }

    /** Fetches an article planned price by ID. */
    private function fetchArticlePlannedPriceById($article_planned_price_id): array
    {
        $sql = <<<SQL
            SELECT *
            FROM backOffice.article_planned_price
            WHERE article_planned_price_id = :article_planned_price_id
        SQL;

        $result = $this->getPdo()->fetchOne($sql, ['article_planned_price_id' => $article_planned_price_id]);

        return false === $result ? [] : $result;
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
