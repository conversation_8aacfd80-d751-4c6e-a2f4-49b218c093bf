<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\Article\Manager\ArticleEventLogger;
use SonVideo\Erp\Article\Manager\ArticleMarginCalculator;
use SonVideo\Erp\Article\Manager\ArticleSalesChannelManager as TestedClass;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Article\Mysql\Repository\ArticleSalesChannelRepository;
use SonVideo\Erp\Article\Mysql\Repository\ArticleUnconditionalDiscountRepository;
use SonVideo\Erp\Article\Mysql\Repository\PackagedArticleRepository;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use SonVideo\Erp\Referential\SalesChannel;
use SonVideo\Erp\SalesChannel\Mysql\Repository\SalesChannelRepository;
use SonVideo\Erp\System\Common\CurrentUser;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleSalesChannelManagerTest extends KernelTestCase
{
    private TestedClass $article_sales_channel_manager;
    private LegacyPdo $pdo;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/get_article_by_id_or_sku_v2.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();

        $this->article_sales_channel_manager = $this->getTestedInstance();
    }

    protected function getTestedInstance(): TestedClass
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        $instance = new TestedClass(
            self::$container->get(LegacyPdo::class),
            self::$container->get(ArticleSalesChannelRepository::class),
            self::$container->get(ArticleEventLogger::class),
            self::$container->get(ArticleRepository::class),
            self::$container->get(SingleArticleReadRepository::class),
            self::$container->get(CurrentUser::class),
            self::$container->get(SalesChannelRepository::class),
            self::$container->get(ArticleUnconditionalDiscountRepository::class),
            self::$container->get(ArticleMarginCalculator::class),
            self::$container->get(PackagedArticleRepository::class)
        );
        $instance->setLogger(self::$container->get(LoggerInterface::class));

        $this->pdo = self::$container->get(LegacyPdo::class);

        return $instance;
    }

    /** @throws NotFoundException */
    public function test_create(): void
    {
        // Test: Unknown article throws exception
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('No article found with id 666.');
        $this->article_sales_channel_manager->create(666, 1);
    }

    public function test_create_with_unknown_sales_channel(): void
    {
        // Test: Unknown sales channel throws exception
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('No sales channel found with id 666.');
        $this->article_sales_channel_manager->create(72215, 666);
    }

    public function test_create_with_existing_article_sales_channel(): void
    {
        // Test: Article sales channel already exists throws exception
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Article sales channel already exists.');
        $this->article_sales_channel_manager->create(72215, 1);
    }

    public function test_create_valid_article_sales_channel(): void
    {
        // Test: Valid create article sales channel and update system_events
        $created = $this->article_sales_channel_manager->create(72215, 2);

        $this->assertEquals(1, $created);

        $system_event = $this->fetchLastSystemEvents();
        $this->assertIsArray($system_event);
        $this->assertEquals('article.create.sales_channel_product', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 72215, "sales_channel_id": 2}, "data": {"label": "son-video.pro", "sales_channel_id": "2"}, "meta": {"created_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    public function test_update_article_price_with_taxes(): void
    {
        // Test: Update article price with taxes
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Product margin is too low');
        $this->article_sales_channel_manager->updateSellingPriceWithTaxes(72215, SalesChannel::SON_VIDEO, 10.0);
    }

    public function test_price_threshold_rule_validation(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage(
            '[key:package_selling_price_too_high] The price must be below the packaged article price'
        );
        $this->article_sales_channel_manager->updateSellingPriceWithTaxes(72218, SalesChannel::SON_VIDEO, 760.0);
    }

    public function test_update_article_price_during_sales()
    {
        $updated = $this->article_sales_channel_manager->updateSellingPriceWithTaxes(
            128416,
            SalesChannel::SON_VIDEO,
            800.0
        );

        $this->assertEquals(0, $updated);

        $updated = $this->article_sales_channel_manager->updateSellingPriceWithTaxes(
            128416,
            SalesChannel::EASYLOUNGE,
            800.0
        );

        $this->assertEquals(1, $updated);
    }

    public function test_check_sales_channel_product_after_creation(): void
    {
        // Create a new sales channel product
        $article_id = 72215;
        $sales_channel_id = 3; // Using a different sales channel than previous tests

        // Ensure it doesn't exist before the test
        $sql_check = <<<SQL
        SELECT COUNT(*) as count
        FROM backOffice.sales_channel_product
        WHERE product_id = :article_id
        AND sales_channel_id = :sales_channel_id
        SQL;

        $result = $this->pdo->fetchOne($sql_check, [
            'article_id' => $article_id,
            'sales_channel_id' => $sales_channel_id,
        ]);

        // If it exists, delete it for the test
        if ($result['count'] > 0) {
            $sql_delete = <<<SQL
            DELETE FROM backOffice.sales_channel_product
            WHERE product_id = :article_id
            AND sales_channel_id = :sales_channel_id
            SQL;

            $this->pdo->fetchAffected($sql_delete, [
                'article_id' => $article_id,
                'sales_channel_id' => $sales_channel_id,
            ]);
        }

        // Create the sales channel product
        $created = $this->article_sales_channel_manager->create($article_id, $sales_channel_id);
        $this->assertEquals(1, $created);

        // Fetch the created sales channel product
        $sales_channel_product = self::$container
            ->get(ArticleSalesChannelRepository::class)
            ->findById($article_id, $sales_channel_id);

        // Check that it exists and has the correct properties
        $this->assertNotNull($sales_channel_product);
        $this->assertEquals($article_id, $sales_channel_product->product_id);
        $this->assertEquals($sales_channel_id, $sales_channel_product->sales_channel_id);
        $this->assertEquals('easylounge.com', $sales_channel_product->sales_channel_label);
        $this->assertNull($sales_channel_product->reference_price);
        $this->assertTrue($sales_channel_product->is_active);

        // Check that the selling price was set correctly (should be copied from the article or from sales_channel_id 1)
        $this->assertGreaterThan(0, $sales_channel_product->selling_price);

        // Check that the system event was created
        $system_event = $this->fetchLastSystemEvents();
        $this->assertIsArray($system_event);
        $this->assertEquals('article.create.sales_channel_product', $system_event['name']);

        // Check the payload contains the correct data
        $payload = json_decode($system_event['payload'], true);
        $this->assertEquals($article_id, $payload['_rel']['article']);
        $this->assertEquals($sales_channel_id, $payload['_rel']['sales_channel_id']);
        $this->assertEquals('easylounge.com', $payload['data']['label']);
        $this->assertEquals($sales_channel_id, $payload['data']['sales_channel_id']);
    }

    private function fetchLastSystemEvents(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->pdo->fetchOne($sql);
    }
}
