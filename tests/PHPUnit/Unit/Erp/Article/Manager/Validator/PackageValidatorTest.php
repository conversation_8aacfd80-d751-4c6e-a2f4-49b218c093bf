<?php

namespace PHPUnit\Unit\Erp\Article\Manager\Validator;

use App\Adapter\Serializer\SerializerInterface;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Article\Dto\CreationContext\PackagedArticleContextDto as PackagedArticleCreationContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\PackagedArticleContextDto as PackagedArticleUpdateContextDto;
use SonVideo\Erp\Article\Manager\Validator\PackageValidator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PackageValidatorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/packaged_articles.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of PackageValidator. */
    protected function getTestedInstance(): PackageValidator
    {
        return self::$container->get(PackageValidator::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** @dataProvider checkIfCanAddArticleDataProvider */
    public function test_check_if_can_add_article(
        PackagedArticleCreationContextDto $dto,
        string $exception,
        string $error_message
    ): void {
        $packaged_validator = $this->getTestedInstance();

        $this->expectException($exception);
        $this->expectExceptionMessage($error_message);

        $packaged_validator->checkIfCanAddArticle($dto);
    }

    /** Data provider for test_check_if_can_add_article. */
    public function checkIfCanAddArticleDataProvider(): array
    {
        self::bootKernel();

        $data = [];
        $dto_values = ['article_id' => 72216, 'package_id' => 123, 'quantity' => 1];
        $data['package not found'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleCreationContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Article not found with id "123".',
        ];
        $dto_values = ['article_id' => 123, 'package_id' => 72218, 'quantity' => 1];
        $data['article not found'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleCreationContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Article not found with id "123".',
        ];
        $dto_values = ['article_id' => 72216, 'package_id' => 72215, 'quantity' => 1];
        $data['article is not a package'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleCreationContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Article with id 72215 is not a package.',
        ];
        $dto_values = ['article_id' => 78226, 'package_id' => 78404, 'quantity' => 1];
        $data['article already in package'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleCreationContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Invalid parameters', // exception context is tested in post_packaged_article.feature
        ];
        $dto_values = ['article_id' => 118223, 'package_id' => 72218, 'quantity' => 1];
        $data['package cannot have more than 8 articles'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleCreationContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Invalid parameters', // exception context is tested in post_packaged_article.feature
        ];
        $dto_values = ['article_id' => 78404, 'package_id' => 78404, 'quantity' => 1];
        $data['article cannot be in own package'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleCreationContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'An article cannot be part of its own package.',
        ];

        return $data;
    }

    /** @dataProvider checkIfCanEditPackagedArticleDataProvider */
    public function test_check_if_can_edit_packaged_article(
        PackagedArticleUpdateContextDto $dto,
        string $exception,
        string $error_message
    ): void {
        $packaged_validator = $this->getTestedInstance();

        $this->expectException($exception);
        $this->expectExceptionMessage($error_message);

        $packaged_validator->checkIfCanEditPackagedArticle($dto);
    }

    /** Data provider for test_check_if_can_edit_packaged_article. */
    public function checkIfCanEditPackagedArticleDataProvider(): array
    {
        self::bootKernel();

        $data = [];
        $dto_values = ['packaged_article_id' => 666, 'package_id' => 120087, 'quantity' => 1];
        $data['packaged article not found'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleUpdateContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Packaged article not found.',
        ];
        $dto_values = ['packaged_article_id' => 119544, 'package_id' => 123, 'quantity' => 1];
        $data['package not found'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleUpdateContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Package not found or packaged article not found in package.',
        ];
        $dto_values = ['packaged_article_id' => 119544, 'package_id' => 72218, 'quantity' => 1];
        $data['not in package'] = [
            'dto' => $this->getSerializer()->denormalize($dto_values, PackagedArticleUpdateContextDto::class),
            'exception' => \Exception::class,
            'error_message' => 'Package not found or packaged article not found in package.',
        ];

        return $data;
    }
}
