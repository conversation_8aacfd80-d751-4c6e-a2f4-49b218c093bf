<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrder\Manager;

use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderStatusUpdater as TestedClass;
use SonVideo\Erp\Referential\CustomerOrderInternalStatus;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderStatusUpdaterTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/rpc/activate.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** Tests activating a customer order. */
    public function test_activate(): void
    {
        $customer_order_id = 1724028;

        // Check initial state
        $customer_order = $this->fetchOneCustomerOrder($customer_order_id);
        $this->assertEquals('1724028', $customer_order['id_commande']);
        $this->assertEquals(CustomerOrderInternalStatus::ON_GOING, $customer_order['flux']);
        $this->assertNull($customer_order['V_statut_traitement']);
        $this->assertEquals('0', $customer_order['compteur_paiement']);

        // Activate customer order
        $this->getTestedInstance()->removeImportStatus($customer_order_id);

        // Check state after activation
        $customer_order = $this->fetchOneCustomerOrder($customer_order_id);
        $this->assertEquals('1724028', $customer_order['id_commande']);
        $this->assertEquals(CustomerOrderInternalStatus::ON_GOING, $customer_order['flux']);
        $this->assertEquals('lvr_attente', $customer_order['V_statut_traitement']);
        $this->assertEquals('2', $customer_order['compteur_paiement']);

        // Check no task created
        $this->assertCount(0, $this->fetchTask($customer_order_id));

        // Check no comment created
        $this->assertCount(0, $this->fetchComment($customer_order_id));

        // Test activating a non-existent customer order
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Customer order not found with id "314159265"');
        $this->getTestedInstance()->removeImportStatus(314159265);
    }

    /** Tests that activating a customer order with incomplete payment creates a task. */
    public function test_activate_should_create_a_task(): void
    {
        $customer_order_id = 1724028;
        $this->setIncompletePayment($customer_order_id);

        // Activate customer order
        $this->getTestedInstance()->removeImportStatus($customer_order_id);

        // Check state after activation
        $customer_order = $this->fetchOneCustomerOrder($customer_order_id);
        $this->assertEquals('1724028', $customer_order['id_commande']);

        // Check task created
        $tasks = $this->fetchTask($customer_order_id);
        $this->assertCount(1, $tasks);
        $this->assertEquals((string) $customer_order_id, $tasks[0]->id_commande);
        $this->assertEquals('20', $tasks[0]->id_type);
        $this->assertEquals(UserEntity::CATHY_ID, $tasks[0]->id_utilisateur);
        $this->assertEquals('La somme des paiements est différente de la somme des produits.', $tasks[0]->sujet);

        // Check comment created
        $comments = $this->fetchComment($customer_order_id);
        $this->assertCount(1, $comments);
        $this->assertEquals((string) $customer_order_id, $comments[0]->id_commande);
        $this->assertEquals(UserEntity::SYSTEM_USERNAME, $comments[0]->utilisateur);
        $this->assertEquals(
            'La somme des paiements est différente de la somme des produits.',
            $comments[0]->commentaire
        );
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /**
     * Fetches a customer order by ID.
     *
     * @return array|false
     */
    protected function fetchOneCustomerOrder(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.commande
        WHERE id_commande = :customer_order_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * Fetches tasks for a customer order.
     *
     * @return array
     */
    protected function fetchTask(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.TC_tache
        WHERE id_commande = :customer_order_id
        ORDER BY id DESC
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * Fetches comments for a customer order.
     *
     * @return array
     */
    protected function fetchComment(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.commentaire_commande
        WHERE id_commande = :customer_order_id
        ORDER BY id DESC
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    /** Sets incomplete payment for a customer order. */
    protected function setIncompletePayment(int $customer_order_id): void
    {
        $sql = <<<SQL
        UPDATE backOffice.paiement_commande
        SET creation_montant = 10
        WHERE id_commande = :customer_order_id
        SQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => $customer_order_id]);
    }
}
