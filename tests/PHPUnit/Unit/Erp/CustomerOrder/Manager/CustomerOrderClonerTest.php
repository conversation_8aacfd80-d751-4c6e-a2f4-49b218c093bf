<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrder\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderCloner as TestedClass;
use SonVideo\Erp\Referential\CustomerOrderIpAddress;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class CustomerOrderClonerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/clone_customer_order.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /**
     * Gets the tested instance.
     *
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /**
     * Gets the serializer.
     *
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /**
     * Tests getting creation context from customer order ID successfully.
     *
     * @throws ContainerExceptionInterface
     * @throws ExceptionInterface
     * @throws NotFoundException
     * @throws NotFoundExceptionInterface
     */
    public function test_get_creation_context_from_customer_order_id_successfully(): void
    {
        $dto = $this->getTestedInstance()->getCreationContextFromCustomerOrderId(4);
        $customer_order = $this->fetchOneCustomerOrder(4);

        $this->assertNull($dto->original_customer_order_id);
        $this->assertEquals(4, $dto->clone_customer_order_id);
        $this->assertEquals('backoffice.sonvideopro.com', $dto->origin);
        $this->assertEquals((new \DateTime())->format('Y-m-d H:i:s'), $dto->created_at);
        $this->assertEquals($customer_order['id_prospect'], $dto->customer_id);
        $this->assertEquals($customer_order['quote_id'], $dto->quote_id);
        $this->assertEquals(CustomerOrderIpAddress::BACKOFFICE, $dto->ip_address);
        $this->assertFalse($dto->is_excluding_tax);
        $this->assertTrue($dto->is_amazon_business);
        $this->assertEquals($customer_order['promotion_id'], $dto->promotion_id);
        $this->assertNull($dto->promotion_code);
        $this->assertEmpty($dto->promotion_linked_products);
        $this->assertNull($dto->estimated_delivery_date);
        $this->assertEquals($customer_order['V_montant_ttc'], $dto->total_price);
        $this->assertNull($dto->return_url);

        // Shipping address assertions
        $this->assertEquals($customer_order['cnt_lvr_societe'], $dto->shipping_address->company_name);
        $this->assertEquals($customer_order['cnt_lvr_civilite'], $dto->shipping_address->civility);
        $this->assertEquals($customer_order['cnt_lvr_prenom'], $dto->shipping_address->firstname);
        $this->assertEquals($customer_order['cnt_lvr_nom'], $dto->shipping_address->lastname);
        $this->assertEquals($customer_order['cnt_lvr_adresse'], $dto->shipping_address->address);
        $this->assertEquals($customer_order['cnt_lvr_code_postal'], $dto->shipping_address->postal_code);
        $this->assertEquals($customer_order['cnt_lvr_ville'], $dto->shipping_address->city);
        $this->assertEquals('FR', $dto->shipping_address->country_code);
        $this->assertEquals($customer_order['cnt_lvr_telephone'], $dto->shipping_address->phone);
        $this->assertEquals($customer_order['cnt_lvr_mobile'], $dto->shipping_address->cellphone);
        $this->assertEquals($customer_order['cnt_lvr_email'], $dto->shipping_address->email);
        $this->assertEquals($customer_order['cnt_fct_numero_tva'], $dto->shipping_address->vat_number);

        // Billing address assertions
        $this->assertEquals($customer_order['cnt_fct_societe'], $dto->billing_address->company_name);
        $this->assertEquals($customer_order['cnt_fct_civilite'], $dto->billing_address->civility);
        $this->assertEquals($customer_order['cnt_fct_prenom'], $dto->billing_address->firstname);
        $this->assertEquals($customer_order['cnt_fct_nom'], $dto->billing_address->lastname);
        $this->assertEquals($customer_order['cnt_fct_adresse'], $dto->billing_address->address);
        $this->assertEquals($customer_order['cnt_fct_code_postal'], $dto->billing_address->postal_code);
        $this->assertEquals($customer_order['cnt_fct_ville'], $dto->billing_address->city);
        $this->assertEquals('FR', $dto->billing_address->country_code);
        $this->assertEquals($customer_order['cnt_fct_telephone'], $dto->billing_address->phone);
        $this->assertEquals($customer_order['cnt_fct_mobile'], $dto->billing_address->cellphone);
        $this->assertEquals($customer_order['cnt_fct_email'], $dto->billing_address->email);
        $this->assertEquals($customer_order['cnt_fct_numero_tva'], $dto->billing_address->vat_number);

        // Products assertions
        $products = $this->fetchCustomerOrderProducts(4);
        $this->assertCount(count($products), $dto->products);
        $this->assertEquals($products[0]['reference'], $dto->products[0]->sku);
        $this->assertEquals($products[0]['description'], $dto->products[0]->description);
        $this->assertEquals($products[0]['prix_ecotaxe'], $dto->products[0]->ecotax_price);
        $this->assertEquals($products[0]['prix_sorecop'], $dto->products[0]->sorecop_price);
        $this->assertEquals($products[0]['quantite'], $dto->products[0]->quantity);
        $this->assertEquals($products[0]['prix_vente'], $dto->products[0]->selling_price_tax_included);
        $this->assertEquals($products[0]['remise_montant'], $dto->products[0]->unit_discount_amount);
    }

    /**
     * Gets the PDO instance.
     *
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /**
     * Fetches a customer order by ID.
     *
     * @return false|array
     *
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function fetchOneCustomerOrder(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.commande
        WHERE id_commande = :customer_order_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * Fetches customer order products.
     *
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    protected function fetchCustomerOrderProducts(int $customer_order_id): array
    {
        $sql = <<<SQL
        SELECT pc.*, p.reference
        FROM backOffice.produit_commande pc
        INNER JOIN backOffice.produit p ON p.id_produit = pc.id_produit
        INNER JOIN backOffice.article a ON a.id_produit = pc.id_produit
        WHERE id_commande = :customer_order_id
        ORDER BY id_produit DESC
        SQL;

        return $this->getPdo()->fetchAll($sql, ['customer_order_id' => $customer_order_id]);
    }
}
