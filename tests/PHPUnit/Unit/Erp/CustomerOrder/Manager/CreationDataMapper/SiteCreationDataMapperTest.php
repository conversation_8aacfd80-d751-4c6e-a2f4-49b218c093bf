<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrder\Manager\CreationDataMapper;

use App\Exception\NotFoundException;
use App\Tests\Mock\Erp\CustomerOrder\CustomerOrderPayload;
use App\Tests\Utils\ArrayHelper;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderCreationContextEntity;
use SonVideo\Erp\CustomerOrder\Manager\CreationDataMapper\SiteCreationDataMapper as TestedClass;
use SonVideo\Erp\Referential\CustomerOrderTag;
use SonVideo\Erp\Referential\Product;
use SonVideo\Erp\Referential\SalesChannel;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SiteCreationDataMapperTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/rpc/creator.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** Tests mapping data successfully with a quote. */
    public function test_map_data_with_quote(): void
    {
        $result = $this->getTestedInstance()->map(
            CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode()
        );

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);
        $this->assertEquals(
            ArrayHelper::sortKeys($result->toArray()),
            ArrayHelper::sortKeys([
                'customer_order_id' => 123,
                'original_customer_order_id' => '123',
                'clone_customer_order_id' => null,
                'origin' => 'son-video.com',
                'internal_status' => 'traitement',
                'created_at' => '2022-01-01 00:00:00',
                'estimated_delivery_date' => null,
                'customer_id' => 1,
                'new_customer' => false,
                'quote_id' => 10,
                'warehouse_id' => 5,
                'invoice_comment' => '',
                'ip_address' => '***********',
                'billing_address_company_name' => '',
                'billing_address_civility' => 'M.',
                'billing_address_firstname' => 'Alain',
                'billing_address_lastname' => 'TERIEUR',
                'billing_address_address' => '1 rue des fleurs',
                'billing_address_postal_code' => '44100',
                'billing_address_city' => 'NANTES',
                'billing_address_country_id' => 67,
                'billing_address_phone' => '0606060606',
                'billing_address_cellphone' => '0606060607',
                'billing_address_email' => '<EMAIL>',
                'billing_vat_number' => null,
                'shipping_address_company_name' => '',
                'shipping_address_civility' => 'M.',
                'shipping_address_firstname' => 'Laure',
                'shipping_address_lastname' => 'DURE',
                'shipping_address_address' => '111 route de paris',
                'shipping_address_postal_code' => '44000',
                'shipping_address_city' => 'NANTES',
                'shipping_address_country_id' => 67,
                'shipping_address_phone' => '0707070707',
                'shipping_address_cellphone' => '0707070708',
                'shipping_address_email' => '<EMAIL>',
                'carrier_id' => 2,
                'shipment_method_id' => 1,
                'store_pickup_id' => null,
                'relay_id' => null,
                'svd_header' => true,
                'tags' => [
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::STATUS_IMPORT,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SITE_CHECKOUT_V2,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SOURCE_INTRAGROUP,
                        'meta' => null,
                    ],
                ],
                'payments' => [
                    [
                        'customer_order_id' => 123,
                        'payment_mean' => 'CBS-O',
                        'unique_id' => 1,
                        'created_at' => '2022-01-01 00:00:00',
                        'amount' => 2362.99,
                        'created_proof' => '123-1',
                        'origin' => 'son-video.com',
                        'extra_data' => [],
                        'workflow' => 'legacy',
                    ],
                ],
                'products' => [
                    [
                        'customer_order_id' => 123,
                        'product_id' => 81078,
                        'type' => Product::TYPE_ARTICLE,
                        'quantity' => 2,
                        'selling_price_tax_included' => 1200.0,
                        'vat' => 0.2,
                        'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                        'discount_type' => 'devis',
                        'discount_amount' => -200.0,
                        'discount_description' => 'Remise devis',
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => 'devis',
                        'group_description' => 'Remise devis',
                        'warranty_duration_ext' => '5',
                        'warranty_unit_selling_price_ext' => 79.0,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'product_id' => Product::SHIPMENT_PRODUCT_ID,
                        'type' => Product::TYPE_GENERIQUE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 4.99,
                        'vat' => 0.2,
                        'description' => 'Frais de port facturés',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => null,
                        'group_description' => null,
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                ],
                'chrono_precise_appointment' => null,
                'is_intragroup' => true,
                'is_b2b' => false,
                'quote_creator_username' => null,
                'is_excluding_tax' => 'non',
                'sales_channel_id' => SalesChannel::SON_VIDEO,
                'promotion_id' => 23,
            ])
        );
    }

    /** Tests mapping data successfully without a quote. */
    public function test_map_data_without_quote(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $payload['quote_id'] = null;
        $result = $this->getTestedInstance()->map($payload);

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);

        $this->assertEquals(
            ArrayHelper::sortKeys($result->toArray()),
            ArrayHelper::sortKeys([
                'customer_order_id' => 123,
                'original_customer_order_id' => '123',
                'clone_customer_order_id' => null,
                'origin' => 'son-video.com',
                'internal_status' => 'traitement',
                'created_at' => '2022-01-01 00:00:00',
                'estimated_delivery_date' => null,
                'customer_id' => 1,
                'new_customer' => false,
                'quote_id' => null,
                'warehouse_id' => null,
                'invoice_comment' => '',
                'ip_address' => '***********',
                'billing_address_company_name' => '',
                'billing_address_civility' => 'M.',
                'billing_address_firstname' => 'Alain',
                'billing_address_lastname' => 'TERIEUR',
                'billing_address_address' => '1 rue des fleurs',
                'billing_address_postal_code' => '44100',
                'billing_address_city' => 'NANTES',
                'billing_address_country_id' => 67,
                'billing_address_phone' => '0606060606',
                'billing_address_cellphone' => '0606060607',
                'billing_address_email' => '<EMAIL>',
                'billing_vat_number' => null,
                'shipping_address_company_name' => '',
                'shipping_address_civility' => 'M.',
                'shipping_address_firstname' => 'Laure',
                'shipping_address_lastname' => 'DURE',
                'shipping_address_address' => '111 route de paris',
                'shipping_address_postal_code' => '44000',
                'shipping_address_city' => 'NANTES',
                'shipping_address_country_id' => 67,
                'shipping_address_phone' => '0707070707',
                'shipping_address_cellphone' => '0707070708',
                'shipping_address_email' => '<EMAIL>',
                'carrier_id' => 2,
                'shipment_method_id' => 1,
                'store_pickup_id' => null,
                'relay_id' => null,
                'svd_header' => true,
                'tags' => [
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::STATUS_IMPORT,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SITE_CHECKOUT_V2,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SOURCE_WEB_SV,
                        'meta' => null,
                    ],
                ],
                'payments' => [
                    [
                        'customer_order_id' => 123,
                        'payment_mean' => 'CBS-O',
                        'unique_id' => 1,
                        'created_at' => '2022-01-01 00:00:00',
                        'amount' => 2362.99,
                        'created_proof' => '123-1',
                        'origin' => 'son-video.com',
                        'extra_data' => [],
                        'workflow' => 'legacy',
                    ],
                ],
                'products' => [
                    [
                        'customer_order_id' => 123,
                        'product_id' => 81078,
                        'type' => Product::TYPE_ARTICLE,
                        'quantity' => 2,
                        'selling_price_tax_included' => 1200.0,
                        'vat' => 0.2,
                        'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                        'discount_type' => 'commande',
                        'discount_amount' => -200.0,
                        'discount_description' => 'Remise exceptionnelle',
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => 'commande',
                        'group_description' => 'Remise exceptionnelle',
                        'warranty_duration_ext' => '5',
                        'warranty_unit_selling_price_ext' => 79.0,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'product_id' => Product::SHIPMENT_PRODUCT_ID,
                        'type' => Product::TYPE_GENERIQUE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 4.99,
                        'vat' => 0.2,
                        'description' => 'Frais de port facturés',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => null,
                        'group_description' => null,
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                ],
                'chrono_precise_appointment' => null,
                'is_intragroup' => false,
                'is_b2b' => false,
                'quote_creator_username' => null,
                'is_excluding_tax' => 'non',
                'sales_channel_id' => SalesChannel::SON_VIDEO,
                'promotion_id' => 23,
            ])
        );
    }

    /** Tests that mapping fails if SKU is not found. */
    public function test_fail_mapping_if_sku_not_found(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $payload['products'][0]['sku'] = 'TOTO';

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Product not found with id or sku "TOTO".');
        $this->getTestedInstance()->map($payload);
    }

    /** Tests that mapping fails if carrier_id is not found. */
    public function test_fail_mapping_if_carrier_id_not_found(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $payload['shipment_method']['shipment_method_id'] = 666;

        $this->expectException(NotFoundException::class);
        $this->getTestedInstance()->map($payload);
    }

    /** Tests mapping data successfully with a B2B customer. */
    public function test_map_data_with_b2b_customer(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $payload['quote_id'] = 11;
        $payload['customer_id'] = 2;
        $result = $this->getTestedInstance()->map($payload);

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);

        $this->assertEquals(
            ArrayHelper::sortKeys($result->toArray()),
            ArrayHelper::sortKeys([
                'customer_order_id' => 123,
                'original_customer_order_id' => '123',
                'clone_customer_order_id' => null,
                'origin' => 'son-video.com',
                'internal_status' => 'traitement',
                'created_at' => '2022-01-01 00:00:00',
                'estimated_delivery_date' => null,
                'customer_id' => 2,
                'new_customer' => false,
                'quote_id' => 11,
                'warehouse_id' => 5,
                'invoice_comment' => '',
                'ip_address' => '***********',
                'billing_address_company_name' => '',
                'billing_address_civility' => 'M.',
                'billing_address_firstname' => 'Alain',
                'billing_address_lastname' => 'TERIEUR',
                'billing_address_address' => '1 rue des fleurs',
                'billing_address_postal_code' => '44100',
                'billing_address_city' => 'NANTES',
                'billing_address_country_id' => 67,
                'billing_address_phone' => '0606060606',
                'billing_address_cellphone' => '0606060607',
                'billing_address_email' => '<EMAIL>',
                'billing_vat_number' => null,
                'shipping_address_company_name' => '',
                'shipping_address_civility' => 'M.',
                'shipping_address_firstname' => 'Laure',
                'shipping_address_lastname' => 'DURE',
                'shipping_address_address' => '111 route de paris',
                'shipping_address_postal_code' => '44000',
                'shipping_address_city' => 'NANTES',
                'shipping_address_country_id' => 67,
                'shipping_address_phone' => '0707070707',
                'shipping_address_cellphone' => '0707070708',
                'shipping_address_email' => '<EMAIL>',
                'carrier_id' => 2,
                'shipment_method_id' => 1,
                'store_pickup_id' => null,
                'relay_id' => null,
                'svd_header' => true,
                'tags' => [
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::STATUS_IMPORT,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SITE_CHECKOUT_V2,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SOURCE_SHOP_PRO_SV,
                        'meta' => null,
                    ],
                ],
                'payments' => [
                    [
                        'customer_order_id' => 123,
                        'payment_mean' => 'CBS-O',
                        'unique_id' => 1,
                        'created_at' => '2022-01-01 00:00:00',
                        'amount' => 2362.99,
                        'created_proof' => '123-1',
                        'origin' => 'son-video.com',
                        'extra_data' => [],
                        'workflow' => 'legacy',
                    ],
                ],
                'products' => [
                    [
                        'customer_order_id' => 123,
                        'product_id' => 81078,
                        'type' => Product::TYPE_ARTICLE,
                        'quantity' => 2,
                        'selling_price_tax_included' => 1200.0,
                        'vat' => 0.2,
                        'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                        'discount_type' => 'devis',
                        'discount_amount' => -200.0,
                        'discount_description' => 'Remise devis',
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => 'devis',
                        'group_description' => 'Remise devis',
                        'warranty_duration_ext' => '5',
                        'warranty_unit_selling_price_ext' => 79.0,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'product_id' => Product::SHIPMENT_PRODUCT_ID,
                        'type' => Product::TYPE_GENERIQUE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 4.99,
                        'vat' => 0.2,
                        'description' => 'Frais de port facturés',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => null,
                        'group_description' => null,
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                ],
                'chrono_precise_appointment' => null,
                'is_intragroup' => false,
                'is_b2b' => true,
                'quote_creator_username' => 'gege',
                'is_excluding_tax' => 'non',
                'sales_channel_id' => SalesChannel::SON_VIDEO,
                'promotion_id' => 23,
            ])
        );
    }

    /** Tests mapping data successfully with a quote from the business department. */
    public function test_map_data_with_business_quote(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $payload['quote_id'] = 104;
        $result = $this->getTestedInstance()->map($payload);

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);

        $this->assertEquals(
            ArrayHelper::sortKeys($result->toArray()),
            ArrayHelper::sortKeys([
                'customer_order_id' => 123,
                'original_customer_order_id' => '123',
                'clone_customer_order_id' => null,
                'origin' => 'son-video.com',
                'internal_status' => 'traitement',
                'created_at' => '2022-01-01 00:00:00',
                'estimated_delivery_date' => null,
                'customer_id' => 1,
                'new_customer' => false,
                'quote_id' => 104,
                'warehouse_id' => 5,
                'invoice_comment' => '',
                'ip_address' => '***********',
                'billing_address_company_name' => '',
                'billing_address_civility' => 'M.',
                'billing_address_firstname' => 'Alain',
                'billing_address_lastname' => 'TERIEUR',
                'billing_address_address' => '1 rue des fleurs',
                'billing_address_postal_code' => '44100',
                'billing_address_city' => 'NANTES',
                'billing_address_country_id' => 67,
                'billing_address_phone' => '0606060606',
                'billing_address_cellphone' => '0606060607',
                'billing_address_email' => '<EMAIL>',
                'billing_vat_number' => null,
                'shipping_address_company_name' => '',
                'shipping_address_civility' => 'M.',
                'shipping_address_firstname' => 'Laure',
                'shipping_address_lastname' => 'DURE',
                'shipping_address_address' => '111 route de paris',
                'shipping_address_postal_code' => '44000',
                'shipping_address_city' => 'NANTES',
                'shipping_address_country_id' => 67,
                'shipping_address_phone' => '0707070707',
                'shipping_address_cellphone' => '0707070708',
                'shipping_address_email' => '<EMAIL>',
                'carrier_id' => 2,
                'shipment_method_id' => 1,
                'store_pickup_id' => null,
                'relay_id' => null,
                'svd_header' => true,
                'tags' => [
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::STATUS_IMPORT,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SITE_CHECKOUT_V2,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SOURCE_BUSINESS_SV,
                        'meta' => null,
                    ],
                ],
                'payments' => [
                    [
                        'customer_order_id' => 123,
                        'payment_mean' => 'CBS-O',
                        'unique_id' => 1,
                        'created_at' => '2022-01-01 00:00:00',
                        'amount' => 2362.99,
                        'created_proof' => '123-1',
                        'origin' => 'son-video.com',
                        'extra_data' => [],
                        'workflow' => 'legacy',
                    ],
                ],
                'products' => [
                    [
                        'customer_order_id' => 123,
                        'product_id' => 81078,
                        'type' => Product::TYPE_ARTICLE,
                        'quantity' => 2,
                        'selling_price_tax_included' => 1200.0,
                        'vat' => 0.2,
                        'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                        'discount_type' => 'devis',
                        'discount_amount' => -200.0,
                        'discount_description' => 'Remise devis',
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => 'devis',
                        'group_description' => 'Remise devis',
                        'warranty_duration_ext' => '5',
                        'warranty_unit_selling_price_ext' => 79.0,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'product_id' => Product::SHIPMENT_PRODUCT_ID,
                        'type' => Product::TYPE_GENERIQUE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 4.99,
                        'vat' => 0.2,
                        'description' => 'Frais de port facturés',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => null,
                        'group_description' => null,
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                ],
                'chrono_precise_appointment' => null,
                'is_intragroup' => false,
                'is_b2b' => false,
                'quote_creator_username' => null,
                'is_excluding_tax' => 'non',
                'sales_channel_id' => SalesChannel::SON_VIDEO,
                'promotion_id' => 23,
            ])
        );
    }

    /** Tests mapping data successfully with a promo code with linked products. */
    public function test_map_data_with_promo_code(): void
    {
        $result = $this->getTestedInstance()->map(CustomerOrderPayload::getValidPayloadFromSiteWithPromoCode());

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);

        $this->assertEquals(
            ArrayHelper::sortKeys($result->toArray()),
            ArrayHelper::sortKeys([
                'customer_order_id' => 123,
                'original_customer_order_id' => '123',
                'clone_customer_order_id' => null,
                'origin' => 'son-video.com',
                'internal_status' => 'traitement',
                'created_at' => '2022-01-01 00:00:00',
                'estimated_delivery_date' => null,
                'customer_id' => 1,
                'new_customer' => false,
                'quote_id' => null,
                'warehouse_id' => null,
                'invoice_comment' => '',
                'ip_address' => '***********',
                'billing_address_company_name' => '',
                'billing_address_civility' => 'M.',
                'billing_address_firstname' => 'Alain',
                'billing_address_lastname' => 'TERIEUR',
                'billing_address_address' => '1 rue des fleurs',
                'billing_address_postal_code' => '44100',
                'billing_address_city' => 'NANTES',
                'billing_address_country_id' => 67,
                'billing_address_phone' => '0606060606',
                'billing_address_cellphone' => '0606060607',
                'billing_address_email' => '<EMAIL>',
                'billing_vat_number' => null,
                'shipping_address_company_name' => '',
                'shipping_address_civility' => 'M.',
                'shipping_address_firstname' => 'Laure',
                'shipping_address_lastname' => 'DURE',
                'shipping_address_address' => '111 route de paris',
                'shipping_address_postal_code' => '44000',
                'shipping_address_city' => 'NANTES',
                'shipping_address_country_id' => 67,
                'shipping_address_phone' => '0707070707',
                'shipping_address_cellphone' => '0707070708',
                'shipping_address_email' => '<EMAIL>',
                'carrier_id' => 50,
                'shipment_method_id' => 65,
                'store_pickup_id' => null,
                'relay_id' => null,
                'svd_header' => true,
                'tags' => [
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::STATUS_IMPORT,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SITE_CHECKOUT_V2,
                        'meta' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SOURCE_WEB_SV,
                        'meta' => null,
                    ],
                ],
                'payments' => [
                    0 => [
                        'customer_order_id' => 123,
                        'payment_mean' => 'CBS-O',
                        'unique_id' => 1,
                        'created_at' => '2022-01-01 00:00:00',
                        'amount' => 862.0,
                        'created_proof' => '123-1',
                        'origin' => 'son-video.com',
                        'extra_data' => [],
                        'workflow' => 'legacy',
                    ],
                ],
                'products' => [
                    0 => [
                        'customer_order_id' => 123,
                        'product_id' => 81078,
                        'type' => Product::TYPE_ARTICLE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 249.0,
                        'vat' => 0.2,
                        'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.15,
                        'sorecop_price' => 0.0,
                        'group_type' => null,
                        'group_description' => null,
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                    1 => [
                        'customer_order_id' => 123,
                        'product_id' => 128416,
                        'type' => Product::TYPE_ARTICLE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 500.0,
                        'vat' => 0.2,
                        'description' => 'Enceinte encastrable BW CCM 7.4',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.15,
                        'sorecop_price' => 0.0,
                        'group_type' => 'promocode',
                        'group_description' => 'PUYDUFOU',
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => 'PUYDUFOU',
                    ],
                    2 => [
                        'customer_order_id' => 123,
                        'product_id' => 81123,
                        'type' => Product::TYPE_ARTICLE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 50.0,
                        'vat' => 0.2,
                        'description' => 'Paire de pieds noirs laqués pour station multimédia La Boîte Concept LD120 et LD130',
                        'discount_type' => 'commande',
                        'discount_amount' => -40.0,
                        'discount_description' => 'Remise exceptionnelle',
                        'ecotax_price' => 0.15,
                        'sorecop_price' => 0.0,
                        'group_type' => 'promocode',
                        'group_description' => 'PUYDUFOU',
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => 'PUYDUFOU',
                    ],
                    3 => [
                        'customer_order_id' => 123,
                        'product_id' => Product::SHIPMENT_PRODUCT_ID,
                        'type' => Product::TYPE_GENERIQUE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 0.0,
                        'vat' => 0.2,
                        'description' => 'Frais de port facturés',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => null,
                        'group_description' => null,
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                ],
                'chrono_precise_appointment' => null,
                'is_intragroup' => false,
                'is_b2b' => false,
                'quote_creator_username' => null,
                'is_excluding_tax' => 'non',
                'sales_channel_id' => SalesChannel::SON_VIDEO,
                'promotion_id' => 23,
            ])
        );
    }

    /** Tests mapping data successfully with a CMS payment mean TEL. */
    public function test_map_data_with_tel_payment(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $payload['payments'][0]['payment_mean'] = 'TEL';
        $result = $this->getTestedInstance()->map($payload);

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);
        $this->assertEquals(
            [
                [
                    'customer_order_id' => 123,
                    'payment_mean' => 'CBS-OT',
                    'unique_id' => 1,
                    'created_at' => '2022-01-01 00:00:00',
                    'amount' => 2362.99,
                    'created_proof' => '123-1',
                    'origin' => 'son-video.com',
                    'extra_data' => [],
                    'workflow' => 'legacy',
                ],
            ],
            $result->toArray()['payments']
        );
    }

    /** Tests mapping with floating discounts. */
    public function test_map_with_floating_discounts(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromSiteWithPromoCode();
        $payload['products'][] = [
            'type' => 'article',
            'sku' => 'NORSTCL2501M',
            'quantity' => 1,
            'description' => 'Some AAAAA',
            'ecotax_price' => 0.15,
            'sorecop_price' => 0,
            'selected_warranties' => [],
            'unit_discount_amount' => -40.0,
            'selling_price_tax_included' => 50.0,
            'promo_code' => null,
        ];
        $payload['products'][] = [
            'type' => 'compose',
            'sku' => 'NORSTCL25025M',
            'quantity' => 1,
            'description' => 'BBBBBB thing',
            'ecotax_price' => 0.15,
            'sorecop_price' => 0,
            'selected_warranties' => [],
            'unit_discount_amount' => -40.0,
            'selling_price_tax_included' => 50.0,
            'promo_code' => null,
        ];
        $payload['products'][0]['unit_discount_amount'] = 0;
        $payload['products'][1]['unit_discount_amount'] = -6.897;
        $payload['products'][2]['unit_discount_amount'] = -5.397;
        $payload['products'][3]['unit_discount_amount'] = -5.097;
        $payload['products'][4]['unit_discount_amount'] = 0;

        $result = $this->getTestedInstance()->map($payload);

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);

        $amounts = array_column($result->toArray()['products'], 'discount_amount');
        $this->assertEqualsWithDelta(0.0, $amounts[0], 0.01);
        $this->assertEqualsWithDelta(-6.89, $amounts[1], 0.01);
        $this->assertEqualsWithDelta(-5.4, $amounts[2], 0.01);
        $this->assertEqualsWithDelta(-5.1, $amounts[3], 0.01);
        $this->assertEqualsWithDelta(0.0, $amounts[4], 0.01);
        $this->assertEqualsWithDelta(0.0, $amounts[5], 0.01);
    }
}
