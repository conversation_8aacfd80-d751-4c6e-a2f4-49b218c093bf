<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrder\Manager\CreationDataMapper;

use App\Exception\NotFoundException;
use App\Tests\Mock\Erp\CustomerOrder\CustomerOrderPayload;
use App\Tests\Utils\ArrayHelper;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderCreationContextEntity;
use SonVideo\Erp\CustomerOrder\Manager\CreationDataMapper\CiloCreationDataMapper as TestedClass;
use SonVideo\Erp\Referential\CustomerOrderOrigin;
use SonVideo\Erp\Referential\CustomerOrderTag;
use SonVideo\Erp\Referential\Product;
use SonVideo\Erp\Referential\SalesChannel;
use <PERSON>ymfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CiloCreationDataMapperTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/rpc/creator.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** Tests the mapping functionality. */
    public function test_convert(): void
    {
        $result = $this->getTestedInstance()->map(CustomerOrderPayload::getValidPayloadFromCilo());

        $this->assertInstanceOf(CustomerOrderCreationContextEntity::class, $result);
        $this->assertEquals(
            ArrayHelper::sortKeys([
                'customer_order_id' => 123,
                'original_customer_order_id' => '*********',
                'clone_customer_order_id' => null,
                'origin' => CustomerOrderOrigin::CILO,
                'internal_status' => 'traitement',
                'created_at' => '2019-07-18 00:00:00',
                'estimated_delivery_date' => null,
                'customer_id' => 1518400,
                'new_customer' => false,
                'quote_id' => null,
                'warehouse_id' => null,
                'invoice_comment' => '',
                'ip_address' => '************',
                'billing_address_company_name' => 'CILO',
                'billing_address_civility' => 'M.',
                'billing_address_firstname' => 'Karsten',
                'billing_address_lastname' => 'Holst',
                'billing_address_address' => 'Birk Centerpark 40',
                'billing_address_postal_code' => '7400',
                'billing_address_city' => 'Herning',
                'billing_address_country_id' => 52,
                'billing_address_phone' => '',
                'billing_address_cellphone' => '004553801044',
                'billing_address_email' => '<EMAIL>',
                'billing_vat_number' => 'NUMTVA',
                'shipping_address_company_name' => '',
                'shipping_address_civility' => 'M.',
                'shipping_address_firstname' => 'Søren G.',
                'shipping_address_lastname' => 'Hansen',
                'shipping_address_address' => 'Strandlyst Alle 3 B',
                'shipping_address_postal_code' => '02670',
                'shipping_address_city' => 'Greve',
                'shipping_address_country_id' => 5,
                'shipping_address_phone' => '',
                'shipping_address_cellphone' => '+4522604660',
                'shipping_address_email' => '<EMAIL>',
                'carrier_id' => 2,
                'shipment_method_id' => 1,
                'store_pickup_id' => null,
                'relay_id' => null,
                'svd_header' => false,
                'tags' => [
                    [
                        'customer_order_id' => 123,
                        'tag_id' => CustomerOrderTag::SOURCE_INTRAGROUP,
                        'meta' => null,
                    ],
                ],
                'payments' => [
                    [
                        'customer_order_id' => 123,
                        'payment_mean' => 'CILO',
                        'unique_id' => 1,
                        'created_at' => '2019-07-18 00:00:00',
                        'amount' => 1000.0,
                        'created_proof' => '*********-1',
                        'origin' => CustomerOrderOrigin::CILO,
                        'extra_data' => [],
                        'workflow' => 'legacy',
                    ],
                ],
                'products' => [
                    [
                        'customer_order_id' => 123,
                        'product_id' => 81078,
                        'type' => Product::TYPE_ARTICLE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 1200.0,
                        'vat' => 0.2,
                        'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => null,
                        'group_description' => null,
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'product_id' => 81123,
                        'type' => Product::TYPE_ARTICLE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 40.0,
                        'vat' => 0.2,
                        'description' => 'Paire de supports d\'enceintes B-Tech Mountlogic BT77 Noir',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => null,
                        'group_description' => null,
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                    [
                        'customer_order_id' => 123,
                        'product_id' => Product::SHIPMENT_PRODUCT_ID,
                        'type' => Product::TYPE_GENERIQUE,
                        'quantity' => 1,
                        'selling_price_tax_included' => 0.0,
                        'vat' => 0.2,
                        'description' => 'Frais de port facturés',
                        'discount_type' => null,
                        'discount_amount' => 0.0,
                        'discount_description' => null,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'group_type' => null,
                        'group_description' => null,
                        'warranty_duration_ext' => null,
                        'warranty_unit_selling_price_ext' => null,
                        'warranty_duration_tb' => null,
                        'warranty_unit_selling_price_tb' => null,
                        'promo_code' => null,
                    ],
                ],
                'chrono_precise_appointment' => null,
                'is_intragroup' => false,
                'is_b2b' => false,
                'quote_creator_username' => null,
                'is_excluding_tax' => 'oui',
                'sales_channel_id' => SalesChannel::CILO,
                'promotion_id' => null,
            ]),
            ArrayHelper::sortKeys($result->toArray())
        );
    }

    /** Tests that mapping fails if SKU is not found. */
    public function test_fail_mapping_if_sku_not_found(): void
    {
        $payload = CustomerOrderPayload::getValidPayloadFromCilo();
        $payload['products'][0]['sku'] = 'TOTO';

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Product not found with id or sku "TOTO".');
        $this->getTestedInstance()->map($payload);
    }
}
