<?php

namespace PHPUnit\Unit\Erp\Competitor\Manager;

use League\Flysystem\FilesystemInterface;
use League\Flysystem\MountManager;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\Competitor\Manager\WiserCatalogExporter;
use SonVideo\Erp\Competitor\Mysql\Repository\CatalogExporterRepository;
use SonVideo\Erp\Filesystem\Manager\ExportedFile;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class WiserCatalogExporterTest extends KernelTestCase
{
    private ExportedFile $exported_file;
    private CatalogExporterRepository $catalog_exporter_repository;
    private FilesystemInterface $wiser_ftp_filesystem;
    private FilesystemInterface $exported_file_filesystem;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->exported_file = self::$container->get(ExportedFile::class);
        $this->catalog_exporter_repository = $this->createMock(CatalogExporterRepository::class);
        $this->logger = self::$container->get(LoggerInterface::class);

        // Clear filesystems
        $this->wiser_ftp_filesystem = self::$container->get(MountManager::class)->getFilesystem('wiser_ftp_filesystem');
        $this->exported_file_filesystem = $this->exported_file->getFilesystem();

        $this->clearFilesystem($this->wiser_ftp_filesystem);
        $this->clearFilesystem($this->exported_file_filesystem);
    }

    private function clearFilesystem(FilesystemInterface $filesystem): void
    {
        $content = $filesystem->listContents('/', true);
        foreach (array_filter($content, static fn ($content_data): bool => 'file' === $content_data['type']) as $file) {
            $filesystem->delete($file['path']);
        }
    }

    private function getTestedInstance(): WiserCatalogExporter
    {
        $exporter = new WiserCatalogExporter(
            $this->exported_file,
            $this->catalog_exporter_repository,
            self::$container->get(MountManager::class)
        );
        $exporter->setLogger($this->logger);

        return $exporter;
    }

    public function test_export_successfully(): void
    {
        // Mock the repository to return a generator with test data
        $test_data = [
            [
                'universe' => 'Test Universe',
                'subcategory_name' => 'Test Category',
                'segment1' => 'Test Segment1',
                'segment2' => 'Test Segment2',
                'segment3' => 'Test Segment3',
                'brand_name' => 'Test Brand',
                'title' => 'Test Title',
                'sku' => 'TEST123',
                'colour' => 'Black',
                'pvgc' => '199.99',
                'ean' => '1234567890123',
                'selling_price' => '149.99',
            ],
        ];

        $generator = (function () use ($test_data) {
            foreach ($test_data as $item) {
                yield $item;
            }
        })();

        $this->catalog_exporter_repository
            ->expects($this->once())
            ->method('fetchAllWithGenerator')
            ->willReturn($generator);

        // Execute the export method
        $this->getTestedInstance()->export();

        $generated_files = array_values(
            array_filter(
                $this->exported_file_filesystem->listContents(''),
                static fn ($content_data): bool => 'file' === $content_data['type']
            )
        );
        $this->assertCount(1, $generated_files, 'The exported file should be written to the wiser FTP filesystem');

        // We don't verify the exact content because it's a binary UTF-16 encoded CSV file
        // Instead, we check that the file exists and has content
        $this->assertGreaterThan(
            0,
            strlen($this->wiser_ftp_filesystem->read($generated_files[0]['path'])),
            'The exported file should have content'
        );
    }

    public function test_get_export_name_format(): void
    {
        $exporter = $this->getTestedInstance();

        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($exporter);
        $method = $reflection->getMethod('getExportName');
        $method->setAccessible(true);

        $export_name = $method->invoke($exporter);

        // Test that the export name follows the expected format
        $this->assertMatchesRegularExpression(
            '/^Export_catalog_wiser_\d{12}\.csv$/',
            $export_name,
            'The export name should match the expected format'
        );
    }
}
