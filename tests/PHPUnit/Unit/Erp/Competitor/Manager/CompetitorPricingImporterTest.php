<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Competitor\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\ConnectionProvider\MysqlErpConnectionProvider;
use App\Database\Orm\MysqlErp\Repository\CompetitorPricingCompetitorCodeMappingRepository;
use App\Database\Orm\MysqlErp\Repository\CompetitorPricingRepository;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use League\Flysystem\MountManager;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\Competitor\Manager\CompetitorPricingImporter;
use SonVideo\Orm\QueryBuilder;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CompetitorPricingImporterTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'competitor/competitors.sql', 'competitor/article_ean.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();

        // clear filesystems
        $mount_manager = self::$container->get(MountManager::class);
        foreach (['wiser_ftp_svd_filesystem', 'wiser_backup_filesystem'] as $fs_to_clean) {
            $fs = $mount_manager->getFilesystem($fs_to_clean);
            $content = $fs->listContents('/', true);

            foreach (
                array_filter($content, static fn ($content_data): bool => 'file' === $content_data['type'])
                as $file
            ) {
                $fs->delete($file['path']);
            }
        }

        self::$container
            ->get(MysqlErpConnectionProvider::class)
            ->getConnection()
            ->executeQuery('TRUNCATE TABLE backOffice.competitor_pricing');

        MySqlDatabase::loadSpecificFixtures(['competitor/competitor_pricing.sql']);

        // make sure that all current records are expired
        self::$container->get(CompetitorPricingRepository::class)->updateWhere(
            [
                'crawled_at' => (new \DateTime('5 days ago'))->format('Y-m-d H:i:s'),
            ],
            'TRUE',
            []
        );

        // make sure that one record will be updated
        self::$container->get(CompetitorPricingRepository::class)->updateWhere(
            [
                'sku' => 'ABC4894019',
            ],
            'ean = :ean AND competitor_code = :competitor_code',
            [
                'ean' => '0017817846141',
                'competitor_code' => 'FNAC',
            ]
        );

        // make sure that one record will is not expired (but no match on ean)
        self::$container->get(CompetitorPricingRepository::class)->updateWhere(
            [
                'sku' => null,
                'crawled_at' => (new \DateTime('35 hour ago'))->format('Y-m-d H:i:s'),
            ],
            'ean = :ean AND competitor_code = :competitor_code',
            [
                'ean' => '0017817846141',
                'competitor_code' => 'GETGOODS',
            ]
        );
    }

    private function getTestedInstance(): CompetitorPricingImporter
    {
        $instance = new CompetitorPricingImporter(
            self::$container->get(CompetitorPricingCompetitorCodeMappingRepository::class),
            self::$container->get(MountManager::class),
            self::$container->get(CompetitorPricingRepository::class)
        );

        $instance->setSerializer(self::$container->get(SerializerInterface::class));
        $instance->setLogger(self::$container->get(LoggerInterface::class));

        return $instance;
    }

    private function getWiserContent(): string
    {
        $content = self::$container
            ->get(MountManager::class)
            ->getFilesystem('mock_filesystem')
            ->read('wiser/wiser.xml');

        return strtr($content, [
            '{VALID_DATE}' => (new \DateTime('yesterday 8:00'))->format('d/m/y H:i:s'),
        ]);
    }

    public function test_import_successfully(): void
    {
        self::$container
            ->get(MountManager::class)
            ->getFilesystem('wiser_ftp_svd_filesystem')
            ->write(
                sprintf('temp_SON-VIDEO_%s.xml', (new \DateTime('yesterday 1:00'))->format('HdmY')),
                $this->getWiserContent()
            );

        // records before
        $this->assertCount(
            15,
            self::$container->get(CompetitorPricingRepository::class)->findWhere(new QueryBuilder())
        );

        $importer = $this->getTestedInstance();
        $importer->import();

        $records = self::$container->get(CompetitorPricingRepository::class)->findWhere(new QueryBuilder());

        // total records
        $this->assertCount(3, $records);

        // the upserted record
        $this->assertCount(
            1,
            array_filter(
                $records,
                static fn ($record): bool => 'ABC4894019' === $record->sku &&
                    '0008634894019' === $record->ean &&
                    'COBRA' === $record->competitor_code
            )
        );

        // the non expired record
        $this->assertCount(
            1,
            array_filter($records, static fn ($record): bool => 'GETGOODS' === $record->competitor_code)
        );

        // non-matched ean
        $this->assertCount(1, array_filter($records, static fn ($record): bool => null === $record->sku));

        // matched ean
        $this->assertCount(2, array_filter($records, static fn ($record): bool => null !== $record->sku));

        // file backed up successfully
        $this->assertTrue(
            self::$container
                ->get(MountManager::class)
                ->getFilesystem('wiser_backup_filesystem')
                ->has(
                    sprintf(
                        '/SVD_%s__temp_SON-VIDEO_%s.xml',
                        (new \DateTime('now'))->format('Ymd'),
                        (new \DateTime('yesterday 1:00'))->format('HdmY')
                    )
                )
        );
    }

    public function test_import_no_files_found(): void
    {
        $importer = $this->getTestedInstance();

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('No file to import found.');

        $importer->import();
    }

    public function test_import_fails_with_invalid_xml_string(): void
    {
        self::$container
            ->get(MountManager::class)
            ->getFilesystem('wiser_ftp_svd_filesystem')
            ->write(
                sprintf('temp_SON-VIDEO_%s.xml', (new \DateTime('yesterday 1:00'))->format('HdmY')),
                'NO XML CONTENT'
            );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('An error occurred while parsing the XML provided by Wiser');

        $this->getTestedInstance()->import();
    }

    public function test_import_when_there_are_several_files_should_use_the_latest(): void
    {
        $wiser_fs = self::$container->get(MountManager::class)->getFilesystem('wiser_ftp_svd_filesystem');

        $wiser_fs->write(
            sprintf('temp_SON-VIDEO_%s.xml', (new \DateTime('yesterday 1:00'))->format('HdmY')),
            $this->getWiserContent()
        );
        $wiser_fs->write(
            sprintf('temp_SON-VIDEO_%s.xml', (new \DateTime('yesterday 8:00'))->format('HdmY')),
            $this->getWiserContent()
        );
        $wiser_fs->write(
            sprintf('temp_SON-VIDEO_%s.xml', (new \DateTime('yesterday 11:00'))->format('HdmY')),
            $this->getWiserContent()
        );
        $wiser_fs->write(
            sprintf('temp_SON-VIDEO_%s.xml', (new \DateTime('yesterday 14:00'))->format('HdmY')),
            $this->getWiserContent()
        );

        $this->getTestedInstance()->import();

        // Latest file has been deleted
        $this->assertCount(3, $wiser_fs->listContents('/', true));

        // file backed up successfully
        $backup_fs = self::$container->get(MountManager::class)->getFilesystem('wiser_backup_filesystem');

        $this->assertCount(1, $backup_fs->listContents('/', true));
        $this->assertTrue(
            $backup_fs->has(
                sprintf(
                    '/SVD_%s__temp_SON-VIDEO_%s.xml',
                    (new \DateTime('now'))->format('Ymd'),
                    (new \DateTime('yesterday 14:00'))->format('HdmY')
                )
            )
        );
    }
}
