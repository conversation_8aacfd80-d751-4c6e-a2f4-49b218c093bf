<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\CustomerOrderConfirmation;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\CustomerOrderConfirmation\CustomerOrderConfirmationEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderConfirmationEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): CustomerOrderConfirmationEmailDispatcher
    {
        return self::$container->get(CustomerOrderConfirmationEmailDispatcher::class);
    }

    public function test_dispatch_with_invalid_payload(): void
    {
        $invalid_request_payload = <<<JSON
        {
        	"to": "bad email",
        	"context": {
        		"customer_order_id": "test",
        		"created_at": 123,
        		"customer": {
        			"customer_id": "zed",
        			"civility": null,
        			"firstname": null,
        			"lastname": null,
        			"email": "test",
        			"company": "",
        			"postal_code": 1234,
        			"city": null,
        			"country_name": null,
        			"phone": null,
        			"office_phone": 1231,
        			"mobile_phone": 1231
        		},
        		"delivery_address": {
        			"company": 123,
        			"firstname": 123,
        			"lastname": 123,
        			"civility": 123,
        			"address": 123,
        			"postal_code": 123,
        			"city": 123,
        			"country_name": 123,
        			"phone": 123,
        			"office_phone": "",
        			"mobile_phone": "",
        			"pickup_store_id": "test",
        			"store": null
        		},
        		"is_quotation_for_abroad_delivery": "test",
        		"articles": [{
        			"sku": 123,
        			"quantity": "123",
        			"description": null,
        			"selling_price": "test",
        			"picture": null,
        			"warranty_duration": "test",
        			"warranty_price": "test"
        		}],
        		"prices": {
        			"discount_amount": "test",
        			"delivery_fees": "test",
        			"extra_cost": "test",
        			"selling_price_tax_included": "test"
        		},
        		"payments": [
        		  {
        			"description": null,
        			"amount": "test"
        		  }
                ]
        	},
        	"_sent_by": "dsfsd",
        	"_rel": {
        		"customer": null
        	}
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][customer_order_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][created_at]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][customer_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][civility]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][civility]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][firstname]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][lastname]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][email]: This value is not a valid email address.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][postal_code]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][city]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][city]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][country_name]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][country_name]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][office_phone]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][mobile_phone]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][company]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][firstname]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][lastname]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][civility]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][address]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][postal_code]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][city]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][country_name]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][phone]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][pickup_store_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_address][store]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][is_quotation_for_abroad_delivery]: This value should be of type boolean.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][sku]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][quantity]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][description]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][selling_price]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][picture]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][warranty_duration]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][warranty_price]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][discount_amount]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][delivery_fees]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][extra_cost]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][selling_price_tax_included]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][payments][0][description]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][payments][0][amount]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_sent_by]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString('[_rel][customer_order]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_valid_payload(): void
    {
        $valid_request_payload = <<<JSON
        {
            "to": "<EMAIL>",
            "context": {
                "customer_order_id": 12345,
        		"created_at": "27/12/2021",
        		"customer": {
        		    "customer_id": 123456,
                    "civility": "Mr.",
                    "firstname": "Gerard",
                    "lastname": "Menvussa",
                    "email": "<EMAIL>",
                    "company": null,
                    "postal_code": "44000",
                    "city": "Nantes",
                    "country_name": "France",
                    "phone": "0601020304",
                    "office_phone": null,
                    "mobile_phone": null
        		},
        		"delivery_address": {
                "company": null,
                "firstname": "Gerard",
                "lastname": "Menvussa",
                "civility": "Mr.",
                "address": "36 rue de la ville en bois",
                "postal_code": "44000",
                "city": "Nantes",
                "country_name": "France",
                "phone": "0601020304",
                "office_phone": null,
                "mobile_phone": null,
                "pickup_store_id": null,
                "store": {
                  "city": null,
                  "address": null,
                  "postal_code": null,
                  "url": null
                }
              },
              "is_quotation_for_abroad_delivery": true,
              "articles": [
                {
                  "sku": "EPH100",
                  "quantity": 10,
                  "description": "ceci est un article de test",
                  "selling_price": 100.00,
                  "picture": "/images/EPH100.jpg",
                  "warranty_duration": null,
                  "warranty_price": 0.00
                }
              ],
              "prices": {
                "discount_amount": 0.00,
                "delivery_fees": 5.90,
                "extra_cost": 0.00,
                "selling_price_tax_included": 105.90
              },
              "payments": [
                {
                  "description": "Virement bancaire",
                  "amount": 105.90
                }
              ]
            },
            "_sent_by": 1000,
        	"_rel": {
        		"customer": 123456,
        		"customer_order": 12345
        	}
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }
}
