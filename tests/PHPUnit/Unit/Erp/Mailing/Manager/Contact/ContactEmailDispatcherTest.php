<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\Contact;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\Contact\ContactEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ContactEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): ContactEmailDispatcher
    {
        return self::$container->get(ContactEmailDispatcher::class);
    }

    public function test_dispatch_with_invalid_payload_first_case(): void
    {
        $invalid_request_payload = <<<JSON
        {
          "to": "bad email",
          "context": {},
          "_rel": {
            "customer": null,
            "customer_order": null
          }
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString('[context]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][customer]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][customer_order]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer_order]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_sent_by]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_invalid_payload_second_case(): void
    {
        $invalid_request_payload = <<<JSON
        {
          "to": "bad email",
          "context": {
            "subject": null,
            "content": null
          },
          "_rel": {}
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][subject]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][subject]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][content]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][content]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_sent_by]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_valid_payload(): void
    {
        // @info This request is usable in your REST client to test if sending an email with mailjet is working properly
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "from": {
            "name": "Son-Video.com",
            "email": "<EMAIL>"
          },
          "context": {
            "subject": "Votre commande 12345678 Son-Vidéo.com",
            "content": "Bonjour,<br/><br/>Comment allez-vous aujourd'hui&nbsp;?"
          },
          "_rel": {
            "customer": 970481,
            "customer_order": 12345678
          },
          "_sent_by": 1300
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }
}
