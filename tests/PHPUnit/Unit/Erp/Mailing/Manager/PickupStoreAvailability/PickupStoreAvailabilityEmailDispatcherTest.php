<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\PickupStoreAvailability;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\PickupStoreAvailability\PickupStoreAvailabilityEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PickupStoreAvailabilityEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): PickupStoreAvailabilityEmailDispatcher
    {
        return self::$container->get(PickupStoreAvailabilityEmailDispatcher::class);
    }

    public function test_dispatch_with_invalid_payload(): void
    {
        $invalid_request_payload = <<<JSON
        {
            "to": "bad email",
            "context": {
                "customer_order_id": "test",
                "created_at": 123,
                "customer": {
                    "civility": null,
                    "firstname": null,
                    "lastname": null
                },
                "store": {
                    "name": null,
                    "city": null,
                    "address": null,
                    "postal_code": null,
                    "url": null
                },
                "articles": [
                    {
                        "sku": 1234,
                        "quantity": "test",
                        "description": null,
                        "picture": null
                    }
                ]
            },
            "_rel": {
                "customer_order": "test"
            }
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][customer_order_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][created_at]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][civility]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][civility]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][firstname]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][lastname]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][name]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][name]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][city]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][city]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][address]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][address]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][postal_code]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][postal_code]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][url]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][store][url]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][sku]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][quantity]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][description]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][picture]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer_order]: This value should be of type integer.',
            $exception->getMessage()
        );
    }

    public function test_dispatch_with_valid_payload(): void
    {
        $valid_request_payload = <<<JSON
        {
            "to": "<EMAIL>",
            "context": {
                "customer_order_id": 12345,
                "created_at": "27/12/2021",
                "customer": {
                    "civility": "Mr.",
                    "firstname": "Gerard",
                    "lastname": "Menvussa"
                },
                "store": {
                    "name": "magasin (Nantes)",
                    "city": "Nantes",
                    "address": "9 place de la bourse",
                    "postal_code": "44100",
                    "url": "https://www.son-video.com/magasin-hifi-home-cinema/nantes"
                },
                "articles": [
                    {
                        "sku": "EPH100",
                        "quantity": 10,
                        "description": "ceci est un article de test",
                        "picture": "/images/EPH100.jpg"
                    }
                ]
            },
            "_rel": {
                "customer_order": 12345
            }
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }
}
