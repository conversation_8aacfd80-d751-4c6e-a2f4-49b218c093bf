<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\External;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use League\Flysystem\MountManager;
use SonVideo\Erp\Filesystem\Manager\TemporaryFile;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\External\ExternalEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ExternalEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): ExternalEmailDispatcher
    {
        return self::$container->get(ExternalEmailDispatcher::class);
    }

    public function test_dispatch_with_invalid_payload(): void
    {
        $invalid_request_payload = <<<JSON
        {
          "to": [
            {
              "email": "bad email"
            }
          ],
          "from": {
            "email": "bad email",
            "not_a_field": "any"
          },
          "cc": [
            {
              "email": "bad email"
            }
          ],
          "context": { },
          "_rel": {
            "suppppplier": null
          },
          "_sent_by": null,
          "_attachments": { }
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString(
            '[to][0][email]: This value is not a valid email address.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[from][email]: This value is not a valid email address.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[from][name]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[from][not_a_field]: This field was not expected.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[cc][0][email]: This value is not a valid email address.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[context][subject]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][content]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][suppppplier]: This field was not expected.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_sent_by]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[_sent_by]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString('[_attachments]: This value should not be blank.', $exception->getMessage());
    }

    public function test_dispatch_with_valid_payload(): void
    {
        $this->thereIsATemporaryFileNamed('affiliation.csv', 'affiliation.csv');

        $valid_request_payload = <<<JSON
        {
          "to": [
            {
              "email": "<EMAIL>"
            }
          ],
          "from": {
            "name": "Son-Video.com",
            "email": "<EMAIL>",
            "reply_to": "<EMAIL>"
          },
          "context": {
            "subject": "Je suis un sujet",
            "content": "Je suis un contenue"
          },
          "_rel": {
            "supplier": 123
          },
          "_sent_by": 1300,
          "_attachments": [
            {
              "filesystem": "default_filesystem",
              "file_path": "affiliation.csv",
              "file_name": "affiliation.csv",
              "mime_type": "text/csv"
            }
          ]
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }

    private function thereIsATemporaryFileNamed(string $target_name, string $source_name): void
    {
        /** @var MountManager $mount_manager */
        $mount_manager = self::$container->get(MountManager::class);
        $source_fs = $mount_manager->getFilesystem('mock_filesystem');
        $target_fs = $mount_manager->getFilesystem(TemporaryFile::FILESYSTEM);

        $content = $source_fs->read($source_name);
        if ($target_fs->has($target_name)) {
            $target_fs->delete($target_name);
        }

        $target_fs->write($target_name, $content);
    }
}
