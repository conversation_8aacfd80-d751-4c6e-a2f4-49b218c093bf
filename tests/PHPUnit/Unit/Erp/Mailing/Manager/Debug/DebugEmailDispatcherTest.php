<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\Debug;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\Debug\DebugEmailDispatcher as DebugEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class DebugEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): DebugEmailDispatcher
    {
        return self::$container->get(DebugEmailDispatcher::class);
    }

    public function test_dispatch_with_invalid_payload(): void
    {
        $invalid_request_payload = <<<JSON
        {
          "to": "bad email",
          "context": {
            "articles": [
              {
                "name": 123,
                "description": null,
                "price": "1 099,00 €",
                "quantity": 1,
                "image": "https://dfxqtqxztmxwe.cloudfront.net/images/article/naim-audio/NAIMMUSOQB2NR/mu-so-qb-2_5d6e602c44fb7_300_square.jpg"
              },
              {
                "name": "JBL Charge 5 Noir ",
                "description": "Enceintes Bluetooth portables",
                "price": 199,
                "quantity": "1"
              }
            ],
            "total_price": ""
          },
          "_rel": {
            "customer": null
          }
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][articles][0][name]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][description]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][description]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][1][price]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][1][quantity]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][1][image]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][total_price]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString('[_rel][invoice]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_valid_payload(): void
    {
        // @info This request is usable in your REST client to test if sending an email with mailjet is working properly
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "context": {
            "articles": [
              {
                "name": "Naim mu-so Qb 2",
                "description": "Enceintes connectées",
                "price": "1 099,00 €",
                "quantity": 1,
                "image": "https://dfxqtqxztmxwe.cloudfront.net/images/article/naim-audio/NAIMMUSOQB2NR/mu-so-qb-2_5d6e602c44fb7_300_square.jpg"
              },
              {
                "name": "JBL Charge 5 Noir ",
                "description": "Enceintes Bluetooth portables",
                "price": "199,00 €",
                "quantity": 1,
                "image": "https://dfxqtqxztmxwe.cloudfront.net/images/article/jbl/JBLCHARGE5NR/charge-5-noir_60a6721a89062_300_square.jpg"
              }
            ],
            "total_price": "1 298,00 €"
          },
          "_rel": {
            "customer": 970481,
            "invoice": 1385939
          }
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);

        $last_call = RpcClientServiceMock::getLastCall(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD
        );

        $this->assertEquals(
            [['email' => '<EMAIL>'], ['email' => '<EMAIL>', 'name' => 'Jo Joba']],
            $last_call['args']['bcc']
        );

        $this->assertEquals(
            'Message de test envoyé par Herald via ERP SERVER. Sans la facture en pièce jointe (non trouvée)',
            $last_call['args']['context']['message']
        );
    }
}
