<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\Internal;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\Internal\InternalEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class InternalEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): InternalEmailDispatcher
    {
        return self::$container->get(InternalEmailDispatcher::class);
    }

    public function test_dispatch_with_invalid_payload(): void
    {
        $invalid_request_payload = <<<JSON
        {
          "to": "bad email",
          "from": {
            "email": "bad email",
            "reply_to": "<EMAIL>",
            "not_a_field": "any"
          },
          "context": {},
          "_rel": {
            "article": null
          }
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString(
            '[from][email]: This value is not a valid email address.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[from][name]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[from][not_a_field]: This field was not expected.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[_rel][article]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[_rel][article]: This value should not be null.', $exception->getMessage());
    }

    public function test_dispatch_with_valid_payload(): void
    {
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "from": {
            "name": "Son-Video.com",
            "email": "<EMAIL>",
            "reply_to": "<EMAIL>"
          },
          "context": {
            "subject": "Tout va bien",
            "content": "Bonjour,<br/><br/>Sachez que je suis content."
          },
          "_rel": {
            "article": 123
          },
          "_sent_by": 1300
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }
}
