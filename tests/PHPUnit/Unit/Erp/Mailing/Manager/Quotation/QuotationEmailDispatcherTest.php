<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\Quotation;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\Quotation\QuotationEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuotationEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): QuotationEmailDispatcher
    {
        return self::$container->get(QuotationEmailDispatcher::class);
    }

    public function test_dispatch_with_invalid_payload(): void
    {
        $invalid_request_payload = <<<JSON
        {
          "to": "bad email",
          "from" : {
              "email" : null
          },
          "context": {},
          "_rel": {
            "customer": null
          }
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString('[from][email]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[from][reply_to]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[context][quotation_id]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][link_to_account]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][link_to_quotations]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][comment]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][firstname]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][lastname]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][discount_amount]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][discount_amount_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][selling_price]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][selling_price_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][created_at]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][created_at_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][expire_at]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][expire_at_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][quotation_creator]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][quotation_creator_name]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_count]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[context][articles]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][customer]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString('[_sent_by]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_valid_payload(): void
    {
        // @info This request is usable in your REST client to test if sending an email with mailjet is working properly
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "from": {
            "name": "Son-Video.com",
            "email": "<EMAIL>",
            "reply_to": "<EMAIL>"
          },
          "context": {
            "copy": "[COPIE]",
            "quotation_id": 1234,
            "link_to_account": "https://son-video.com/mon-compte?autologin=auieauieauieauie",
            "link_to_quotations": "https://son-video.com/mon-compte/mes-devis?autologin=auieauieauieauie",
            "comment": "Suite à votre demande voici<br/>notre offre pour les produits suivants&nbsp;:",
            "firstname": "Bruce",
            "lastname": "Wayne",
            "discount_amount": 1200.86,
            "discount_amount_formatted": "1 200,86&nbsp;&euro;",
            "selling_price": 98500.86,
            "selling_price_formatted": "9 8500,86&nbsp;&euro;",
            "created_at": "2019-10-25 15:32:54",
            "created_at_formatted": "25/10/2019 15:32",
            "expire_at": "2019-11-24 15:32:54",
            "expire_at_formatted": "24/11/2019",
            "quotation_creator": 1300,
            "quotation_creator_name": "Joe Le Rigolo",
            "articles_count": 2,
            "articles": [
              {
                "sku":  "jesuisunsku",
                "name": "KEF LS50 Meta Bleu Special Edition",
                "description": "Enceintes bibliothèque",
                "unit_price": 99999.48,
                "unit_price_formatted": "99 999,48&nbsp;&euro;",
                "quantity": 1,
                "total_price": 99999,
                "total_price_formatted": "99 999,00&nbsp;&euro;",
                "image": "https://dfxqtqxztmxwe.cloudfront.net/images/article/kef/KEFLS50METABLMT/ls50-meta-bleu-royal-mat-special-edition_5f6862c8104e5_300_square.jpg",
                "url": "https://son-video.com",
                "warranty_5_yr_amount": 56,
                "warranty_5_yr_amount_formatted": "56&nbsp;&euro;",
                "warranty_1_yr_amount": null,
                "warranty_1_yr_amount_formatted": null,
                "warranty_2_yr_amount": null,
                "warranty_2_yr_amount_formatted": null,
                "message": null
              },
              {
                "sku": "jesuisunsku2",
                "name": "KEF LS50 Meta Bleu Special Edition",
                "description": "Enceintes bibliothèque",
                "message": null,
                "warranty_5_yr_amount": null,
                "warranty_5_yr_amount_formatted": null,
                "warranty_1_yr_amount": null,
                "warranty_1_yr_amount_formatted": null,
                "warranty_2_yr_amount": null,
                "warranty_2_yr_amount_formatted": null,
                "unit_price": null,
                "unit_price_formatted": null,
                "quantity": 1,
                "total_price": 99999,
                "total_price_formatted": "99 999,00nbsp;&euro;",
                "image": "https://dfxqtqxztmxwe.cloudfront.net/images/article/kef/KEFLS50METABLMT/ls50-meta-bleu-royal-mat-special-edition_5f6862c8104e5_300_square.jpg",
                "url": "https://son-video.com"
              }
            ]
          },
          "_rel": {
            "customer": 970481
          },
          "_sent_by": 1300
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }
}
