<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\CustomerOrderReplenishmentInProgress;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\CustomerOrderReplenishmentInProgress\CustomerOrderReplenishmentInProgressEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderReplenishmentInProgressEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): CustomerOrderReplenishmentInProgressEmailDispatcher
    {
        return self::$container->get(CustomerOrderReplenishmentInProgressEmailDispatcher::class);
    }

    public function test_dispatch_with_invalid_payload(): void
    {
        $invalid_request_payload = <<<JSON
        {
        	"to": "bad email",
        	"context": {
        		"customer_order_id": "test",
        		"ts": "test",
        		"customer": {
        			"customer_id": "zed",
        			"civility": null,
        			"firstname": null,
        			"lastname": null
        		},
        		 "delivery_time": {
                    "max": 123
                },
        		"count_articles_replenishment": "test",
        		"articles_replenishment": [{
        			"quantity": "123",
        			"description": null,
        			"url_image": 123,
        			"image_size": "123",
        			"estimated_delivery_date": 123,
        			"estimated_delivery_date_difference_in_days": "123"
        		}],
        		"prices": {
        			"discount_amount": "test",
        			"delivery_fees": "test",
        			"extra_cost": "test",
        			"selling_price_tax_included": "test"
        		}
        	},
        	"_rel": {
        		"customer": null,
        		"customer_order": "null"
        	}
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][customer_order_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][ts]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][customer_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][civility]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][civility]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][firstname]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][lastname]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_time][max]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][count_articles_replenishment]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_replenishment][0][sku]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_replenishment][0][quantity]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_replenishment][0][description]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_replenishment][0][description]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_replenishment][0][url_image]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_replenishment][0][image_size]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_replenishment][0][estimated_delivery_date]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles_replenishment][0][estimated_delivery_date_difference_in_days]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][discount_amount]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][discount_amount_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][delivery_fees]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][delivery_fees_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][extra_cost]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][extra_cost_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][selling_price_tax_included]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][selling_price_tax_included_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][discount_amount_without_delivery_fees]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][prices][discount_amount_without_delivery_fees_formatted]: This field is missing.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][customer_order]: This value should be of type integer.',
            $exception->getMessage()
        );
    }

    public function test_dispatch_with_valid_payload(): void
    {
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "context": {
            "customer_order_id": 12345,
            "ts": 12354,
            "customer": {
              "customer_id": 123456,
              "civility": "Mr.",
              "firstname": "Gerard",
              "lastname": "Menvussa"
            },
            "delivery_time": {
              "max": null
            },
            "count_articles_replenishment": 3,
            "articles_replenishment": [
              {
                "sku": "jesuisunsku",
                "quantity": 10,
                "description": "ceci est un article de test",
                "url_image": "http://www.son-video.com/images/static/Rayons/Hifi/Enceintes/Eltax/Millenium100.jpg",
                "image_size": 500,
                "estimated_delivery_date": "01/01/2011",
                "estimated_delivery_date_difference_in_days": 40
              }
            ],
            "prices": {
              "discount_amount": 0,
              "discount_amount_formatted": "0,00 €",
              "delivery_fees": 5.90,
              "delivery_fees_formatted": "5,90 €",
              "extra_cost": 0.0,
              "extra_cost_formatted": "0,00 €",
              "selling_price_tax_included": 105.90,
              "selling_price_tax_included_formatted": "105,90 €",
              "discount_amount_without_delivery_fees": 105.90,
              "discount_amount_without_delivery_fees_formatted": "105,90 €"
            }
          },
          "_rel": {
            "customer": 123456,
            "customer_order": 12345
          }
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }
}
