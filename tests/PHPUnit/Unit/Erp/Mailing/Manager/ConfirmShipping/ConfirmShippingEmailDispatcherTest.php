<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\ConfirmShipping;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use League\Flysystem\FileExistsException;
use League\Flysystem\MountManager;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\ConfirmShipping\ConfirmShippingEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ConfirmShippingEmailDispatcherTest extends KernelTestCase
{
    private const FIXTURE_FILESYSTEM = 'mock_filesystem';
    private const MOCKED_INVOICE_PATH = 'sonvideopro.com/data/backoffice/factures/2021/11/03';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): ConfirmShippingEmailDispatcher
    {
        return self::$container->get(ConfirmShippingEmailDispatcher::class);
    }

    /**
     * @throws FileExistsException
     * @throws EmailRequestPayloadException
     */
    public function test_dispatch_with_invalid_payload(): void
    {
        $invalid_request_payload = <<<JSON
        {
          "to": "bad email",
          "from": {
            "email": null
          },
          "subject": "",
          "context": {
            "customer_order": {
              "customer_order_id": "2149644",
              "origin_number": null,
              "invoice_id": "",
              "invoiced_at": "",
              "invoiced_at_formatted": ""
            },
            "customer": {
              "email": "",
              "is_regular": null
            },
            "delivery_note": {
              "validated_at": "",
              "validated_at_formatted": "",
              "carrier_id": null,
              "shipment_method_id": "15",
              "phone_number": null,
              "zip_code": null,
              "is_pick_up": ""
            },
            "parcel": {
              "tracking_number": "",
              "tracking_numbers": null,
              "total_tracking_numbers": "no",
              "tracking_france_express": null,
              "delivery_hours": {
                "start_day": 1,
                "start_hour": 2,
                "end_day": "",
                "end_hour": null
              }
            },
            "articles": [
              {
                "quantity": null,
                "description": "",
                "category_id": "56",
                "url_image": null,
                "sku": null,
                "image_width": "80cm"
              }
            ],
            "ts": "1640776335"
          },
          "_rel": {
            "customer_order": null,
            "delivery_note": "",
            "customer": null,
            "invoice": ""
          },
          "_sent_by": "1224"
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString('[from][email]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[subject]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][customer_order][customer_order_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer_order][origin_number]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer_order][origin_number]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer_order][invoice_id]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer_order][invoice_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer_order][invoiced_at]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer_order][invoiced_at_formatted]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer][email]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][validated_at]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][validated_at_formatted]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][carrier_id]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][carrier_id]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][shipment_method_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][phone_number]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][phone_number]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][zip_code]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][zip_code]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][delivery_note][is_pick_up]: This value should be of type boolean.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][parcel][total_tracking_numbers]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][parcel][delivery_hours][start_day]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][parcel][delivery_hours][start_hour]: This value should be of type string.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][parcel][delivery_hours][end_day]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][parcel][delivery_hours][end_hour]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][quantity]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][quantity]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][description]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][category_id]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][url_image]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][sku]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][sku]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][articles][0][image_width]: This value should be of type numeric.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][ts]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][invoice]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][invoice]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][delivery_note]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][delivery_note]: This value should be of type integer.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer_order]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer_order]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_sent_by]: This value should be of type integer.',
            $exception->getMessage()
        );
    }

    /**
     * @throws FileExistsException
     * @throws EmailRequestPayloadException
     */
    public function test_dispatch_with_valid_payload(): void
    {
        // @info This request is usable in your REST client to test if sending an email with mailjet is working properly
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "from": {
            "email": "<EMAIL>"
          },
          "subject": "Confirmation d'expédition - Son-Vidéo.com",
          "context": {
            "customer_order": {
              "customer_order_id": 2149644,
              "origin_number": "2149644",
              "invoice_id": 1559166,
              "invoiced_at": "2021-11-03",
              "invoiced_at_formatted": "03/11/2021"
            },
            "customer": {
              "email": "<EMAIL>",
              "is_regular": true
            },
            "delivery_note": {
              "validated_at": "2021-11-03 19:28:37",
              "validated_at_formatted": "03/11/2021",
              "carrier_id": 7,
              "shipment_method_id": 15,
              "phone_number": "0825 100 037",
              "zip_code": "13012",
              "is_pick_up": false
            },
            "parcel": {
              "tracking_number": "XP413745349FR",
              "tracking_numbers": [
                {
                  "index": 1,
                  "value": "XP413745349FR"
                }
              ],
              "total_tracking_numbers": 1,
              "tracking_france_express": "",
              "delivery_hours": {
                "start_day": "01/01/1970",
                "start_hour": "01:00",
                "end_day": "01/01/1970",
                "end_hour": "01:00"
              }
            },
            "articles": [
              {
                "quantity": 1,
                "description": "Baladeur audiophile Fiio M3K Noir",
                "category_id": 56,
                "url_image": "https://www.son-video.com/images/article/fiio/FIIOM3KNR/m3k-noir_5bd1a0665dfcf_600.jpg",
                "sku": "FIIOM3KNR",
                "image_width": 80
              }
            ],
            "ts": 1640776335
          },
          "_rel": {
            "customer_order": 2149644,
            "delivery_note": 4662940,
            "customer": 1592473,
            "invoice": 1559166
          },
          "_sent_by": 1224
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();
        $this->putMockedInvoiceFileInLegacyFilesystem();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }

    /**
     * @throws FileExistsException
     * @throws EmailRequestPayloadException
     */
    public function test_dispatch_with_no_loggable_event(): void
    {
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "from": {
            "email": "<EMAIL>"
          },
          "subject": "Confirmation d'expédition - Son-Vidéo.com",
          "context": {
            "customer_order": {
              "customer_order_id": 2149644,
              "origin_number": "2149644",
              "invoice_id": 1559166,
              "invoiced_at": "2021-11-03",
              "invoiced_at_formatted": "03/11/2021"
            },
            "customer": {
              "email": "<EMAIL>",
              "is_regular": true
            },
            "delivery_note": {
              "validated_at": "2021-11-03 19:28:37",
              "validated_at_formatted": "03/11/2021",
              "carrier_id": 7,
              "shipment_method_id": 15,
              "phone_number": "0825 100 037",
              "zip_code": "13012",
              "is_pick_up": false
            },
            "parcel": {
              "tracking_number": "XP413745349FR",
              "tracking_numbers": [
                {
                  "index": 1,
                  "value": "XP413745349FR"
                }
              ],
              "total_tracking_numbers": 1,
              "tracking_france_express": "",
              "delivery_hours": {
                "start_day": "01/01/1970",
                "start_hour": "01:00",
                "end_day": "01/01/1970",
                "end_hour": "01:00"
              }
            },
            "articles": [
              {
                "quantity": 1,
                "description": "Baladeur audiophile Fiio M3K Noir",
                "category_id": 56,
                "url_image": "https://www.son-video.com/images/article/fiio/FIIOM3KNR/m3k-noir_5bd1a0665dfcf_600.jpg",
                "sku": "FIIOM3KNR",
                "image_width": 80
              }
            ],
            "ts": 1640776335
          },
          "_rel": {
            "customer_order": 2149644,
            "delivery_note": 4662940,
            "customer": 1592473,
            "invoice": 1559166
          },
          "_sent_by": 1224,
          "_loggable_event": false
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertNull($result);
    }

    /** @throws FileExistsException */
    public function putMockedInvoiceFileInLegacyFilesystem(): void
    {
        /** @var MountManager $mount_manager */
        $mount_manager = self::$container->get('oneup_flysystem.mount_manager');

        $mocked_invoice = sprintf(
            '%s://%s/1559166.pdf',
            ConfirmShippingEmailDispatcher::LEGACY_FILESYSTEM_NAME,
            static::MOCKED_INVOICE_PATH
        );

        if (!$mount_manager->has($mocked_invoice)) {
            $mount_manager->copy(sprintf('%s://invoice/1559166.pdf', static::FIXTURE_FILESYSTEM), $mocked_invoice);
        }
    }
}
