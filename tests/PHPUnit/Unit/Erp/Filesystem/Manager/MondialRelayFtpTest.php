<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Filesystem\Manager;

use SonVideo\Erp\Filesystem\Manager\MondialRelayFtp;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class MondialRelayFtpTest extends KernelTestCase
{
    private const FIXTURES_DIR = __DIR__ . '/../../../../../fixtures';

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests retrieving tracking files to process. */
    public function test_retrieve_tracking_files_to_process(): void
    {
        $service = $this->getInstance();
        $this->reloadFilesFixtures();
        $list = $service->retrieveTrackingFilesToProcess();

        // Verify that tracking files are retrieved correctly
        $this->assertCount(1, $list);
        $this->assertArrayHasKey('type', $list[0]);
        $this->assertArrayHasKey('path', $list[0]);
        $this->assertArrayHasKey('timestamp', $list[0]);
        $this->assertArrayHasKey('size', $list[0]);
        $this->assertArrayHasKey('dirname', $list[0]);
        $this->assertArrayHasKey('basename', $list[0]);
        $this->assertArrayHasKey('extension', $list[0]);
        $this->assertArrayHasKey('filename', $list[0]);
    }

    /**
     * Copy fixtures files into the Mondial Relay FTP filesystem.
     *
     * @throws \Exception
     */
    public function reloadFilesFixtures(): void
    {
        $fixtures = [
            [
                'src' => '/files/mondial_relay/tracking/trc.tracking.txt',
                'dest' => '/trc.tracking.txt',
            ],
        ];
        $mondial_relay_filesystem = self::$container
            ->get('oneup_flysystem.mount_manager')
            ->getFilesystem(MondialRelayFtp::FILESYSTEM);
        foreach ($fixtures as $fixture) {
            $resource = fopen(self::FIXTURES_DIR . $fixture['src'], 'r');
            $mondial_relay_filesystem->putStream(MondialRelayFtp::TRACKING_DIR . $fixture['dest'], $resource);
            fclose($resource);
        }
    }

    /** Gets an instance of MondialRelayFtp. */
    protected function getInstance(): MondialRelayFtp
    {
        return new MondialRelayFtp(self::$container->get('oneup_flysystem.mount_manager'));
    }
}
