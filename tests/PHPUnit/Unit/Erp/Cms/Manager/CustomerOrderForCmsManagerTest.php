<?php

namespace PHPUnit\Unit\Erp\Cms\Manager;

use App\Exception\NotFoundException;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Cms\Entity\CustomerOrderForCms;
use SonVideo\Erp\Cms\Manager\CustomerOrderForCmsManager;
use SonVideo\Erp\Referential\Rpc\BoCmsRpcMethodReferential;
use SonVideo\Synapps\Client\RpcClientService;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderForCmsManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql']);
        MySqlDatabase::loadSpecificFixtures(['sales_channel/sales_channels.sql']);
        MySqlDatabase::loadSpecificFixtures(['customer_order/cms.sql']);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests fetching an entity representing a customer order for the CMS. */
    public function test_when_fetching_an_entity_representing_a_customer_order_for_the_cms(): void
    {
        $result = self::$container->get(CustomerOrderForCmsManager::class)->fetchWith(1);

        $this->assertInstanceOf(CustomerOrderForCms::class, $result);
    }

    /** Tests trying to fetch a non-existing customer order. */
    public function test_when_trying_to_fetch_a_non_existing_customer_order(): void
    {
        $tested_instance = self::$container->get(CustomerOrderForCmsManager::class);

        $this->expectException(NotFoundException::class);
        $tested_instance->fetchWith(666);
    }

    /** Tests sending an up-to-date customer order to the CMS. */
    public function test_when_sending_an_up_to_date_customer_order_to_the_cms(): void
    {
        $tested_class = self::$container->get(CustomerOrderForCmsManager::class);

        $rpc_client_call_args = null;
        $rpc_client = $this->createMock(RpcClientService::class);
        $rpc_client
            ->expects($this->once())
            ->method('call')
            ->willReturnCallback(function (...$args) use (&$rpc_client_call_args) {
                $rpc_client_call_args = $args;

                return [];
            });

        $tested_class->setRpcClient($rpc_client);

        $tested_class->push(1);

        // Verify RPC client has been called with expected parameters
        [$service, $method, $args] = $rpc_client_call_args;
        $this->assertEquals(BoCmsRpcMethodReferential::CUSTOMER_ORDER_UPSERT['service'], $service);
        $this->assertEquals(BoCmsRpcMethodReferential::CUSTOMER_ORDER_UPSERT['method'], $method);
        $this->assertArrayHasKey('customer_order', $args);
        $this->assertArrayHasKey('delivery_notes', $args);
        $this->assertArrayHasKey('transfers', $args);

        $this->assertEquals(
            [
                'customer_order_id' => 1,
                'customer_id' => 1,
                'created_at' => '1990-01-01 00:00:00',
                'modified_at' => '2024-12-22 15:20:00',
                'flux' => 'traitement',
                'in_progress_flags' => ['lvr_attente'],
                'origin' => 'SON-VIDEO.COM',
                'total_price' => 449.0,
                'total_price_vat_excluded' => 374.17,
                'ecotax_price' => 0.0,
                'shipping_price' => 0.0,
                'shipment_method_id' => 62,
                'relay_id' => null,
                'store_pick_up_id' => 5,
                'quote_id' => null,
                'preparation_email_sent_at' => null,
                'origin_shop_name' => null,
                'shipping_address' => [
                    'city' => null,
                    'phone' => null,
                    'address' => 'tmp',
                    'civility' => 'M.',
                    'cellphone' => null,
                    'last_name' => null,
                    'first_name' => null,
                    'postal_code' => null,
                    'company_name' => null,
                    'country_code' => 'FR',
                ],
                'billing_address' => [
                    'city' => null,
                    'phone' => null,
                    'address' => 'tmp',
                    'civility' => 'M.',
                    'cellphone' => null,
                    'last_name' => 'Baeyens',
                    'first_name' => 'Henri',
                    'postal_code' => null,
                    'company_name' => null,
                    'country_code' => 'FR',
                ],
                'products' => [
                    [
                        'sku' => 'ARCAMRBLINKNR',
                        'quantity' => 1,
                        'article_id' => 81078,
                        'total_amount' => 249,
                        'basket_description' => 'rBlink',
                        'estimated_delivery_time' => null,
                        'amount_extension_warranty' => 0,
                        'duration_extension_warranty' => null,
                        'amount_theft_break_extension_warranty' => 0,
                        'duration_theft_break_extension_warranty' => null,
                    ],
                    [
                        'sku' => 'LBCLD25BP',
                        'quantity' => 1,
                        'article_id' => 81123,
                        'total_amount' => 200,
                        'basket_description' => 'Pieds noirs laqués pour La Boîte Concept LD120 / LD130 (la paire)',
                        'estimated_delivery_time' => null,
                        'amount_extension_warranty' => 0,
                        'duration_extension_warranty' => null,
                        'amount_theft_break_extension_warranty' => 0,
                        'duration_theft_break_extension_warranty' => null,
                    ],
                ],
                'payments' => [
                    [
                        'code' => 'CTPE',
                        'type' => 'PAYMENT',
                        'amount' => 249,
                        'created_at' => '2019-10-08 19:15:18.000000',
                        'extra_data' => [],
                        'cancel_date' => null,
                        'remitted_at' => '2025-03-01 12:34:56.000000',
                        'remitted_amount' => 249,
                        'validation_date' => '2019-10-08 19:15:40.000000',
                        'validation_proof' => 'XXXXXX',
                        'customer_order_id' => 1,
                        'payment_method_id' => null,
                        'validation_amount' => 249,
                        'back_payment_method_id' => 11,
                        'customer_order_payment_id' => 101,
                    ],
                ],
                'tags' => [],
            ],
            $args['customer_order']
        );

        $this->assertEquals(
            [
                [
                    'delivery_note_id' => 2,
                    'status' => 'au depart',
                    'carrier_id' => 31,
                    'created_at' => '2025-02-28 11:25:07',
                    'shipped_at' => null,
                    'delivered_at' => null,
                    'products' => [
                        [
                            'quantity' => 1,
                            'product_id' => 81123,
                        ],
                    ],
                    'parcels' => [],
                ],
            ],
            $args['delivery_notes']
        );

        $this->assertEquals(
            [
                [
                    'transfer_id' => 1,
                    'status' => 'expedie',
                    'created_at' => '2025-03-01 11:45:02',
                    'shipped_at' => '2025-03-02 11:41:10',
                    'closed_at' => null,
                    'products' => [
                        [
                            'quantity' => 1,
                            'product_id' => 81078,
                            'delivered_quantity' => 0,
                        ],
                    ],
                ],
            ],
            $args['transfers']
        );
    }

    /** Tests trying to update a customer order before sending it to the CMS. */
    public function test_when_trying_to_update_a_customer_order_before_sending_it_to_the_cms(): void
    {
        $tested_class = self::$container->get(CustomerOrderForCmsManager::class);

        $rpc_client = $this->createMock(RpcClientService::class);
        $rpc_client
            ->expects($this->once())
            ->method('call')
            ->willReturn([]);

        $tested_class->setRpcClient($rpc_client);

        $tested_class->touchAndPush(1);
    }
}
