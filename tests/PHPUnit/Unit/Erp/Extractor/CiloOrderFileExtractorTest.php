<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Extractor;

use App\Adapter\Serializer\SerializerInterface;
use App\DataLoader\EntityDataLoader;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\Entity\CustomerEntity;
use SonVideo\Erp\Extractor\CiloOrderFileExtractor;
use SonVideo\Erp\Referential\CustomerOrderOrigin;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;

class CiloOrderFileExtractorTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests extracting an order from a file. */
    public function test_extract_order(): void
    {
        // Prepare service and its mocked sub services
        $service = $this->getInstance();

        $customer = new CustomerEntity();
        $customer->cnt_type = 'DB entreprise';
        $customer->cnt_societe = 'DB Cilo A/S';
        $customer->cnt_civilite = 'M.';
        $customer->cnt_nom = 'DB Holst';
        $customer->cnt_prenom = 'DB Karsten';
        $customer->cnt_adresse = 'DB birk centerpark 40';
        $customer->cnt_code_postal = '7400';
        $customer->cnt_ville = 'HERNING';
        $customer->cnt_id_pays = 52;
        $customer->cnt_telephone = '12345';
        $customer->cnt_telephone_bureau = '54321';
        $customer->cnt_mobile = '6789';
        $customer->cnt_fax = 'fax';
        $customer->cnt_email = 'cilo_email';
        $customer->cnt_numero_tva = 'NUMTVA';

        $customer_repo_mock = $this->createMock(CustomerRepository::class);
        $customer_repo_mock->method('getById')->willReturn($customer);

        $service = new CiloOrderFileExtractor($customer_repo_mock, self::$container->get(SerializerInterface::class));
        $service->setDataLoader(self::$container->get(EntityDataLoader::class));

        // Load an order entity with the data extracted from the loaded content
        $service->setContent($this->getFixture());
        $order = $service->extractOrder();

        $this->assertInstanceOf(CustomerOrderCreationContextDto::class, $order);

        $array = self::$container->get(SerializerInterface::class)->normalize($order, null, [
            AbstractNormalizer::IGNORED_ATTRIBUTES => ['paymentV2VerifiedPayment'],
        ]);

        $this->assertEquals(
            [
                'original_customer_order_id' => '*********',
                'clone_customer_order_id' => null,
                'origin' => CustomerOrderOrigin::CILO,
                'created_at' => '2019-07-18',
                'customer_id' => CiloOrderFileExtractor::CILO_PROSPECT_ID,
                'quote_id' => null,
                'ip_address' => CiloOrderFileExtractor::CILO_IP,
                'source' => null,
                'is_excluding_tax' => true,
                'is_amazon_business' => false,
                'promotion_id' => null,
                'promotion_code' => null,
                'promotion_linked_products' => [],
                'estimated_delivery_date' => null,
                'total_price' => null,
                'return_url' => null,
                'billing_address' => [
                    'company_name' => 'CILO',
                    'civility' => 'M.',
                    'firstname' => 'Karsten',
                    'lastname' => 'Holst',
                    'address' => 'Birk Centerpark 40',
                    'postal_code' => '7400',
                    'city' => 'Herning',
                    'country_code' => 'DK',
                    'phone' => null,
                    'cellphone' => '004553801044',
                    'email' => '<EMAIL>',
                    'vat_number' => 'NUMTVA',
                ],
                'shipping_address' => [
                    'company_name' => '',
                    'civility' => 'M.',
                    'firstname' => 'Søren G.',
                    'lastname' => 'Hansen',
                    'address' => 'Strandlyst Alle 3 B',
                    'postal_code' => '02670',
                    'city' => 'Greve',
                    'country_code' => 'DE',
                    'phone' => null,
                    'cellphone' => '+4522604660',
                    'email' => '<EMAIL>',
                    'vat_number' => null,
                ],
                'shipment_method' => [
                    'cost' => 0.0,
                    'shipment_method_id' => CiloOrderFileExtractor::CILO_SHIPMENT_METHODE_ID,
                    'relay_id' => null,
                    'chrono_precise_appointment' => null,
                ],
                'payments' => [
                    0 => [
                        'payment_mean' => 'CILO',
                        'created_at' => '2019-07-18',
                        'amount' => 553.09,
                        'extra_data' => [],
                        'external_reference' => null,
                        'workflow' => 'legacy',
                    ],
                ],
                'products' => [
                    0 => [
                        'sku' => 'SONOSSUBBC',
                        'description' => 'Sonos Sub Hvid',
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'quantity' => 1,
                        'selling_price_tax_included' => 480.93 * 1.2,
                        'unit_discount_amount' => 0.0,
                        'selected_warranties' => null,
                    ],
                    1 => [
                        'sku' => 'ELTHOBROBC',
                        'description' => 'Eltax Hobro Højttalerstande Hvide',
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'quantity' => 2,
                        'selling_price_tax_included' => 36.08 * 1.2,
                        'unit_discount_amount' => 0.0,
                        'selected_warranties' => null,
                    ],
                ],
            ],
            $array
        );
    }

    /** Tests exceptions when extracting an order. */
    public function test_extract_order_exceptions(): void
    {
        $customer_repo_mock = $this->createMock(CustomerRepository::class);
        $customer_repo_mock->method('getById')->willReturn(
            self::$container->get(EntityDataLoader::class)->hydrate(
                [
                    'id_prospect' => 1518400,
                    'date_creation' => '2019-03-06 10:22:48',
                    'cnt_type' => 'entreprise',
                    'cnt_email' => '<EMAIL>',
                    'cnt_societe' => 'Cilo A/S',
                    'cnt_civilite' => 'M.',
                    'cnt_nom' => 'Holst',
                    'cnt_prenom' => 'Karsten',
                    'cnt_adresse' => 'birk centerpark 40',
                    'cnt_code_postal' => '7400',
                    'cnt_ville' => 'HERNING',
                    'cnt_id_pays' => 52,
                    'cnt_telephone' => '',
                    'cnt_telephone_bureau' => '',
                    'cnt_mobile' => '004522980539',
                    'cnt_fax' => '',
                    'cnt_lvr_type' => 'particulier',
                    'cnt_lvr_email' => '<EMAIL>',
                    'cnt_lvr_societe' => 'Cilo A/S',
                    'cnt_lvr_civilite' => 'M.',
                    'cnt_lvr_nom' => 'Holst',
                    'cnt_lvr_prenom' => 'Karsten',
                    'cnt_lvr_adresse' => 'birk centerpark 40',
                    'cnt_lvr_code_postal' => '7400',
                    'cnt_lvr_ville' => 'HERNING',
                    'cnt_lvr_id_pays' => 52,
                    'cnt_lvr_telephone' => '',
                    'cnt_lvr_telephone_bureau' => '',
                    'cnt_lvr_mobile' => '004522980539',
                    'cnt_lvr_fax' => '',
                    'email' => '<EMAIL>',
                    'type' => 'entreprise',
                    'civilite' => 'M.',
                    'nom' => 'Holst',
                    'prenom' => 'Karsten',
                ],
                CustomerEntity::class
            )
        );

        $service = new CiloOrderFileExtractor($customer_repo_mock, self::$container->get(SerializerInterface::class));
        $service->setDataLoader(self::$container->get(EntityDataLoader::class));

        // Content needs to be loaded first
        $this->expectException(\LogicException::class);
        $this->expectExceptionMessage('Content needs to be set before trying to extract data.');
        $service->extractOrder();
    }

    /** Gets an instance of CiloOrderFileExtractor. */
    protected function getInstance(): CiloOrderFileExtractor
    {
        $customer_repo_mock = $this->createMock(CustomerRepository::class);

        $service = new CiloOrderFileExtractor($customer_repo_mock, self::$container->get(SerializerInterface::class));
        $service->setDataLoader(self::$container->get(EntityDataLoader::class));

        return $service;
    }

    /** Returns the XML content expected from a Cilo file. */
    protected function getFixture(): string
    {
        return <<<XML
        <?xml version="1.0"?>
        <CommandeFournisseur>
          <NumCommande>*********</NumCommande>
          <DateCommande>2019-07-18</DateCommande>
          <Facturation>
            <Societe><![CDATA[CILO]]></Societe>
            <Nom><![CDATA[Holst]]></Nom>
            <Prenom><![CDATA[Karsten]]></Prenom>
            <Adresse1><![CDATA[Birk Centerpark 40]]></Adresse1>
            <Adresse2><![CDATA[]]></Adresse2>
            <Adresse3><![CDATA[]]></Adresse3>
            <CodePostal>7400</CodePostal>
            <Ville><![CDATA[Herning]]></Ville>
            <Pays><![CDATA[DK]]></Pays>
            <Telephone>004553801044</Telephone>
            <Email><EMAIL></Email>
          </Facturation>
          <Livraison>
            <Societe><![CDATA[]]></Societe>
            <Nom><![CDATA[Hansen]]></Nom>
            <Prenom><![CDATA[Søren G.]]></Prenom>
            <Adresse1><![CDATA[Strandlyst Alle 3 B]]></Adresse1>
            <Adresse2><![CDATA[]]></Adresse2>
            <Adresse3><![CDATA[]]></Adresse3>
            <CodePostal>02670</CodePostal>
            <Ville><![CDATA[Greve]]></Ville>
            <Pays><![CDATA[DE]]></Pays>
            <Commentaire><![CDATA[]]></Commentaire>
            <Telephone>+4522604660</Telephone>
            <Portable/>
            <Email><EMAIL></Email>
          </Livraison>
          <Commande>
            <Produit>
              <Type>ARTICLE</Type>
              <ReferenceMarchand>SONOSSUBBC</ReferenceMarchand>
              <Designation><![CDATA[Sonos Sub Hvid]]></Designation>
              <Quantite>1</Quantite>
              <PrixUnitaire devise="EUR" type="HT" tva="20">480.93</PrixUnitaire>
            </Produit>
            <Produit>
              <Type>ARTICLE</Type>
              <ReferenceMarchand>ELTHOBROBC</ReferenceMarchand>
              <Designation><![CDATA[Eltax Hobro Højttalerstande Hvide]]></Designation>
              <Quantite>2</Quantite>
              <PrixUnitaire devise="EUR" type="HT" tva="20">36.08</PrixUnitaire>
            </Produit>
          </Commande>
        </CommandeFournisseur>
        XML;
    }
}
