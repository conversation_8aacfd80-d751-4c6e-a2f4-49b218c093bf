<?php

namespace PHPUnit\Unit\Erp\Accounting\Payment\Manager;

use SonVideo\Erp\Accounting\Payment\Manager\ExportToCsvManager;
use SonVideo\Erp\Accounting\Payment\Mysql\Repository\PaymentRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ExportToCsvManagerTest extends KernelTestCase
{
    public const MOCKED_SQL_RESULTS = [
        [
            'customer_order_created_at' => '2024-05-27 11:17:15',
            'customer_order_id' => '2660148',
            'customer_id' => '988999',
            'customer_name' => 'HUGO Victor',
            'accepted_amount' => '9.00',
            'accepted_at' => '2024-06-03 10:13:50',
            'payment_mean' => 'CTPE',
            'accepted_proof' => null,
            'warehouse_name' => 'Nantes',
        ],
        [
            'customer_order_created_at' => '2024-06-05 15:58:24',
            'customer_order_id' => '2664461',
            'customer_id' => '1271086',
            'customer_name' => '<PERSON><PERSON><PERSON><PERSON>',
            'accepted_amount' => '2332.02',
            'accepted_at' => '2024-06-07 16:03:05',
            'payment_mean' => 'CTPE',
            'accepted_proof' => null,
            'warehouse_name' => 'Nantes',
        ],
        [
            'customer_order_created_at' => '2024-06-15 10:14:40',
            'customer_order_id' => '2669290',
            'customer_id' => '1248885',
            'customer_name' => 'Proust Marcel',
            'accepted_amount' => '319.00',
            'accepted_at' => '2024-06-15 10:25:57',
            'payment_mean' => 'PANT',
            'accepted_proof' => null,
            'warehouse_name' => 'Nantes',
        ],
    ];

    /** Creates a mock repository for testing. */
    private function getMockTransferRepository(): PaymentRepository
    {
        $repository_mock = $this->createMock(PaymentRepository::class);
        $repository_mock->method('findForCsvExport')->willReturn(self::MOCKED_SQL_RESULTS);

        return $repository_mock;
    }

    /** Creates a test instance of ExportToCsvManager. */
    protected function getTransferCsvExporter(): ExportToCsvManager
    {
        return new ExportToCsvManager($this->getMockTransferRepository());
    }

    /** Tests the CSV export functionality. */
    public function test_csv_export(): void
    {
        $result = $this->getTransferCsvExporter()->export([4567035, 4569768, 4574360]);

        // Convert from UTF-16 to UTF-8 for testing
        $converted_result = iconv('UTF-16', 'UTF-8', $result);

        $this->assertStringContainsString('"2024-06-05 15:58:24"', $converted_result);
        $this->assertStringContainsString('HUGO', $converted_result);
        $this->assertStringContainsString('"Baudelaire Charles"', $converted_result);
    }
}
