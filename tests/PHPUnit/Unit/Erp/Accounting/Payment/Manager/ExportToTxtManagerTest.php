<?php

namespace PHPUnit\Unit\Erp\Accounting\Payment\Manager;

use SonVideo\Erp\Accounting\Payment\Manager\ExportToTxtManager;
use SonVideo\Erp\Accounting\Payment\Mysql\Repository\PaymentRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ExportToTxtManagerTest extends KernelTestCase
{
    public const MOCKED_SQL_RESULTS = [
        [
            'created_at' => '2024-01-01 13:55:29',
            'type' => 'paiement',
            'payment_mean' => 'CTPE',
            'bank_account' => '512300',
            'journal' => 'CE',
            'remit_amount' => '4.90',
            'client_nb' => '1960650',
            'client' => 'EDDIE VAN HALEN',
            'account' => '411000',
            'remit_date' => '2024-05-31 13:55:29',
            'rate' => '1',
            'order_id' => '2662124',
            'warehouse' => 'Champigny 2',
            'invoice_id' => '1926018',
            'commission_amount' => '0.00',
        ],
        [
            'created_at' => '2024-01-01 16:19:19',
            'type' => 'paiement',
            'payment_mean' => 'CTPE',
            'bank_account' => '512300',
            'journal' => 'CE',
            'remit_amount' => '28.90',
            'client_nb' => '0342820',
            'client' => 'CARLOS SANTANA',
            'account' => '411000',
            'remit_date' => '2024-05-31 16:19:19',
            'rate' => '1',
            'order_id' => '2662223',
            'warehouse' => 'Champigny 2',
            'invoice_id' => '1926065',
            'commission_amount' => '0.00',
        ],
        [
            'created_at' => '2024-01-01 15:52:59',
            'type' => 'paiement',
            'payment_mean' => 'EMPT',
            'bank_account' => '512000',
            'journal' => 'BQ',
            'remit_amount' => '1485.00',
            'client_nb' => '1647812',
            'client' => 'PRINCE',
            'account' => '411100',
            'remit_date' => '2024-06-01 15:52:59',
            'rate' => '1',
            'order_id' => '2661312',
            'warehouse' => 'Nantes',
            'invoice_id' => '1926450',
            'commission_amount' => '0.00',
        ],
        [
            'created_at' => '2024-01-03 10:17:48',
            'type' => 'paiement',
            'payment_mean' => 'EMPT',
            'bank_account' => '512000',
            'journal' => 'BQ',
            'remit_amount' => '88.40',
            'client_nb' => '1820998',
            'client' => 'MARK KNOPFLER',
            'account' => '411000',
            'remit_date' => '2024-06-03 10:17:48',
            'rate' => '1',
            'order_id' => '2660079',
            'warehouse' => 'Champigny 2',
            'invoice_id' => '1927531',
            'commission_amount' => '0.00',
        ],
        [
            'created_at' => '2024-01-01 15:52:59',
            'type' => 'paiement',
            'payment_mean' => 'WBCTC',
            'bank_account' => '512000',
            'journal' => 'BQ',
            'remit_amount' => '123.00',
            'client_nb' => '4444',
            'client' => 'JONATHAN',
            'account' => '411000',
            'remit_date' => '2024-06-01 15:52:59',
            'rate' => '1',
            'order_id' => '123456',
            'warehouse' => null,
            'invoice_id' => '********',
            'commission_amount' => '1.4',
        ],
        [
            'created_at' => '2024-01-03 10:17:48',
            'type' => 'remboursement',
            'payment_mean' => 'WBCTC',
            'bank_account' => '512000',
            'journal' => 'BQ',
            'remit_amount' => '-45.40',
            'client_nb' => '5555',
            'client' => 'JENNYFER',
            'account' => '411000',
            'remit_date' => '2024-06-01 10:17:48',
            'rate' => '1',
            'order_id' => '654321',
            'warehouse' => null,
            'invoice_id' => '********',
            'commission_amount' => '0.3',
        ],
    ];

    /** Creates a mock repository for testing. */
    private function getMockTransferRepository(): PaymentRepository
    {
        $repository_mock = $this->createMock(PaymentRepository::class);
        $repository_mock->method('findForTxtExport')->willReturn(self::MOCKED_SQL_RESULTS);

        return $repository_mock;
    }

    /** Creates a test instance of ExportToTxtManager. */
    protected function getTestedClass(): ExportToTxtManager
    {
        return new ExportToTxtManager($this->getMockTransferRepository());
    }

    /** Tests the TXT export functionality. */
    public function test_txt_export(): void
    {
        $result = $this->getTestedClass()->export([4567035, 4569768, 4574360, 4574387]);

        $expected = implode("\r\n", [
            'CE    202405312662124      411000       1926018          2662124          1960650          REMISE CTPE EDDIE VAN HALEN        C          4,90',
            'CE    202405312662223      411000       1926065          2662223          0342820          REMISE CTPE CARLOS SANTANA         C         28,90',
            'CE    20240531310524       512300                                                          TOTAL CTPE CHAMPIGNY 2             D         33,80',
            'BQ    202406012661312      411100       1926450          2661312          1647812          REMISE EMPT PRINCE                 C       1485,00',
            'BQ    20240601010624       512000                                                          TOTAL EMPT NANTES                  D       1485,00',
            'BQ    202406032660079      411000       1927531          2660079          1820998          REMISE EMPT MARK KNOPFLER          C         88,40',
            'BQ    20240603030624       512000                                                          TOTAL EMPT CHAMPIGNY 2             D         88,40',
            'BQ    20240601123456       411000       ********         123456           4444             REMISE WBCTC JONATHAN              C        123,00',
            'BQ    20240601654321       411000       ********         654321           5555             REMBOURSEMENT WBCTC JENNYFER       D         45,40',
            'BQ    20240601             627822                                                          COMMISSION WBCTC                   D          1,70',
            'BQ    20240601010624       512000                                                          TOTAL WBCTC                        D         75,90',
        ]);

        $this->assertEquals($expected, $result);
    }
}
