<?php

namespace PHPUnit\Unit\Erp\Supplier\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Sql\Query\QueryBuilder;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use SonVideo\Erp\Supplier\Entity\SupplierPaymentInformationEntity;
use SonVideo\Erp\Supplier\Manager\SupplierManager;
use SonVideo\Erp\Supplier\Mysql\Repository\SupplierRepository;
use SonVideo\Erp\SupplierPayment\Mysql\Repository\SupplierPaymentRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SupplierManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['supplier_contract/cpost_supplier_contracts.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): SupplierManager
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        $tested_class = new SupplierManager(
            self::$container->get(SupplierRepository::class),
            self::$container->get(SupplierPaymentRepository::class),
            self::$container->get(ValidatorInterface::class),
            self::$container->get(QueryBuilder::class)
        );

        $tested_class->setSerializer(self::$container->get(SerializerInterface::class));

        return $tested_class;
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /**
     * Tests the update method.
     *
     * @throws NotFoundException
     * @throws ExceptionInterface
     * @throws \JsonException
     * @throws InternalErrorException
     * @throws \Exception
     */
    public function test_update(): void
    {
        $suppliers = [
            'supplier_id' => 400,
            'name' => 'LA BOITE CONCEPT',
            'status' => 'oui',
            'origin_country_id' => 67,
            'discount_rate' => 0,
            'franco' => 0.0,
            'delivery_cost' => '',
            'payment_deadline_id' => 4,
            'discount_payment_deadline' => 2,
            'supplier_payment_id' => 2,
            'comment' => '',
        ];

        /** @var SupplierPaymentInformationEntity $dto_success */
        $dto_success = $this->getSerializer()->denormalize($suppliers, SupplierPaymentInformationEntity::class);
        $supplier_manager = $this->getTestedInstance();

        // Test: Update supplier contract success
        $supplier_id = 400;

        $supplier_contract_before_update = $this->fetchSupplierContract($supplier_id);
        $this->assertEquals('2', $supplier_contract_before_update['id_paiement_fournisseur']);
        $this->assertEquals('0.00', $supplier_contract_before_update['franco']);

        $supplier_manager->update($dto_success);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches a supplier contract by ID. */
    protected function fetchSupplierContract(int $supplier_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.fournisseur
        WHERE id_fournisseur = :supplier_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['supplier_id' => $supplier_id]);
    }
}
