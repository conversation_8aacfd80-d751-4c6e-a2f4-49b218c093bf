<?php

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager;

use App\Database\PgDataWarehouse\DataSchema\AutoStructure\SupplierOrderDispute;
use App\Database\PgDataWarehouse\DataSchema\CustomerOrderModel;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\PHPUnit\PhpUnitPommHelperTrait;
use PommProject\Foundation\Session\Connection;
use SonVideo\Erp\DataWarehouse\Manager\SupplierOrderDisputeSynchronizer;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SupplierOrderDisputeSynchronizerTest extends KernelTestCase
{
    use PhpUnitPommHelperTrait;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'datawarehouse/supplier_order_products.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** @throws \Exception */
    public function test_synchronize(): void
    {
        $today = new \DateTime();
        $this->getManager();
        $this->getManager()->synchronize(\DateTime::createFromFormat('U', 0), $today);

        $this->assertEquals(
            [
                [
                    'date' => $today->format('Y-m-d'),
                    'nb_supplier_order' => '4',
                    'nb_supplier_order_dispute' => '1',
                ],
            ],
            $this->getPgResult()
        );
    }

    private function getPgConnexion(): Connection
    {
        return self::$container
            ->get(CustomerOrderModel::class)
            ->getSession()
            ->getConnection();
    }

    private function getManager(): SupplierOrderDisputeSynchronizer
    {
        return self::$container->get(SupplierOrderDisputeSynchronizer::class);
    }

    private function getLegacyPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** @return array<int, mixed[]> */
    private function getPgResult(): array
    {
        $sql = <<<'SQL'
        SELECT * FROM {table}
        SQL;

        $sql = strtr($sql, [
            '{table}' => (new SupplierOrderDispute())->getRelation(),
        ]);

        $stmt = $this->getPgConnexion()->executeAnonymousQuery($sql);

        $count = $stmt->countRows();
        $result = [];
        for ($i = 0; $i < $count; ++$i) {
            $result[] = $stmt->fetchRow($i);
        }

        return $result;
    }
}
