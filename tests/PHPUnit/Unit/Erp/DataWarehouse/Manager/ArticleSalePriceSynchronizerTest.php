<?php

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager;

use App\Database\PgDataWarehouse\DataSchema\ArticleSalePriceModel;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\PHPUnit\PhpUnitPommHelperTrait;
use PommProject\Foundation\Session\Connection;
use PommProject\ModelManager\Exception\ModelException;
use SonVideo\Erp\DataWarehouse\Manager\ArticleSalePriceSynchronizer;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleSalePriceSynchronizerTest extends KernelTestCase
{
    use PhpUnitPommHelperTrait;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'datawarehouse/article_sale_price.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();

        $date_time = new \DateTime();
        $date_time->modify('-1 day');
        $this->initDataParameter($date_time);
    }

    protected function tearDown(): void
    {
        $this->deleteData();
        parent::tearDown();
    }

    /** @throws ModelException */
    public function test_synchronize_one(): void
    {
        $this->deleteData();
        $this->getManager()->synchronizeOne('LBCLD25BP');

        $this->assertEquals(
            [
                [
                    'reference' => 'LBCLD25BP',
                    'selling_price_tax_included' => '359.00',
                    'price_change_date' => '2023-07-30 00:00:00',
                ],
            ],
            $this->getPgResult()
        );
        $this->deleteData();
    }

    public function test_multiple_synchronize_all(): void
    {
        // synchronizeAll
        $this->deleteData();
        $old_last_update = strtotime($this->getDataParameter(ArticleSalePriceSynchronizer::LAST_UPDATE)['value']);

        $this->getManager()->synchronizeAll();

        $this->assertEquals(
            [
                [
                    'reference' => 'ARCAMRBLINKNR',
                    'selling_price_tax_included' => '189.00',
                    'price_change_date' => '2023-07-31 00:00:00',
                ],
                [
                    'reference' => 'BWCCM74',
                    'selling_price_tax_included' => '1490.99',
                    'price_change_date' => '2023-07-22 00:00:00',
                ],
                [
                    'reference' => 'LBCLD25BP',
                    'selling_price_tax_included' => '359.00',
                    'price_change_date' => '2023-07-30 00:00:00',
                ],
                [
                    'reference' => 'NORSTCL25025M',
                    'selling_price_tax_included' => '549.00',
                    'price_change_date' => '2023-07-14 00:00:00',
                ],
            ],
            $this->getPgResult()
        );

        $last_update = strtotime($this->getDataParameter(ArticleSalePriceSynchronizer::LAST_UPDATE)['value']);
        $this->assertGreaterThan($old_last_update, $last_update);

        // synchronizeSince
        $this->deleteData();
        $this->initDataParameter(new \DateTime('2023-07-23'));
        $this->getManager()->synchronizeSince(new \DateTime('2023-07-23'));

        $this->assertEquals(
            [
                [
                    'reference' => 'ARCAMRBLINKNR',
                    'selling_price_tax_included' => '189.00',
                    'price_change_date' => '2023-07-31 00:00:00',
                ],
                [
                    'reference' => 'LBCLD25BP',
                    'selling_price_tax_included' => '359.00',
                    'price_change_date' => '2023-07-30 00:00:00',
                ],
            ],
            $this->getPgResult()
        );

        // synchronizeBetween
        $this->deleteData();
        $this->getManager()->synchronizeBetween(new \DateTime('2023-07-22'), new \DateTime('2023-07-23'));

        $this->assertEquals(
            [
                [
                    'reference' => 'BWCCM74',
                    'selling_price_tax_included' => '1490.99',
                    'price_change_date' => '2023-07-22 00:00:00',
                ],
            ],
            $this->getPgResult()
        );

        // synchronizeLatest
        $this->deleteData();
        $this->initDataParameter(new \DateTime('2023-07-31'));
        $this->getManager()->synchronizeLatest();

        $this->assertEquals(
            [
                [
                    'reference' => 'ARCAMRBLINKNR',
                    'selling_price_tax_included' => '189.00',
                    'price_change_date' => '2023-07-31 00:00:00',
                ],
            ],
            $this->getPgResult()
        );
    }

    private function getPgConnexion(): Connection
    {
        return self::$container
            ->get(ArticleSalePriceModel::class)
            ->getSession()
            ->getConnection();
    }

    private function getManager(): ArticleSalePriceSynchronizer
    {
        return self::$container->get(ArticleSalePriceSynchronizer::class);
    }

    private function deleteData($id = null): void
    {
        $sql = <<<SQL
        DELETE FROM data.article_sale_price :id;
        SQL;

        $sql = str_replace(':id', $id ? 'WHERE reference = ' . $id : '', $sql);

        $this->getPgConnexion()->executeAnonymousQuery($sql);
    }

    private function initDataParameter(\DateTime $date_time): void
    {
        $sql = <<<SQL
        INSERT INTO data.data_parameter (key, value) VALUES (':key',':value' )
        ON CONFLICT (KEY) DO UPDATE set VALUE = excluded.value;
        SQL;

        $sql = strtr($sql, [
            ':key' => ArticleSalePriceSynchronizer::LAST_UPDATE,
            ':value' => $date_time->format('Y-m-d H:i:s'),
        ]);

        $this->getPgConnexion()->executeAnonymousQuery($sql);
    }

    /** @return array<int, mixed[]> */
    private function getPgResult(): array
    {
        $sql = <<<'SQL'
        SELECT asp.reference, asp.selling_price_tax_included, asp.price_change_date
        FROM data.article_sale_price asp
        ORDER BY asp.reference, asp.price_change_date;
        SQL;

        $stmt = $this->getPgConnexion()->executeAnonymousQuery($sql);

        $count = $stmt->countRows();
        $result = [];
        for ($i = 0; $i < $count; ++$i) {
            $result[] = $stmt->fetchRow($i);
        }

        return $result;
    }

    private function getDataParameter(string $key): array
    {
        $sql = <<<SQL
        SELECT dp.key, dp.value
        FROM data.data_parameter dp
        WHERE dp.key = ':key'
        SQL;

        $sql = str_replace(':key', $key, $sql);

        return $this->getPgConnexion()
            ->executeAnonymousQuery($sql)
            ->fetchRow(0);
    }
}
