<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\SafetyStockRepository;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\SafetyStockUpsertTopicContent;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\SafetyStockTopicUpsertSynchronizer;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SafetyStockUpsertTopicSynchronizerTest extends KernelTestCase
{
    private SafetyStockTopicUpsertSynchronizer $synchronizer;
    private SafetyStockRepository $safety_stock_repository;
    private SerializerInterface $serializer;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->safety_stock_repository = $this->createMock(SafetyStockRepository::class);
        $this->serializer = self::$container->get(SerializerInterface::class);

        $this->synchronizer = new SafetyStockTopicUpsertSynchronizer($this->safety_stock_repository);
        $this->synchronizer->setSerializer($this->serializer);
        $this->synchronizer->setLogger(new NullLogger());
    }

    /** Teste que la méthode canHandle retourne true pour le topic correct */
    public function test_can_handle_returns_true_for_correct_topic(): void
    {
        $this->assertTrue($this->synchronizer->canHandle(SynchronizableTopicName::SAFETY_STOCK_UPSERT));
    }

    /** Teste que la méthode canHandle retourne false pour un topic incorrect */
    public function test_can_handle_returns_false_for_incorrect_topic(): void
    {
        $this->assertFalse($this->synchronizer->canHandle('some_other_topic'));
    }

    /** Teste que la méthode synchronize appelle correctement upsert avec les données appropriées */
    public function test_synchronize_calls_upsert_with_correct_data(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SAFETY_STOCK_UPSERT,
                'product_id' => 456,
                'supplier_id' => 789,
                'brand_id' => 101,
                'snapshotted_at' => '2023-01-01T12:00:00+00:00',
                'subcategory_id' => 202,
                'safety_stock' => 50,
                'shipping_delay' => 3.5,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SafetyStockUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to expect upsert call with SafetyStock instance
        $this->safety_stock_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container->get(SerializerInterface::class)->normalize($synchronizable_topic)
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur */
    public function test_synchronize_throws_exception_on_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SAFETY_STOCK_UPSERT,
                'product_id' => 456,
                'supplier_id' => 789,
                'brand_id' => 101,
                'snapshotted_at' => '2023-01-01T12:00:00+00:00',
                'subcategory_id' => 202,
                'safety_stock' => 50,
                'shipping_delay' => 3.5,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SafetyStockUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to throw exception
        $this->safety_stock_repository->method('upsert')->willThrowException(new \Exception('Database error'));

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' . SynchronizableTopicName::SAFETY_STOCK_UPSERT . '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize gère correctement les valeurs nullables */
    public function test_synchronize_handles_nullable_values(): void
    {
        // Create a real SynchronizableTopic instance with nullable values
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SAFETY_STOCK_UPSERT,
                'product_id' => 456,
                'supplier_id' => 789,
                'brand_id' => 101,
                'snapshotted_at' => '2023-01-01T12:00:00+00:00',
                'subcategory_id' => 202,
                'safety_stock' => null,
                'shipping_delay' => null,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SafetyStockUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to expect upsert call with SafetyStock instance
        $this->safety_stock_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container->get(SerializerInterface::class)->normalize($synchronizable_topic)
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }
}
