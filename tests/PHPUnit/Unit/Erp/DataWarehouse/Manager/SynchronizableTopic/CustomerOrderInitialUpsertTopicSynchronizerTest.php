<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\ConnectionProvider\PgDataWarehouseConnectionProvider;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderInitialLineRepository;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Entity\CustomerOrderInitialLine;
use Doctrine\DBAL\Connection;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\CustomerOrderInitialUpsertTopicContent;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\CustomerOrderInitialUpsertTopicSynchronizer;
use SonVideo\Erp\DataWarehouse\Repository\Mysql\CustomerOrderInitialForDataWarehouseRepository;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderInitialUpsertTopicSynchronizerTest extends KernelTestCase
{
    private CustomerOrderInitialUpsertTopicSynchronizer $synchronizer;
    private CustomerOrderInitialLineRepository $customer_order_initial_line_repository;
    private CustomerOrderInitialForDataWarehouseRepository $customer_order_initial_repository;
    private PgDataWarehouseConnectionProvider $pg_data_warehouse_connection_provider;
    private Connection $connection;
    private SerializerInterface $serializer;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->customer_order_initial_line_repository = $this->createMock(CustomerOrderInitialLineRepository::class);
        $this->customer_order_initial_repository = $this->createMock(
            CustomerOrderInitialForDataWarehouseRepository::class
        );
        $this->connection = $this->createMock(Connection::class);
        $this->pg_data_warehouse_connection_provider = $this->createMock(PgDataWarehouseConnectionProvider::class);
        $this->pg_data_warehouse_connection_provider->method('getConnection')->willReturn($this->connection);
        $this->serializer = self::$container->get(SerializerInterface::class);

        $this->synchronizer = new CustomerOrderInitialUpsertTopicSynchronizer(
            $this->customer_order_initial_line_repository,
            $this->customer_order_initial_repository,
            $this->pg_data_warehouse_connection_provider
        );
        $this->synchronizer->setSerializer($this->serializer);
        $this->synchronizer->setLogger(new NullLogger());
    }

    /** Teste que la méthode canHandle retourne true pour le topic correct */
    public function test_can_handle_returns_true_for_correct_topic(): void
    {
        $this->assertTrue($this->synchronizer->canHandle(SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT));
    }

    /** Teste que la méthode canHandle retourne false pour un topic incorrect */
    public function test_can_handle_returns_false_for_incorrect_topic(): void
    {
        $this->assertFalse($this->synchronizer->canHandle('some_other_topic'));
    }

    /** Teste que la méthode synchronize appelle correctement les méthodes nécessaires avec les données appropriées */
    public function test_synchronize_calls_methods_with_correct_data(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(CustomerOrderInitialUpsertTopicContent::class, $synchronizable_topic);

        // Create sample product data
        $product1 = new CustomerOrderInitialLine();
        $product1->customer_order_product_initial_id = 1001;
        $product1->customer_order_id = 456;
        $product1->reference = 'REF001';
        $product1->model = 'Model A';
        $product1->brand = 'Brand X';
        $product1->quantity = 2;

        $product2 = new CustomerOrderInitialLine();
        $product2->customer_order_product_initial_id = 1002;
        $product2->customer_order_id = 456;
        $product2->reference = 'REF002';
        $product2->model = 'Model B';
        $product2->brand = 'Brand Y';
        $product2->quantity = 1;

        $products = [$product1, $product2];

        // Configure repository mock to return sample products
        $this->customer_order_initial_repository
            ->expects($this->once())
            ->method('findByIdForDataWarehouse')
            ->with(456)
            ->willReturn($products);

        // Configure connection to expect transaction methods
        $this->connection->expects($this->once())->method('beginTransaction');
        $this->connection->expects($this->once())->method('commit');
        $this->connection->expects($this->never())->method('rollBack');

        // Configure repository mock to expect upsert calls for each product
        $this->customer_order_initial_line_repository
            ->expects($this->exactly(2))
            ->method('upsert')
            ->withConsecutive(
                [
                    $this->callback(function ($arg) use ($product1) {
                        return $arg['customer_order_product_initial_id'] ===
                            $product1->customer_order_product_initial_id;
                    }),
                ],
                [
                    $this->callback(function ($arg) use ($product2) {
                        return $arg['customer_order_product_initial_id'] ===
                            $product2->customer_order_product_initial_id;
                    }),
                ]
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur lors de la récupération des produits */
    public function test_synchronize_throws_exception_on_repository_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(CustomerOrderInitialUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to throw exception
        $this->customer_order_initial_repository
            ->method('findByIdForDataWarehouse')
            ->willThrowException(new \Exception('Database error'));

        // Configure connection to expect transaction methods
        $this->connection->expects($this->once())->method('beginTransaction');
        $this->connection->expects($this->never())->method('commit');
        $this->connection->expects($this->once())->method('rollBack');

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' . SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT . '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur lors de l'upsert des produits */
    public function test_synchronize_throws_exception_on_upsert_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT,
                'customer_order_id' => 456,
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(CustomerOrderInitialUpsertTopicContent::class, $synchronizable_topic);

        // Create sample product data
        $product = new CustomerOrderInitialLine();
        $product->customer_order_product_initial_id = 1001;
        $product->customer_order_id = 456;
        $product->reference = 'REF001';

        // Configure repository mock to return sample products
        $this->customer_order_initial_repository->method('findByIdForDataWarehouse')->willReturn([$product]);

        // Configure repository mock to throw exception on upsert
        $this->customer_order_initial_line_repository
            ->method('upsert')
            ->willThrowException(new \Exception('Upsert error'));

        // Configure connection to expect transaction methods
        $this->connection->expects($this->once())->method('beginTransaction');
        $this->connection->expects($this->never())->method('commit');
        $this->connection->expects($this->once())->method('rollBack');

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' . SynchronizableTopicName::CUSTOMER_ORDER_INITIAL_UPSERT . '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }
}
