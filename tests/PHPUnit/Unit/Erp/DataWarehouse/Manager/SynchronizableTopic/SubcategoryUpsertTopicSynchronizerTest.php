<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\SubcategoryRepository;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\SubcategoryUpsertTopicContent;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\SubcategoryTopicUpsertSynchronizer;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SubcategoryUpsertTopicSynchronizerTest extends KernelTestCase
{
    private SubcategoryTopicUpsertSynchronizer $synchronizer;
    private SubcategoryRepository $subcategory_repository;
    private SerializerInterface $serializer;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->subcategory_repository = $this->createMock(SubcategoryRepository::class);
        $this->serializer = self::$container->get(SerializerInterface::class);

        $this->synchronizer = new SubcategoryTopicUpsertSynchronizer($this->subcategory_repository);
        $this->synchronizer->setSerializer($this->serializer);
        $this->synchronizer->setLogger(new NullLogger());
    }

    /** Teste que la méthode canHandle retourne true pour le topic correct */
    public function test_can_handle_returns_true_for_correct_topic(): void
    {
        $this->assertTrue($this->synchronizer->canHandle(SynchronizableTopicName::SUBCATEGORY_UPSERT));
    }

    /** Teste que la méthode canHandle retourne false pour un topic incorrect */
    public function test_can_handle_returns_false_for_incorrect_topic(): void
    {
        $this->assertFalse($this->synchronizer->canHandle('some_other_topic'));
    }

    /** Teste que la méthode synchronize appelle correctement upsert avec les données appropriées */
    public function test_synchronize_calls_upsert_with_correct_data(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SUBCATEGORY_UPSERT,
                'subcategory_id' => 456,
                'subcategory_name' => 'Electronics',
                'parent_category_id' => 789,
                'parent_category_name' => 'Technology',
                'user_id' => 101,
                'user_name' => 'John Doe',
                'subcategory_type' => 'standard',
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SubcategoryUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to expect upsert call with Subcategory instance
        $this->subcategory_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container->get(SerializerInterface::class)->normalize($synchronizable_topic)
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur */
    public function test_synchronize_throws_exception_on_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SUBCATEGORY_UPSERT,
                'subcategory_id' => 456,
                'subcategory_name' => 'Electronics',
                'parent_category_id' => 789,
                'parent_category_name' => 'Technology',
                'user_id' => 101,
                'user_name' => 'John Doe',
                'subcategory_type' => 'standard',
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SubcategoryUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to throw exception
        $this->subcategory_repository->method('upsert')->willThrowException(new \Exception('Database error'));

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' . SynchronizableTopicName::SUBCATEGORY_UPSERT . '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize gère correctement les valeurs nullables */
    public function test_synchronize_handles_nullable_values(): void
    {
        // Create a real SynchronizableTopic instance with nullable values
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::SUBCATEGORY_UPSERT,
                'subcategory_id' => 456,
                'subcategory_name' => 'Electronics',
                'parent_category_id' => 789,
                'parent_category_name' => 'Technology',
                'user_id' => null,
                'user_name' => null,
                'subcategory_type' => 'standard',
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(SubcategoryUpsertTopicContent::class, $synchronizable_topic);

        // Configure repository mock to expect upsert call with Subcategory instance
        $this->subcategory_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container->get(SerializerInterface::class)->normalize($synchronizable_topic)
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }
}
