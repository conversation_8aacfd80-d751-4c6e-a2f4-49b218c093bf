<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Export\Cilo\Manager;

use App\Contract\DataLoaderInterface;
use App\DataLoader\EntityDataLoader;
use SonVideo\Erp\Entity\Shipping\ShipmentTrackingEntity;
use SonVideo\Erp\Entity\Shipping\ShipmentTrackingParcelEntity;
use SonVideo\Erp\Export\Cilo\Manager\Shipment;
use SonVideo\Erp\Filesystem\Manager\CiloFtp;
use SonVideo\Erp\Filesystem\Manager\CiloS3;
use SonVideo\Erp\Repository\Shipping\DeliveryTicketActivityLogReadRepository;
use SonVideo\Erp\Repository\Shipping\DeliveryTicketActivityLogWriteRepository;
use SonVideo\Erp\Repository\Shipping\ShipmentTrackingReadRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ShipmentTest extends KernelTestCase
{
    public const DELIVERY_TICKET_QUERY = [
        'delivery_ticket_id' => 123456,
        'order_id' => 123,
        'external_order_number' => 'WHATEVER',
        'carrier_name' => 'SUPER CARRIER NAME',
        'shipping_method_name' => 'AWESOME SHIPPING METHOD NAME',
    ];

    public const PARCELS_QUERY = [
        [
            'tracking_number' => 'SOMETHING OLD',
            'quantity' => 1,
            'sku' => '2UNLIMITED',
        ],
        [
            'tracking_number' => 'SOMETHING TRENDY',
            'quantity' => 2,
            'sku' => 'KHALID4EVER',
        ],
        [
            'tracking_number' => 'SOMETHING POPISH',
            'quantity' => 3,
            'sku' => 'BLACKPINK',
        ],
        [
            'tracking_number' => 'SOMETHING OUTRAGEOUS',
            'quantity' => 4,
            'sku' => 'DAMDAMDEOOOO',
        ],
    ];

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested class. */
    protected function getTestedClass(): Shipment
    {
        $class = new Shipment(
            $this->getLogReadRepository(),
            $this->getLogWriteRepository(),
            $this->getShipmentTrackingReadRepository(),
            self::$container->get(CiloS3::class),
            self::$container->get(CiloFtp::class)
        );

        $class->setLogger(self::$container->get('logger'));

        return $class;
    }

    /** Gets the data loader. */
    protected function getDataLoader(): DataLoaderInterface
    {
        return self::$container->get(EntityDataLoader::class);
    }

    /** Gets the log read repository. */
    protected function getLogReadRepository(): DeliveryTicketActivityLogReadRepository
    {
        return $this->createMock(DeliveryTicketActivityLogReadRepository::class);
    }

    /** Gets the shipment tracking read repository. */
    protected function getShipmentTrackingReadRepository(): ShipmentTrackingReadRepository
    {
        $repository_mocked = $this->createMock(ShipmentTrackingReadRepository::class);

        $data = static::DELIVERY_TICKET_QUERY;
        $data2 = static::PARCELS_QUERY;
        $data_loader = $this->getDataLoader();

        $repository_mocked
            ->method('findById')
            ->willReturnCallback(function () use ($data, $data2, $data_loader): ShipmentTrackingEntity {
                $entity = $data_loader->hydrate($data, ShipmentTrackingEntity::class);

                $parcels = [];
                foreach ($data2 as $result) {
                    $parcels[] = $data_loader->hydrate($result, ShipmentTrackingParcelEntity::class);
                }

                $entity->parcels = $parcels;

                return $entity;
            });

        return $repository_mocked;
    }

    /** Gets the log write repository. */
    protected function getLogWriteRepository(): DeliveryTicketActivityLogWriteRepository
    {
        $repository_mocked = $this->createMock(DeliveryTicketActivityLogWriteRepository::class);

        $repository_mocked->method('write')->willReturnSelf();

        return $repository_mocked;
    }

    /** Tests the notifyOne method. */
    public function test_notify_one(): void
    {
        $std_class = new \stdClass();
        $std_class->delivery_ticket_id = '123456';

        // Check generated XML string
        $result = $this->getTestedClass()->notifyOne($std_class);

        $this->assertStringContainsString('<?xml version="1.0" encoding="ISO-8859-1"?>', $result);
        $this->assertStringContainsString(
            '<shipment delivery_ticket="123456" carrier="SUPER CARRIER NAME" shipping_method="AWESOME SHIPPING METHOD NAME">',
            $result
        );
        $this->assertStringContainsString('<order_id>WHATEVER</order_id>', $result);
        $this->assertStringContainsString('<parcel tracking_number="SOMETHING OLD">', $result);
        $this->assertStringContainsString(
            '<sku type="SellerSku" quantity="1" value="2UNLIMITED">2UNLIMITED</sku>',
            $result
        );
        $this->assertStringContainsString('<parcel tracking_number="SOMETHING TRENDY">', $result);
        $this->assertStringContainsString(
            '<sku type="SellerSku" quantity="2" value="KHALID4EVER">KHALID4EVER</sku>',
            $result
        );
        $this->assertStringContainsString('<parcel tracking_number="SOMETHING POPISH">', $result);
        $this->assertStringContainsString(
            '<sku type="SellerSku" quantity="3" value="BLACKPINK">BLACKPINK</sku>',
            $result
        );
        $this->assertStringContainsString('<parcel tracking_number="SOMETHING OUTRAGEOUS">', $result);
        $this->assertStringContainsString(
            '<sku type="SellerSku" quantity="4" value="DAMDAMDEOOOO">DAMDAMDEOOOO</sku>',
            $result
        );
    }
}
