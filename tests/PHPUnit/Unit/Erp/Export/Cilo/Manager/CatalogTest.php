<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Export\Cilo\Manager;

use App\DataLoader\EntityDataLoader;
use SonVideo\Erp\Entity\Marketplace\Cilo\CiloProductEntity;
use SonVideo\Erp\Export\Cilo\Manager\Catalog;
use SonVideo\Erp\Repository\Marketplace\Cilo\CiloMarketplaceReadRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CatalogTest extends KernelTestCase
{
    public const MOCKED_SQL_RESULTS = [
        [
            'sku' => 'AAATCRESCENDOINR',
            'prix' => '940.00',
            'prix_cilo_ht' => 0,
            'prix_svd_ttc' => '599.00',
            'frais_port' => '20.30',
            'modele' => 'ArmoniA AirTech Crescendo Intégré Noir',
            'ean13' => null,
            'marque' => 'Audio Analogue',
            'description' => 'Amplificateurs Stérée Audio Analogue ArmoniA AirTech Crescendo Intégré Noir',
            'disponibilite' => 'En stock - Généralement expédié sous 24 à 48 heures',
            'quantite' => '6',
            'date_creation' => '2014-10-29 00:00:00',
            'prix_lancement' => '899.00',
            'domaine' => 'Haute-fidélité',
            'categorie' => 'Amplificateurs',
            'souscategorie' => 'Amplis hi-fi stéréo',
            'url_site' => 'https://www.son-video.com/article/amplis-hi-fi-stereo/audio-analogue/armonia-airtech-crescendo-integre-noir',
            'url_image' => 'https://www.son-video.com/images/dynamic/Amplificateurs/articles/Audio_Analogue/AAATCRESCENDOINR/Audio-Analogue-ArmoniA-AirTech-Crescendo-Integre-Noir_P_450.jpg',
            'taux_marque' => '0.279',
            'souscategorie_id' => '16',
            'categorie_id' => '6',
            'domaine_id' => '3',
            'status' => 'last',
            'garantie' => '2',
            'poids' => '12.000',
            'ecotaxe' => '0.50',
            'url_image_big' => 'https://www.son-video.com/images/dynamic/Amplificateurs/articles/Audio_Analogue/AAATCRESCENDOINR/Audio-Analogue-ArmoniA-AirTech-Crescendo-Integre-Noir_P_450.jpg',
        ],
        [
            'sku' => 'AAATCRESCENDOISR',
            'prix' => '940.00',
            'prix_cilo_ht' => 0,
            'prix_svd_ttc' => '599.00',
            'frais_port' => '20.30',
            'modele' => 'ArmoniA AirTech Crescendo Intégré Silver',
            'ean13' => null,
            'marque' => 'Audio Analogue',
            'description' => 'Amplificateurs Stéréo Audio Analogue ArmoniA AirTech Crescendo Intégré Silver',
            'disponibilite' => 'En stock - Généralement expédié sous 24 à 48 heures',
            'quantite' => '4',
            'date_creation' => '2014-10-29 00:00:00',
            'prix_lancement' => '899.00',
            'domaine' => 'Haute-fidélité',
            'categorie' => 'Amplificateurs',
            'souscategorie' => 'Amplis hi-fi stéréo',
            'url_site' => 'https://www.son-video.com/article/amplis-hi-fi-stereo/audio-analogue/armonia-airtech-crescendo-integre-silver',
            'url_image' => 'https://www.son-video.com/images/dynamic/Amplificateurs/articles/Audio_Analogue/AAATCRESCENDOISR/Audio-Analogue-ArmoniA-AirTech-Crescendo-Integre-Silver_P_450.jpg',
            'taux_marque' => '0.279',
            'souscategorie_id' => '16',
            'categorie_id' => '6',
            'domaine_id' => '3',
            'status' => 'last',
            'garantie' => '2',
            'poids' => '12.000',
            'ecotaxe' => '0.50',
            'url_image_big' => 'https://www.son-video.com/images/dynamic/Amplificateurs/articles/Audio_Analogue/AAATCRESCENDOISR/Audio-Analogue-ArmoniA-AirTech-Crescendo-Integre-Silver_P_450.jpg',
        ],
    ];

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the CiloMarketplaceReadRepository mock. */
    private function getCiloMarketplaceReadRepository(): CiloMarketplaceReadRepository
    {
        $data_loader = self::$container->get(EntityDataLoader::class);

        $repository_mocked = $this->createMock(CiloMarketplaceReadRepository::class);
        $mocked_result = static::MOCKED_SQL_RESULTS;

        $repository_mocked
            ->method('fetchAllForCatalog')
            ->willReturnCallback(function () use ($mocked_result, $data_loader): array {
                $catalog = [];

                foreach ($mocked_result as $product) {
                    $catalog[] = $data_loader->hydrate($product, CiloProductEntity::class);
                }

                return $catalog;
            });

        return $repository_mocked;
    }

    /** Gets the tested class. */
    protected function getTestedClass(): Catalog
    {
        return new Catalog($this->getCiloMarketplaceReadRepository());
    }

    /** Tests the exportAsXml method. */
    public function test_export_as_xml(): void
    {
        // Check generated XML string
        $result = $this->getTestedClass()
            ->load()
            ->exportAsXml();

        $this->assertStringContainsString('<?xml version="1.0" encoding="ISO-8859-1"?>', $result);
        $this->assertStringContainsString(
            '<offer_reference type="SellerSku">AAATCRESCENDOINR</offer_reference>',
            $result
        );
        $this->assertStringContainsString(
            '<offer_reference type="SellerSku">AAATCRESCENDOISR</offer_reference>',
            $result
        );
    }
}
