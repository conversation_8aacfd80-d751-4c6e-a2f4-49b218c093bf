<?php
/*
 * This file is part of erp package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\ReleaseNote\Manager;

use SonVideo\Erp\ReleaseNote\Manager\ReleaseNoteIoResponseFormatter;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ReleaseNoteIoResponseFormatterTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests the format method. */
    public function test_format(): void
    {
        $data = [
            [
                'id' => '123ABC',
                'external_id' => null,
                'url' => 'https://son-video.releasenotes.io/release/123AB-private',
                'type' => 'feature',
                'private' => true,
                'status' => 'published',
                'released_at' => **********,
                'released_at_human' => '6 days ago',
                'title' => 'Titre de la release 2021-1',
                'description' => null,
                'notes' => [
                    [
                        'display_label' => 'features',
                        'label' => 'feature',
                        'order' => 1,
                        'notes' => [
                            [
                                'title' => '<p><code>WEB :</code> <a href="https://son-video.releasenotes.io/release/456DE-private">Lien vers une release note</a></p>',
                                'type' => 'feature',
                            ],
                            [
                                'title' => '<p><code>WEB :</code> Lien vers <a href="www.google.com">Google</a></p>',
                                'type' => 'feature',
                            ],
                            [
                                'title' => '<p>Un titre sans lien</p>',
                                'type' => 'feature',
                            ],
                        ],
                    ],
                ],
                'tags' => ['erp'],
            ],
        ];

        $results = (new ReleaseNoteIoResponseFormatter($data))->format();

        // Verify the results
        $this->assertCount(1, $results);
        $this->assertEquals('123ABC', $results[0]['release_note_id']);
        $this->assertEquals('feature', $results[0]['type']);
        $this->assertEquals('published', $results[0]['status']);
        $this->assertEquals('2021-11-22 13:32:16', $results[0]['released_at']);
        $this->assertEquals('Titre de la release 2021-1', $results[0]['title']);

        $this->assertCount(1, $results[0]['tags']);
        $this->assertEquals('erp', $results[0]['tags'][0]);

        $this->assertCount(1, $results[0]['extracted_notes']);
        $this->assertCount(3, $results[0]['extracted_notes']['features']);

        $this->assertEquals('WEB', $results[0]['extracted_notes']['features'][0]['subject']);
        $this->assertEquals('Lien vers une release note', $results[0]['extracted_notes']['features'][0]['description']);
        $this->assertEquals('456DE', $results[0]['extracted_notes']['features'][0]['release_note_id']);

        $this->assertEquals('WEB', $results[0]['extracted_notes']['features'][1]['subject']);
        $this->assertEquals(
            'Lien vers [Google](www.google.com)',
            $results[0]['extracted_notes']['features'][1]['description']
        );
        $this->assertNull($results[0]['extracted_notes']['features'][1]['release_note_id']);

        $this->assertNull($results[0]['extracted_notes']['features'][2]['subject']);
        $this->assertEquals('Un titre sans lien', $results[0]['extracted_notes']['features'][2]['description']);
        $this->assertNull($results[0]['extracted_notes']['features'][2]['release_note_id']);
    }
}
