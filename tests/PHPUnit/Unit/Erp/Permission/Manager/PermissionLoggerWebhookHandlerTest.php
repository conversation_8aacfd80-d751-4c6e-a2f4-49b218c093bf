<?php

namespace PHPUnit\Unit\Erp\Permission\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use <PERSON>\Uuid\UuidInterface;
use SonVideo\Erp\Account\DataProvider\AccountFromHalDataProvider;
use SonVideo\Erp\Account\Dto\LegacyAccountIdsForModifiedData;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Permission\Manager\PermissionLoggerWebhookHandler as TestedClass;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PermissionLoggerWebhookHandlerTest extends KernelTestCase
{
    private $logged;

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(
        SystemEventLogger $system_event_logger,
        AccountFromHalDataProvider $account_from_hal_data_provider,
        AccountQueryRepository $account_repository,
        QueryBuilder $query_builder
    ): TestedClass {
        $tested_class = new TestedClass(
            $system_event_logger,
            $account_from_hal_data_provider,
            $account_repository,
            $query_builder
        );

        $tested_class->setSerializer(self::$container->get(SerializerInterface::class));

        return $tested_class;
    }

    /** Gets a mocked system event logger. */
    protected function getSystemEventLogger(): SystemEventLogger
    {
        $instance = new class() extends SystemEventLogger {
            public static $logged;

            public function __construct()
            {
            }

            public function log(?LoggableSystemEvent $loggable_system_event): void
            {
                static::$logged = $loggable_system_event;
            }
        };

        $instance::$logged = &$this->logged;

        return $instance;
    }

    /** Gets a mocked account from HAL data provider. */
    protected function getAccountFromHalDataProvider(): AccountFromHalDataProvider
    {
        return new class() extends AccountFromHalDataProvider {
            public function __construct()
            {
            }
        };
    }

    /** Gets a mocked account repository. */
    protected function getAccountRepository(): AccountQueryRepository
    {
        return new class() extends AccountQueryRepository {};
    }

    /** Tests that the process method fails when payload does not have one of the required keys. */
    public function test_fails_when_payload_does_not_have_one_of_the_required_keys(): void
    {
        $instance = $this->getTestedInstance(
            $this->getSystemEventLogger(),
            $this->getAccountFromHalDataProvider(),
            $this->getAccountRepository(),
            self::$container->get(QueryBuilder::class)
        );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Argument supplied null or undefined, please check the logs');

        $instance->process([
            'event' => [
                'op' => 'INSERT',
                'session_variables' => [
                    'x-hasura-user-id' => '4d09c38c-18d6-44ae-9bab-2322396eb124',
                ],
                'data' => [
                    'new' => [
                        'permission_id' => 'CAN_DOMINATE_THA_WORLD',
                    ],
                ],
            ],
        ]);
    }

    /** Tests that the process method fails when action is not triggered by a user. */
    public function test_fails_when_action_is_not_triggered_by_a_user(): void
    {
        $instance = $this->getTestedInstance(
            $this->getSystemEventLogger(),
            $this->getAccountFromHalDataProvider(),
            $this->getAccountRepository(),
            self::$container->get(QueryBuilder::class)
        );

        $result = $instance->process([]);

        $this->assertEquals('Not triggered by a user action, nothing logged', $result['message']);
    }

    /** Tests that the process method fails when one of the IDs is not a valid UUID. */
    public function test_fails_when_payload_one_of_the_id_is_not_a_valid_uuid(): void
    {
        $instance = $this->getTestedInstance(
            $this->getSystemEventLogger(),
            $this->getAccountFromHalDataProvider(),
            $this->getAccountRepository(),
            self::$container->get(QueryBuilder::class)
        );

        // Test with invalid user ID
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid UUID string: wrong a');

        $instance->process([
            'event' => [
                'op' => 'INSERT',
                'session_variables' => [
                    'x-hasura-user-id' => 'wrong a',
                ],
                'data' => [
                    'new' => [
                        'owner_id' => 'wrong b',
                        'permission_id' => 'CAN_DOMINATE_THA_WORLD',
                    ],
                ],
            ],
        ]);
    }

    /** Tests that the process method fails when one of the IDs is not a valid UUID (second case). */
    public function test_fails_when_payload_one_of_the_id_is_not_a_valid_uuid_second_case(): void
    {
        $instance = $this->getTestedInstance(
            $this->getSystemEventLogger(),
            $this->getAccountFromHalDataProvider(),
            $this->getAccountRepository(),
            self::$container->get(QueryBuilder::class)
        );

        // Test with invalid owner ID
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid UUID string: wrong b');

        $instance->process([
            'event' => [
                'op' => 'INSERT',
                'session_variables' => [
                    'x-hasura-user-id' => '4d09c38c-18d6-44ae-9bab-2322396eb123',
                ],
                'data' => [
                    'new' => [
                        'owner_id' => 'wrong b',
                        'permission_id' => 'CAN_DOMINATE_THA_WORLD',
                    ],
                ],
            ],
        ]);
    }

    /** Tests that the process method fails when the amount of returned legacy accounts is wrong. */
    public function test_fails_when_the_amount_of_returned_legacy_accounts_is_wrong(): void
    {
        $hal_account = new class() extends AccountFromHalDataProvider {
            public static $return;

            public function __construct()
            {
            }

            public function getLegacyAccountIdsForModifiedData(
                UuidInterface $who_did_it,
                UuidInterface $for_who
            ): LegacyAccountIdsForModifiedData {
                return static::$return;
            }
        };

        $hal_account::$return = self::$container->get(SerializerInterface::class)->denormalize(
            [
                'who_did_it' => 1,
                'for_who' => 2,
            ],
            LegacyAccountIdsForModifiedData::class
        );

        $account_repo = new class() extends AccountQueryRepository {
            public static $return;

            public function findAllPaginated(QueryBuilder $query_builder): Pager
            {
                return (new Pager())->setResults(static::$return);
            }
        };

        $account_repo::$return = [
            [
                'user_id' => 1,
                'username' => 'toto',
                'firstname' => 'toto',
                'lastname' => 'toto',
            ],
        ];

        $instance = $this->getTestedInstance(
            $this->getSystemEventLogger(),
            $hal_account,
            $account_repo,
            self::$container->get(QueryBuilder::class)
        );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Expected 2 legacy accounts, got 1');

        $instance->process([
            'event' => [
                'op' => 'INSERT',
                'session_variables' => [
                    'x-hasura-user-id' => '4d09c38c-18d6-44ae-9bab-2322396eb124',
                ],
                'data' => [
                    'new' => [
                        'owner_id' => '4d09c38c-18d6-44ae-9bab-2322396eb125',
                        'permission_id' => 'CAN_DOMINATE_THA_WORLD',
                    ],
                ],
            ],
        ]);
    }

    /** Tests that the process method succeeds. */
    public function test_process_succeeds(): void
    {
        $hal_account = new class() extends AccountFromHalDataProvider {
            public static $return;

            public function __construct()
            {
            }

            public function getLegacyAccountIdsForModifiedData(
                UuidInterface $who_did_it,
                UuidInterface $for_who
            ): LegacyAccountIdsForModifiedData {
                return static::$return;
            }
        };

        $hal_account::$return = self::$container->get(SerializerInterface::class)->denormalize(
            [
                'who_did_it' => 1,
                'for_who' => 2,
            ],
            LegacyAccountIdsForModifiedData::class
        );

        $account_repo = new class() extends AccountQueryRepository {
            public static $return;

            public function findAllPaginated(QueryBuilder $query_builder): Pager
            {
                return (new Pager())->setResults(static::$return);
            }
        };

        $account_repo::$return = [
            [
                'user_id' => 1,
                'username' => 'toto',
                'firstname' => 'toto',
                'lastname' => 'toto',
            ],
            [
                'user_id' => 2,
                'username' => 'zozo',
                'firstname' => 'zozo',
                'lastname' => 'zozo',
            ],
        ];

        $instance = $this->getTestedInstance(
            $this->getSystemEventLogger(),
            $hal_account,
            $account_repo,
            self::$container->get(QueryBuilder::class)
        );

        $result = $instance->process([
            'event' => [
                'op' => 'INSERT',
                'session_variables' => [
                    'x-hasura-user-id' => '4d09c38c-18d6-44ae-9bab-2322396eb124',
                ],
                'data' => [
                    'new' => [
                        'owner_id' => '4d09c38c-18d6-44ae-9bab-2322396eb125',
                        'permission_id' => 'CAN_DOMINATE_THA_WORLD',
                    ],
                ],
            ],
        ]);

        // Check generic response
        $this->assertEquals('Information logged successfully', $result['message']);

        // Check what is logged is correct
        $this->assertEquals(LoggableSystemEvent::SYSTEM_ERP_PG, $this->logged->getSystem());
        $this->assertEquals('permission.insert', $this->logged->getName());
        $this->assertEquals(
            [
                '_rel' => [
                    'user' => 2,
                ],
                'meta' => [
                    'operation' => 'INSERT',
                    'permission' => 'CAN_DOMINATE_THA_WORLD',
                    'modified_by' => [
                        'account_id' => '4d09c38c-18d6-44ae-9bab-2322396eb124',
                        'user_id' => 1,
                        'username' => 'toto',
                        'firstname' => null,
                        'lastname' => null,
                    ],
                    'modified_for' => [
                        'account_id' => '4d09c38c-18d6-44ae-9bab-2322396eb125',
                        'user_id' => 2,
                        'username' => 'zozo',
                        'firstname' => null,
                        'lastname' => null,
                    ],
                ],
            ],
            $this->logged->getPayload()
        );
    }
}
