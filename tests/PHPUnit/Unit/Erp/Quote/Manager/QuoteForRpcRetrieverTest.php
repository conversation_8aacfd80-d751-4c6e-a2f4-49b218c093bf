<?php

namespace PHPUnit\Unit\Erp\Quote\Manager;

use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Quote\Entity\ForRpc\QuoteForRpcEntity;
use SonVideo\Erp\Quote\Entity\ProductInfoSelectedWarrantyEntity;
use SonVideo\Erp\Quote\Entity\QuoteLineEntity;
use SonVideo\Erp\Quote\Manager\QuoteForRpcRetriever as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteForRpcRetrieverTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/quote_for_rpc.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    public function test_fetch_one_quotation(): void
    {
        $quote = $this->getTestedInstance()->fetchOne(10);

        $this->assertInstanceOf(QuoteForRpcEntity::class, $quote);

        $extracted_quote = $quote->toArray();

        $this->assertEquals(10, $extracted_quote['quote_id']);
        $this->assertEquals('quotation', $extracted_quote['type']);
        $this->assertEquals('inactive', $extracted_quote['status']);
        $this->assertEquals('2022-03-03 16:20:00', $extracted_quote['created_at']);
        $this->assertEquals(2, $extracted_quote['customer_id']);
        $this->assertNull($extracted_quote['expired_at']);
        $this->assertEquals('Best of the best', $extracted_quote['message']);

        $this->assertEquals(
            [
                'city' => 'NANTES',
                'name' => 'Bureau',
                'phone' => '',
                'address' => '1 rue des fleurs',
                'country' => ['name' => 'FRANCE', 'country_id' => 67, 'country_code' => 'FR'],
                'civility' => 'M.',
                'lastname' => 'TERIEUR',
                'cellphone' => '0606060606',
                'firstname' => 'Alain',
                'created_at' => '2020-03-04 9:55:15',
                'postal_code' => '44100',
                'company_name' => 'La Cie',
            ],
            $extracted_quote['billing_address']
        );

        $this->assertEquals(
            [
                'city' => 'NANTES',
                'name' => 'Bureau',
                'phone' => '',
                'address' => '1 rue des fleurs',
                'country' => ['name' => 'FRANCE', 'country_id' => 67, 'country_code' => 'FR'],
                'civility' => 'M.',
                'lastname' => 'TERIEUR',
                'cellphone' => '0606060606',
                'firstname' => 'Alain',
                'created_at' => '2020-03-04 9:55:15',
                'postal_code' => '44100',
                'company_name' => 'La Cie',
            ],
            $extracted_quote['shipping_address']
        );

        $this->assertEquals(
            [
                'cost' => 4.99,
                'shipment_method_id' => 10,
            ],
            $extracted_quote['shipment_method']
        );

        $this->assertEqualsWithDelta(750.0, $extracted_quote['prices']['total_discount_tax_excluded'], 0.001);
        $this->assertEqualsWithDelta(900.0, $extracted_quote['prices']['total_discount_tax_included'], 0.001);
        $this->assertEqualsWithDelta(6056.6583333333, $extracted_quote['prices']['total_price_tax_excluded'], 0.001);
        $this->assertEqualsWithDelta(1211.33166666667, $extracted_quote['prices']['total_vat'], 0.001);
        $this->assertEqualsWithDelta(7267.99, $extracted_quote['prices']['total_price_tax_included'], 0.001);
        $this->assertEqualsWithDelta(1.2, $extracted_quote['prices']['computed_vat_rate'], 0.001);

        $this->assertEquals(
            [
                'display_order' => 1,
                'type' => QuoteLineEntity::TYPE_PRODUCT,
                'data' => [
                    'product' => [
                        'product_id' => 128416,
                        'sku' => 'QACOQ3050INRMT',
                        'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                        'short_description' => 'Arcam rBlink',
                        'image' => '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
                        'selling_price_tax_included' => 1200.0,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'vat' => 0.2,
                        'type' => 'article',
                    ],
                    'quantity' => 1,
                    'selling_price_tax_excluded' => 1000.0,
                    'selling_price_tax_included' => 1200.0,
                    'unit_discount_amount_abs_tax_included' => 300.0,
                    'total_discount_amount' => 300.0,
                    'total_price' => 900.0,
                    'selected_warranties' => [],
                ],
            ],
            $extracted_quote['lines'][0]
        );

        $this->assertEquals(
            [
                'display_order' => 2,
                'type' => QuoteLineEntity::TYPE_PRODUCT,
                'data' => [
                    'product' => [
                        'product_id' => 81123,
                        'sku' => 'ELIPSPLANETMSUBBC',
                        'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                        'short_description' => 'Arcam rBlink',
                        'image' => '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
                        'selling_price_tax_included' => 600.0,
                        'ecotax_price' => 0.5,
                        'sorecop_price' => 1.0,
                        'vat' => 0.2,
                        'type' => 'article',
                    ],
                    'quantity' => 4,
                    'selling_price_tax_excluded' => 500.0,
                    'selling_price_tax_included' => 600.0,
                    'unit_discount_amount_abs_tax_included' => 50.0,
                    'total_discount_amount' => 200.0,
                    'total_price' => 2200.0,
                    'selected_warranties' => [],
                ],
            ],
            $extracted_quote['lines'][1]
        );

        $this->assertEquals(
            [
                'display_order' => 3,
                'type' => QuoteLineEntity::TYPE_SECTION,
                'data' => [
                    'label' => 'Ceci est une section',
                ],
            ],
            $extracted_quote['lines'][2]
        );

        $this->assertEquals(
            [
                'display_order' => 4,
                'type' => QuoteLineEntity::TYPE_PRODUCT,
                'data' => [
                    'product' => [
                        'product_id' => 81078,
                        'sku' => 'QACOQ3050IBCMT',
                        'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                        'short_description' => 'Arcam rBlink',
                        'image' => '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
                        'selling_price_tax_included' => 1200.0,
                        'ecotax_price' => 0.0,
                        'sorecop_price' => 0.0,
                        'vat' => 0.2,
                        'type' => 'article',
                    ],
                    'quantity' => 2,
                    'selling_price_tax_excluded' => 1000.0,
                    'selling_price_tax_included' => 1200.0,
                    'unit_discount_amount_abs_tax_included' => 200.0,
                    'total_discount_amount' => 400.0,
                    'total_price' => 2000.0,
                    'selected_warranties' => [
                        [
                            'type' => ProductInfoSelectedWarrantyEntity::TYPE_EXTENSION,
                            'label' => 'Extension de garantie 5 ans',
                            'unit_selling_price_tax_included' => 79.5,
                            'duration' => 5,
                        ],
                        [
                            'type' => ProductInfoSelectedWarrantyEntity::TYPE_THEFT_BREAKDOWN,
                            'label' => 'Garantie vol-casse 2 ans',
                            'unit_selling_price_tax_included' => 1002.0,
                            'duration' => 2,
                        ],
                    ],
                ],
            ],
            $extracted_quote['lines'][3]
        );

        $this->assertEquals(
            [
                'display_order' => 4,
                'type' => QuoteLineEntity::TYPE_PRODUCT_WARRANTY,
                'data' => [
                    'type' => ProductInfoSelectedWarrantyEntity::TYPE_EXTENSION,
                    'label' => 'Extension de garantie 5 ans',
                    'duration' => 5,
                    'quantity' => 2,
                    'unit_selling_price_tax_included' => 79.5,
                    'price_tax_excluded' => 66.25,
                    'total_price_tax_included' => 159.0,
                    'total_price_tax_excluded' => 132.5,
                ],
            ],
            $extracted_quote['lines'][4]
        );

        $this->assertEquals(
            [
                'display_order' => 4,
                'type' => QuoteLineEntity::TYPE_PRODUCT_WARRANTY,
                'data' => [
                    'type' => ProductInfoSelectedWarrantyEntity::TYPE_THEFT_BREAKDOWN,
                    'label' => 'Garantie vol-casse 2 ans',
                    'duration' => 2,
                    'quantity' => 2,
                    'unit_selling_price_tax_included' => 1002.0,
                    'price_tax_excluded' => 835.0,
                    'total_price_tax_included' => 2004.0,
                    'total_price_tax_excluded' => 1670.0,
                ],
            ],
            $extracted_quote['lines'][5]
        );
    }

    public function test_fetch_one_offer(): void
    {
        $quote = $this->getTestedInstance()->fetchOne(11);

        $this->assertInstanceOf(QuoteForRpcEntity::class, $quote);

        $extracted_quote = $quote->toArray();

        $this->assertEquals('offer', $extracted_quote['type']);
        $this->assertNull($extracted_quote['shipping_address']);
        $this->assertNull($extracted_quote['billing_address']);
        $this->assertNull($extracted_quote['shipment_method']);
    }
}
