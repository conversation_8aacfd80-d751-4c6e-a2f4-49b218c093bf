<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Quote\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\PHPUnit\PhpUnitPommHelperTrait;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Quote\Manager\QuoteCreator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteCreatorTest extends KernelTestCase
{
    use PhpUnitPommHelperTrait;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql', 'quote/post_quote.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): QuoteCreator
    {
        return self::$container->get(QuoteCreator::class);
    }

    /** Tests the quote creation functionality. */
    public function test_create(): void
    {
        // Create a new quote
        $new_quote_id = $this->getTestedInstance()->create(
            ['customer_id' => 1500001],
            self::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        // Verify quote data
        $new_quote = $this->fetchQuote($new_quote_id);
        $this->assertEquals(1, (int) $new_quote['quote_id']);
        $this->assertEquals(QuoteEntity::TYPE_DRAFT, $new_quote['type']);
        $this->assertNotNull($new_quote['created_at']);
        $this->assertEquals(1, (int) $new_quote['created_by']);
        $this->assertNull($new_quote['modified_by']); // not cloned
        $this->assertEquals(1500001, (int) $new_quote['customer_id']);
        $this->assertNull($new_quote['sent_at']); // not cloned
        $this->assertNull($new_quote['expired_at']); // not cloned
        $this->assertEquals(15, (int) $new_quote['valid_until']);
        $this->assertEquals(
            'Suite à votre demande voici notre offre pour les produits suivants:',
            $new_quote['message']
        );
        $this->assertNull($new_quote['billing_address']);
        $this->assertNull($new_quote['shipping_address']);
        $this->assertNull($new_quote['shipment_method']);
        $this->assertEquals('CLASSIQUE', $new_quote['quote_subtype']);

        // Verify creation log on new quote
        $log = $this->fetchLog($new_quote_id);
        $payload = json_decode($log['payload'], false, 512, JSON_THROW_ON_ERROR);
        $this->assertEquals(QuoteEntity::TYPE_DRAFT, $payload->data->type);
        $this->assertEquals(1, $payload->meta->created_by->user_id);
    }

    /** Tests quote creation with force selection quote subtype. */
    public function test_create_with_force_selection_quote_subtype(): void
    {
        // Set force selection quote subtype to true
        $this->setForceSelectionQuoteSubtypeTrue();

        // Create a new quote
        $new_quote_id = $this->getTestedInstance()->create(
            ['customer_id' => 1500001],
            self::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        // Verify quote data
        $new_quote = $this->fetchQuote($new_quote_id);
        $this->assertEquals(2, (int) $new_quote['quote_id']);
        $this->assertEquals(QuoteEntity::TYPE_DRAFT, $new_quote['type']);
        $this->assertNotNull($new_quote['created_at']);
        $this->assertEquals(1, (int) $new_quote['created_by']);
        $this->assertNull($new_quote['modified_by']); // not cloned
        $this->assertEquals(1500001, (int) $new_quote['customer_id']);
        $this->assertNull($new_quote['sent_at']); // not cloned
        $this->assertNull($new_quote['expired_at']); // not cloned
        $this->assertEquals(15, (int) $new_quote['valid_until']);
        $this->assertEquals(
            'Suite à votre demande voici notre offre pour les produits suivants:',
            $new_quote['message']
        );
        $this->assertNull($new_quote['billing_address']);
        $this->assertNull($new_quote['shipping_address']);
        $this->assertNull($new_quote['shipment_method']);
        $this->assertNull($new_quote['quote_subtype']);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches a quote by ID. */
    protected function fetchQuote(int $quote_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.quote
        WHERE quote_id = :quote_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['quote_id' => $quote_id]);
    }

    /** Fetches a log entry for a quote. */
    protected function fetchLog(int $quote_id): array
    {
        $sql = <<<SQL
        SELECT *
          FROM backOffice.system_event
        WHERE name = 'quote.create'
        AND payload->"$._rel.quote" = :quote_id
        AND main_id = :quote_id
        ;
        SQL;

        return $this->getPdo()->fetchOne($sql, ['quote_id' => $quote_id]);
    }

    /** Sets the force selection quote subtype to true. */
    protected function setForceSelectionQuoteSubtypeTrue(): void
    {
        $sql = <<<SQL
        UPDATE  permission.owner
        SET meta = meta ||  '{"preferences": {"erp": {"force_selection_quote_subtype": true}}}'::jsonb
        WHERE owner_id = '9e81bd23-e7ac-4ba3-842f-8da6554bc540';
        SQL;

        $this->executeSqlQuery($sql);
    }
}
