<?php

namespace PHPUnit\Unit\Erp\Quote\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Quote\Manager\QuoteProductLineCreator as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteProductLineCreatorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/post_quote_line_product.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    public function test_add_to_packaged_article_with_one_product(): void
    {
        $this->getTestedInstance()->addTo(
            12,
            'NORSTB250100M',
            self::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        $quote_line_products = $this->fetchQuoteLineProducts(12);

        $this->assertEquals(78226, (int) $quote_line_products[0]->product_id);
        $this->assertEquals(100, (int) $quote_line_products[0]->quantity);
        $this->assertEquals(-0.7, (float) $quote_line_products[0]->unit_discount_amount);
        $this->assertNotNull($quote_line_products[0]->product);
    }

    public function test_add_to_packaged_article_with_multi_products(): void
    {
        $this->getTestedInstance()->addTo(
            13,
            'ELIPSPRESTIGEPK2I',
            self::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        $quote_line_products = $this->fetchQuoteLineProducts(13);

        $this->assertEquals(72215, (int) $quote_line_products[0]->product_id);
        $this->assertEquals(2, (int) $quote_line_products[0]->quantity);
        $this->assertEquals(-45.88, (float) $quote_line_products[0]->unit_discount_amount);
        $this->assertNotNull($quote_line_products[0]->product);

        // eligible_warranties has been added successfully
        $product_data = json_decode($quote_line_products[0]->product, JSON_THROW_ON_ERROR, 512, JSON_THROW_ON_ERROR);
        $this->assertCount(1, $product_data['eligible_warranties']);

        $this->assertEquals(72216, (int) $quote_line_products[1]->product_id);
        $this->assertEquals(1, (int) $quote_line_products[1]->quantity);
        $this->assertEquals(-36.24, (float) $quote_line_products[1]->unit_discount_amount);
        $this->assertNotNull($quote_line_products[1]->product);

        // eligible_warranties has been added successfully
        $product_data = json_decode($quote_line_products[1]->product, JSON_THROW_ON_ERROR, 512, JSON_THROW_ON_ERROR);
        $this->assertCount(1, $product_data['eligible_warranties']);
    }

    public function test_add_to_packaged_article_with_multi_products_more_expensive(): void
    {
        $this->getTestedInstance()->addTo(
            14,
            'FOCASIBEVO712NRV2',
            self::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        $quote_line_products = $this->fetchQuoteLineProducts(14);

        $this->assertEquals(118223, (int) $quote_line_products[0]->product_id);
        $this->assertEquals(1, (int) $quote_line_products[0]->quantity);
        $this->assertEquals(0, (float) $quote_line_products[0]->unit_discount_amount);
        $this->assertNotNull($quote_line_products[0]->product);

        $selected_warranties = json_decode(
            $quote_line_products[0]->selected_warranties,
            JSON_THROW_ON_ERROR,
            512,
            JSON_THROW_ON_ERROR
        );
        $this->assertEmpty($selected_warranties);

        $this->assertEquals(118230, (int) $quote_line_products[1]->product_id);
        $this->assertEquals(1, (int) $quote_line_products[1]->quantity);
        $this->assertEquals(0, (float) $quote_line_products[1]->unit_discount_amount);
        $this->assertNotNull($quote_line_products[1]->product);

        $selected_warranties = json_decode(
            $quote_line_products[1]->selected_warranties,
            JSON_THROW_ON_ERROR,
            512,
            JSON_THROW_ON_ERROR
        );
        $this->assertEmpty($selected_warranties);
    }

    public function test_add_to_article_eligible_to_extended_warranty(): void
    {
        $this->getTestedInstance()->addTo(
            15,
            'FOCASIBATMEVO512NR',
            self::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        $quote_line_products = $this->fetchQuoteLineProducts(15);

        $this->assertEquals(118230, (int) $quote_line_products[0]->product_id);
        $this->assertEquals(1, (int) $quote_line_products[0]->quantity);
        $this->assertEquals(0, (float) $quote_line_products[0]->unit_discount_amount);
        $this->assertNotNull($quote_line_products[0]->product);
        $this->assertStringContainsString(
            '[{"type": "extension", "label": "Garantie premium 5 ans", "duration": 5, "unit_selling_price_tax_included": 129}]',
            $quote_line_products[0]->product
        );

        $selected_warranties = json_decode(
            $quote_line_products[0]->selected_warranties,
            JSON_THROW_ON_ERROR,
            512,
            JSON_THROW_ON_ERROR
        );
        $this->assertEmpty($selected_warranties);
    }

    public function test_add_to_quote_already_containing_one_should_set_same_warranties(): void
    {
        $this->getTestedInstance()->addTo(
            11,
            'ARCAMRBLINKNR',
            self::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        $quote_line_products = $this->fetchQuoteLineProducts(11);

        $selected_warranties_1 = json_decode(
            $quote_line_products[1]->selected_warranties,
            JSON_THROW_ON_ERROR,
            512,
            JSON_THROW_ON_ERROR
        );
        $selected_warranties_0 = json_decode(
            $quote_line_products[0]->selected_warranties,
            JSON_THROW_ON_ERROR,
            512,
            JSON_THROW_ON_ERROR
        );

        $this->assertNotEmpty($selected_warranties_1);
        $this->assertEquals($selected_warranties_0, $selected_warranties_1);
    }

    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    protected function fetchQuoteLineProducts(int $quote_id): array
    {
        $sql = <<<SQL
        SELECT qlp.*
        FROM backOffice.quote_line_product qlp
          INNER JOIN backOffice.quote_line ql ON qlp.quote_line_id = ql.quote_line_id
        WHERE ql.quote_id = :quote_id
        ORDER BY qlp.product_id
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['quote_id' => $quote_id]);
    }
}
