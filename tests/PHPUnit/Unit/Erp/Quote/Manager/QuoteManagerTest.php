<?php

namespace PHPUnit\Unit\Erp\Quote\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Quote\Exception\QuoteValidationException;
use SonVideo\Erp\Quote\Manager\QuoteManager as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql', 'quote/put_quote.sql']);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    public function test_update_with_invalid_data(): void
    {
        $quote_manager = $this->getTestedInstance();
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        foreach ($this->getInvalidData() as $data) {
            try {
                $quote_manager->update(13, $data['payload'], $user);
                $this->fail('Expected QuoteValidationException was not thrown');
            } catch (QuoteValidationException $e) {
                $this->assertStringContainsString($data['error'], $e->getMessage());
            }
        }
    }

    public function test_update_with_valid_data(): void
    {
        $quote_manager = $this->getTestedInstance();
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        foreach ($this->getValidData() as $data) {
            $updated = $quote_manager->update(13, $data, $user);
            $this->assertEquals(1, $updated);
        }
    }

    public function test_update_with_special_characters(): void
    {
        $quote_manager = $this->getTestedInstance();
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $quote_manager->update(
            13,
            [
                'message' => '‭Toto‬',
                'shipping_address' => [
                    'name' => 'Mon adresse enregistrée',
                    'address' => '10 rue du Pont',
                    'cellphone' => '‭+33 6 01 02 03 04‬',
                    'city' => 'Nantes',
                    'civility' => 'Mme',
                    'company_name' => 'Son-Vidéo.com',
                    'country' => [
                        'country_code' => 'FR',
                        'country_id' => 67,
                        'name' => 'FRANCE',
                    ],
                    'firstname' => 'Tata',
                    'lastname' => 'Zozo',
                    'phone' => '‭+33 2 01 02 03 04‬',
                    'postal_code' => '44000',
                ],
                'billing_address' => [
                    'address' => '10 rue du Pont',
                    'cellphone' => '🌈+33 2 01 02🌡 03 04',
                    'city' => 'Nantes',
                    'civility' => 'Mme',
                    'company_name' => 'Son-Vidéo.com',
                    'country' => [
                        'country_code' => 'FR',
                        'country_id' => 67,
                        'name' => 'FRANCE',
                    ],
                    'created_at' => '2020-03-31 13:58:05',
                    'firstname' => 'Tata',
                    'lastname' => 'Zozo',
                    'postal_code' => '44000',
                ],
            ],
            $user
        );

        $updated_quote = $this->fetchQuote(13)[0];
        $shipping_address = json_decode($updated_quote->shipping_address, null, 512, JSON_THROW_ON_ERROR);
        $billing_address = json_decode($updated_quote->shipping_address, null, 512, JSON_THROW_ON_ERROR);

        $this->assertEquals('Toto', $updated_quote->message);
        $this->assertEquals('+33 6 01 02 03 04', $shipping_address->cellphone);
        $this->assertEquals('+33 2 01 02 03 04', $shipping_address->phone);
        $this->assertEquals('+33 6 01 02 03 04', $billing_address->cellphone);
    }

    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    private function fetchQuote(int $quote_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.quote
        WHERE quote_id = :quote_id
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['quote_id' => $quote_id]);
    }

    private function getInvalidData(): array
    {
        return [
            [
                'error' => 'Shipping address is invalid : [cellphone]: This value should not be blank.',
                'payload' => [
                    'shipping_address' => [
                        'address' => '10 rue du Pont',
                        'cellphone' => null,
                        'city' => 'Nantes',
                        'civility' => 'M.',
                        'company_name' => '',
                        'country' => [
                            'country_code' => 'FR',
                            'country_id' => 67,
                            'name' => 'FRANCE',
                        ],
                        'firstname' => 'Tata',
                        'lastname' => 'Zozo',
                        'phone' => '',
                        'postal_code' => '44000',
                    ],
                ],
            ],
            [
                'error' => 'Shipping address is invalid : [address]: This value should not be blank.
 [cellphone]: This value should not be blank.
 [city]: This value should not be blank.
 [civility]: This value should not be blank.
 [country][country_code]: This value should not be blank.
 [country][country_id]: This value should not be blank.
 [country][name]: This value should not be blank.
 [firstname]: This value should not be blank.
 [lastname]: This field is missing.
 [postal_code]: This value should not be blank.',
                'payload' => [
                    'shipping_address' => [
                        'address' => '',
                        'cellphone' => '',
                        'city' => '',
                        'civility' => '',
                        'company_name' => '',
                        'country' => [
                            'country_code' => '',
                            'country_id' => null,
                            'name' => '',
                        ],
                        'firstname' => '',
                        'phone' => '',
                        'postal_code' => '',
                    ],
                ],
            ],
            [
                'error' => 'Shipping address is invalid : [address]: This field is missing.
 [cellphone]: This field is missing.
 [city]: This field is missing.
 [civility]: This field is missing.
 [country]: This field is missing.
 [firstname]: This field is missing.
 [lastname]: This field is missing.
 [postal_code]: This field is missing.',
                'payload' => [
                    'shipping_address' => [],
                ],
            ],
        ];
    }

    private function getValidData(): array
    {
        return [
            [
                'shipping_address' => [
                    'address' => '10 rue du Pont',
                    'cellphone' => '06 01 02 03 04',
                    'city' => 'Nantes',
                    'civility' => 'M.',
                    'company_name' => null,
                    'country' => [
                        'country_code' => 'FR',
                        'country_id' => 67,
                        'name' => 'FRANCE',
                    ],
                    'firstname' => 'Tata',
                    'lastname' => 'Zozo',
                    'phone' => null,
                    'postal_code' => '44000',
                ],
            ],
            [
                'shipping_address' => [
                    'name' => 'Mon adresse enregistrée',
                    'address' => '10 rue du Pont',
                    'cellphone' => '+33 6 01 02 03 04',
                    'city' => 'Nantes',
                    'civility' => 'Mme',
                    'company_name' => 'Son-Vidéo.com',
                    'country' => [
                        'country_code' => 'FR',
                        'country_id' => 67,
                        'name' => 'FRANCE',
                    ],
                    'firstname' => 'Tata',
                    'lastname' => 'Zozo',
                    'phone' => null,
                    'postal_code' => '44000',
                ],
            ],
        ];
    }
}
