<?php

namespace PHPUnit\Unit\Erp\Quote\Manager\Manager\Document;

use App\DataLoader\EntityDataLoader;
use App\Entity\OptionalColumnsLoader;
use App\Exception\NotFoundException;
use App\Sql\Query\QueryBuilder;
use App\Tests\Utils\Database\MySqlDatabase;
use League\Flysystem\MountManager;
use SonVideo\Erp\Document\Manager\HtmlToPdfGenerator;
use SonVideo\Erp\Quote\Manager\Decorator\QuoteDecorator;
use SonVideo\Erp\Quote\Manager\Document\QuotePdfDocument;
use SonVideo\Erp\Quote\Mysql\Repository\QuoteRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuotePdfDocumentTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Set up database fixtures once before all tests
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql', 'quote/quote_pdf.sql']);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Get the instance to test */
    protected function getTestedInstance(): QuotePdfDocument
    {
        // Create mock for PDF generator
        $pdfGenerator = $this->createMock(HtmlToPdfGenerator::class);
        $pdfGenerator->method('generate')->willReturn('ok');

        $generator = new QuotePdfDocument(
            $pdfGenerator,
            self::$container->get(QueryBuilder::class),
            self::$container->get(QuoteRepository::class),
            self::$container->get(OptionalColumnsLoader::class),
            self::$container->get(QuoteDecorator::class),
            self::$container->get(MountManager::class)
        );

        $generator->setDataLoader(self::$container->get(EntityDataLoader::class));

        return $generator;
    }

    public function test_generate_throws_exception_if_quote_id_not_provided(): void
    {
        $generator = $this->getTestedInstance();

        $this->expectException(\UnexpectedValueException::class);
        $this->expectExceptionMessage('Quote id must be provided.');

        $generator->generate([]);
    }

    public function test_generate_throws_exception_if_quote_does_not_exist(): void
    {
        $generator = $this->getTestedInstance();

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Quote not found with id "666"');

        $generator->generate(['quote_id' => 666]);
    }

    public function test_can_generate_expired_quotation_type_pdf_successfully(): void
    {
        $generator = $this->getTestedInstance();

        $result = $generator->generate(['quote_id' => 10]);

        $this->assertEquals('ok', $result);
    }

    public function test_can_retrieve_already_generated_quotation_type_pdf(): void
    {
        $generator = $this->getTestedInstance();

        $result = $generator->generate(['quote_id' => 10]);

        $this->assertEquals('ok', $result);
    }

    public function test_can_regenerate_quotation_type_which_is_not_inactive(): void
    {
        $generator = $this->getTestedInstance();

        $result = $generator->generate(['quote_id' => 10, 'regenerate' => true]);

        $this->assertEquals('ok', $result);
    }

    public function test_can_generate_offer_type_pdf_successfully(): void
    {
        $generator = $this->getTestedInstance();

        $result = $generator->generate(['quote_id' => 11]);

        $this->assertEquals('ok', $result);
    }

    public function test_can_retrieve_already_generated_offer_type_pdf(): void
    {
        $generator = $this->getTestedInstance();

        $result = $generator->generate(['quote_id' => 11]);

        $this->assertEquals('ok', $result);
    }

    public function test_can_regenerate_offer_type_pdf_regardless_of_status(): void
    {
        $generator = $this->getTestedInstance();

        $result = $generator->generate(['quote_id' => 11, 'regenerate' => true]);

        $this->assertEquals('ok', $result);
    }
}
