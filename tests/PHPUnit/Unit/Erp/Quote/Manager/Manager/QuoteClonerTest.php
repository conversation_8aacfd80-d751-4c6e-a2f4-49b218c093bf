<?php

namespace PHPUnit\Unit\Erp\Quote\Manager\Manager;

use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\PHPUnit\PhpUnitPommHelperTrait;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Quote\Exception\CloneQuoteException;
use SonVideo\Erp\Quote\Manager\QuoteCloner as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteClonerTest extends KernelTestCase
{
    use PhpUnitPommHelperTrait;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/post_clone_quote.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** @throws \Exception */
    protected function getTestedInstance(): TestedClass
    {
        return static::$container->get(TestedClass::class);
    }

    /**
     * @throws NotFoundException
     * @throws CloneQuoteException
     */
    public function test_clone(): void
    {
        $this->setForceSelectionQuoteSubtype(false);

        // Original quote data
        $original_quote = $this->fetchQuote(10);
        $original_quote_line_products = $this->fetchQuoteLineProducts(10);

        // Clone the quote
        $cloned_quote_id = $this->getTestedInstance()->clone(
            10,
            static::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        // Test quote data are cloned properly
        $cloned_quote = $this->fetchQuote($cloned_quote_id);

        // Verify original quote properties
        $this->assertEquals(10, (int) $original_quote['quote_id']);
        $this->assertEquals(QuoteEntity::TYPE_QUOTATION, $original_quote['type']);
        $this->assertEquals('2021-02-02 16:00:00', $original_quote['created_at']);
        $this->assertEquals(2, (int) $original_quote['created_by']);
        $this->assertEquals(2, (int) $original_quote['modified_by']);
        $this->assertEquals(1, (int) $original_quote['customer_id']);
        $this->assertEquals('2021-04-04 12:00:00', $original_quote['sent_at']);
        $this->assertEquals('2021-04-04 16:00:00', $original_quote['expired_at']);
        $this->assertEquals(99, (int) $original_quote['valid_until']);
        $this->assertEquals('ceci est un message', $original_quote['message']);
        $this->assertEquals('{"foo": "bar"}', $original_quote['billing_address']);
        $this->assertEquals('{"foo": "bar"}', $original_quote['shipping_address']);
        $this->assertEquals(
            '{"cost": 4.99, "is_retail_store": false, "shipment_method_id": 10}',
            $original_quote['shipment_method']
        );

        // Verify cloned quote properties
        // do not test the quote_id as tests are run in random order and you can't predict the value
        $this->assertEquals(QuoteEntity::TYPE_DRAFT, $cloned_quote['type']);
        $this->assertNotEquals('2021-02-02 16:00:00', $cloned_quote['created_at']); // creation date should have changed
        $this->assertEquals(2, (int) $cloned_quote['created_by']); // user who created the first quote
        $this->assertNull($cloned_quote['modified_by']); // not cloned
        $this->assertEquals(1, (int) $cloned_quote['customer_id']);
        $this->assertNull($cloned_quote['sent_at']); // not cloned
        $this->assertNull($cloned_quote['expired_at']); // not cloned
        $this->assertEquals(99, (int) $cloned_quote['valid_until']);
        $this->assertNull($cloned_quote['message']); // not cloned
        $this->assertEquals('{"foo": "bar"}', $cloned_quote['billing_address']);
        $this->assertEquals('{"foo": "bar"}', $cloned_quote['shipping_address']);
        $this->assertEquals(
            '{"cost": 4.99, "is_retail_store": false, "shipment_method_id": 10}',
            $cloned_quote['shipment_method']
        );
        $this->assertEquals('CLASSIQUE', $cloned_quote['quote_subtype']); // not cloned

        // Test quote line products are cloned properly
        $cloned_quote_line_products = $this->fetchQuoteLineProducts($cloned_quote_id);

        // Verify original quote line products
        $this->assertEquals(81078, (int) $original_quote_line_products[0]->product_id);
        $this->assertEquals(1, (int) $original_quote_line_products[0]->quantity);
        $this->assertEquals(0, (float) $original_quote_line_products[0]->unit_discount_amount);
        $this->assertNotNull($original_quote_line_products[0]->product);

        $this->assertEquals(81123, (int) $original_quote_line_products[1]->product_id);
        $this->assertEquals(23, (int) $original_quote_line_products[1]->quantity);
        $this->assertEquals(-12.34, (float) $original_quote_line_products[1]->unit_discount_amount);
        $this->assertNotNull($original_quote_line_products[1]->product);

        // Verify cloned quote line products
        $this->assertEquals(81078, (int) $cloned_quote_line_products[0]->product_id);
        $this->assertEquals(1, (int) $cloned_quote_line_products[0]->quantity);
        $this->assertEquals(0, (float) $cloned_quote_line_products[0]->unit_discount_amount);
        $this->assertNotNull($cloned_quote_line_products[0]->product);
        $this->assertEmpty(json_decode($cloned_quote_line_products[0]->selected_warranties, JSON_THROW_ON_ERROR)); // null in fixtures

        $this->assertEquals(81123, (int) $cloned_quote_line_products[1]->product_id);
        $this->assertEquals(23, (int) $cloned_quote_line_products[1]->quantity);
        $this->assertEquals(0, (float) $cloned_quote_line_products[1]->unit_discount_amount);
        $this->assertNotNull($cloned_quote_line_products[1]->product);
        $this->assertEmpty(json_decode($cloned_quote_line_products[1]->selected_warranties, JSON_THROW_ON_ERROR)); // valued in fixtures

        // Test creation log on original quote is created
        $log = $this->fetchLog(10);
        $payload = json_decode($log['payload'], false);
        $this->assertEquals(11, $payload->data->to);
        $this->assertEquals(1, $payload->meta->cloned_by->user_id);

        // Test creation log on new quote is created
        $log = $this->fetchLog($cloned_quote_id);
        $payload = json_decode($log['payload'], false);
        $this->assertEquals(10, $payload->data->from);
        $this->assertEquals(1, $payload->meta->cloned_by->user_id);
    }

    public function test_force_selection_quote_subtype(): void
    {
        // Set force selection quote subtype to true
        $this->setForceSelectionQuoteSubtype(true);

        // Clone the quote
        $cloned_quote_id = $this->getTestedInstance()->clone(
            10,
            static::$container->get(AccountQueryRepository::class)->getUser('admin')
        );

        // Test quote data are cloned properly
        $cloned_quote = $this->fetchQuote($cloned_quote_id);

        // Verify cloned quote properties
        // do not test the quote_id as tests are run in random order and you can't predict the value
        $this->assertEquals(QuoteEntity::TYPE_DRAFT, $cloned_quote['type']);
        $this->assertNotEquals('2021-02-02 16:00:00', $cloned_quote['created_at']); // creation date should have changed
        $this->assertEquals(2, (int) $cloned_quote['created_by']); // user who created the first quote
        $this->assertNull($cloned_quote['modified_by']); // not cloned
        $this->assertEquals(1, (int) $cloned_quote['customer_id']);
        $this->assertNull($cloned_quote['sent_at']); // not cloned
        $this->assertNull($cloned_quote['expired_at']); // not cloned
        $this->assertEquals(99, (int) $cloned_quote['valid_until']);
        $this->assertNull($cloned_quote['message']); // not cloned
        $this->assertEquals('{"foo": "bar"}', $cloned_quote['billing_address']);
        $this->assertEquals('{"foo": "bar"}', $cloned_quote['shipping_address']);
        $this->assertEquals(
            '{"cost": 4.99, "is_retail_store": false, "shipment_method_id": 10}',
            $cloned_quote['shipment_method']
        );
        $this->assertNull($cloned_quote['quote_subtype']); // not cloned
    }

    /** @throws \Exception */
    protected function getPdo(): LegacyPdo
    {
        return static::$container->get(LegacyPdo::class);
    }

    /**
     * @return mixed
     *
     * @throws \Exception
     */
    protected function fetchQuote(int $quote_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.quote
        WHERE quote_id = :quote_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['quote_id' => $quote_id]);
    }

    /**
     * @return mixed
     *
     * @throws \Exception
     */
    protected function fetchQuoteLineProducts(int $quote_id)
    {
        $sql = <<<SQL
        SELECT qlp.*
        FROM backOffice.quote_line_product qlp
          INNER JOIN backOffice.quote_line ql ON qlp.quote_line_id = ql.quote_line_id
        WHERE ql.quote_id = :quote_id
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['quote_id' => $quote_id]);
    }

    /**
     * @return false|array
     *
     * @throws \Exception
     */
    protected function fetchLog(int $quote_id)
    {
        $sql = <<<SQL
        SELECT *
          FROM backOffice.system_event
        WHERE name = 'quote.clone'
        AND payload->"$._rel.quote" = :quote_id
        AND main_id = :quote_id
        ;
        SQL;

        return $this->getPdo()->fetchOne($sql, ['quote_id' => $quote_id]);
    }

    public function setForceSelectionQuoteSubtype(bool $value): void
    {
        $sql = <<<SQL
        UPDATE  permission.owner
        SET meta = meta ||  '{"preferences": {"erp": {"force_selection_quote_subtype": {value} }}}'::jsonb
        WHERE owner_id = '9e81bd23-e7ac-4ba3-842f-8da6554bc540';
        SQL;

        $this->getPommSession('admin')
            ->getConnection()
            ->executeAnonymousQuery(strtr($sql, ['{value}' => $value ? 'true' : 'false']));
    }
}
