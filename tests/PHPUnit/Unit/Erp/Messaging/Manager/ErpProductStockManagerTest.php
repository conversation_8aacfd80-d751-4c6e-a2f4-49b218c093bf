<?php

namespace SonVideo\Erp\Tests\PHPUnit\Unit\Messaging\Manager;

use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Message\ErpProductStockMessage;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\NullLogger;
use SonVideo\Erp\Messaging\Manager\ErpProduct\ErpProductStockManager;
use SonVideo\Erp\Messaging\Repository\ErpProduct\ErpProductStockReadRepository;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

class ErpProductStockManagerTest extends TestCase
{
    /** @var MessageBusInterface&MockObject */
    private $bus;

    /** @var ErpProductStockReadRepository&MockObject */
    private $repository;

    /** @var ParameterModel&MockObject */
    private $parameter_repo;

    /** @var ErpProductStockManager */
    private $manager;

    protected function setUp(): void
    {
        $this->bus = $this->createMock(MessageBusInterface::class);
        $this->repository = $this->createMock(ErpProductStockReadRepository::class);
        $this->parameter_repo = $this->createMock(ParameterModel::class);
        $this->manager = new ErpProductStockManager($this->bus, $this->repository, $this->parameter_repo);
        $this->manager->setLogger(new NullLogger());
    }

    public function test_fetch_and_publish_erp_product_stocks_by_id(): void
    {
        $product_id = 123;
        $erp_product_stock = new ErpProductStockMessage();
        $erp_product_stock->updated_at = '2024-03-20 10:00:00';
        $erp_product_stock->erp_product_id = $product_id;

        $this->repository
            ->expects($this->once())
            ->method('findBySelector')
            ->with($product_id)
            ->willReturn([$erp_product_stock]);

        $this->bus
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(Envelope::class));

        $this->parameter_repo->expects($this->never())->method('setParameter');

        $this->manager->fetchAndPublish($product_id);
    }

    public function test_fetch_and_publish_erp_product_stocks_by_date(): void
    {
        $date = new \DateTime('2024-03-20 10:00:00');
        $erp_product_stock = new ErpProductStockMessage();
        $erp_product_stock->updated_at = '2024-03-20 11:00:00';
        $erp_product_stock->erp_product_id = 123;

        $this->repository
            ->expects($this->once())
            ->method('findBySelector')
            ->with($date)
            ->willReturn([$erp_product_stock]);

        $this->bus
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(Envelope::class));

        $this->parameter_repo
            ->expects($this->once())
            ->method('setParameter')
            ->with(
                ErpProductStockManager::PARAMETER,
                $erp_product_stock->updated_at,
                'Last erp-product-stock update date sync'
            );

        $this->manager->fetchAndPublish($date);
    }

    public function test_no_parameter_update_when_no_product_stocks(): void
    {
        $date = new \DateTime('2024-03-20 10:00:00');

        $this->repository
            ->expects($this->once())
            ->method('findBySelector')
            ->with($date)
            ->willReturn([]);

        $this->bus->expects($this->never())->method('dispatch');
        $this->parameter_repo->expects($this->never())->method('setParameter');

        $this->manager->fetchAndPublish($date);
    }
}
