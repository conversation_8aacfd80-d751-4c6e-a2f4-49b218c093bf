<?php

namespace SonVideo\Erp\Tests\PHPUnit\Unit\Messaging\Manager;

use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Message\ErpProductMessage;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\NullLogger;
use SonVideo\Erp\Messaging\Manager\ErpProduct\ErpProductManager;
use SonVideo\Erp\Messaging\Repository\ErpProduct\ErpProductReadRepository;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

class ErpProductManagerTest extends TestCase
{
    /** @var MessageBusInterface&MockObject */
    private $bus;

    /** @var ErpProductReadRepository&MockObject */
    private $repository;

    /** @var ParameterModel&MockObject */
    private $parameter_repo;

    /** @var ErpProductManager */
    private $manager;

    protected function setUp(): void
    {
        $this->bus = $this->createMock(MessageBusInterface::class);
        $this->repository = $this->createMock(ErpProductReadRepository::class);
        $this->parameter_repo = $this->createMock(ParameterModel::class);
        $this->manager = new ErpProductManager($this->bus, $this->repository, $this->parameter_repo);
        $this->manager->setLogger(new NullLogger());
    }

    public function test_fetch_and_publish_erp_products_by_id(): void
    {
        $product_id = 123;
        $erp_product = new ErpProductMessage();
        $erp_product->updated_at = '2024-03-20 10:00:00';
        $erp_product->erp_product_id = $product_id;
        $erp_product->sku = 'TEST-SKU';
        $erp_product->name = 'Test Product';

        $this->repository
            ->expects($this->once())
            ->method('findBySelector')
            ->with($product_id)
            ->willReturn([$erp_product]);

        $this->bus
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(Envelope::class));

        $this->parameter_repo->expects($this->never())->method('setParameter');

        $this->manager->fetchAndPublish($product_id);
    }

    public function test_fetch_and_publish_erp_products_by_sku(): void
    {
        $sku = 'PROD-123';
        $erp_product = new ErpProductMessage();
        $erp_product->updated_at = '2024-03-20 10:00:00';

        $this->repository
            ->expects($this->once())
            ->method('findBySelector')
            ->with($sku)
            ->willReturn([$erp_product]);

        $this->bus
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(Envelope::class));

        $this->parameter_repo->expects($this->never())->method('setParameter');

        $this->manager->fetchAndPublish($sku);
    }

    public function test_fetch_and_publish_erp_products_by_date(): void
    {
        $date = new \DateTime('2024-03-20 10:00:00');
        $erp_product = new ErpProductMessage();
        $erp_product->updated_at = '2024-03-20 11:00:00';

        $this->repository
            ->expects($this->once())
            ->method('findBySelector')
            ->with($date)
            ->willReturn([$erp_product]);

        $this->bus
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(Envelope::class));

        $this->parameter_repo
            ->expects($this->once())
            ->method('setParameter')
            ->with(ErpProductManager::PARAMETER, $erp_product->updated_at, 'Last erp-product update date sync');

        $this->manager->fetchAndPublish($date);
    }

    public function test_no_parameter_update_when_no_products_are_found(): void
    {
        $date = new \DateTime('2024-03-20 10:00:00');

        $this->repository
            ->expects($this->once())
            ->method('findBySelector')
            ->with($date)
            ->willReturn([]);

        $this->bus->expects($this->never())->method('dispatch');

        $this->parameter_repo->expects($this->never())->method('setParameter');

        $this->manager->fetchAndPublish($date);
    }
}
