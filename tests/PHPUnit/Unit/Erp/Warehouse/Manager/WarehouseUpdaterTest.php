<?php

namespace PHPUnit\Unit\Erp\Warehouse\Manager;

use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Wms\Manager\WarehouseUpdater;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class WarehouseUpdaterTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'warehouses.sql']);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): WarehouseUpdater
    {
        return self::$container->get(WarehouseUpdater::class);
    }

    public function test_update_warehouse_comment(): void
    {
        $warehouse_id = 12;
        $new_comment = 'Nouveau commentaire pour test';

        $updater = $this->getTestedInstance();
        $updater->updateWarehouseComment($warehouse_id, $new_comment);

        $updated_comment = $this->fetchWarehouseComment($warehouse_id);

        $this->assertEquals($new_comment, $updated_comment);
    }

    public function test_update_warehouse_comment_with_invalid_warehouse_id(): void
    {
        $invalid_warehouseId = 9999;
        $comment = 'Commentaire pour entrepôt inexistant';

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage("Warehouse $invalid_warehouseId not found");

        $updater = $this->getTestedInstance();
        $updater->updateWarehouseComment($invalid_warehouseId, $comment);
    }

    /** @throws NotFoundException */
    private function fetchWarehouseComment(int $warehouse_id): string
    {
        $sql = <<<SQL
            SELECT commentaire
            FROM backOffice.BO_STK_depot
            WHERE id = :warehouse_id
        SQL;

        $result = static::$container->get(LegacyPdo::class)->fetchOne($sql, ['warehouse_id' => $warehouse_id]);

        if (false === $result) {
            throw new NotFoundException('Warehouse not found.');
        }

        return (string) $result['commentaire'];
    }
}
