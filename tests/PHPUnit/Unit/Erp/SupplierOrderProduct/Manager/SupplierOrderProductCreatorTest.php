<?php

namespace PHPUnit\Unit\Erp\SupplierOrderProduct\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\SupplierOrderProduct\Dto\SupplierOrderProductCreationContextDto;
use SonVideo\Erp\SupplierOrderProduct\Exception\SupplierOrderProductRequestPayloadException;
use SonVideo\Erp\SupplierOrderProduct\Manager\SupplierOrderProductCreator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class SupplierOrderProductCreatorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'supplier_order/post_supplier_order.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): SupplierOrderProductCreator
    {
        return self::$container->get(SupplierOrderProductCreator::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /**
     * Tests the create method.
     *
     * @throws ExceptionInterface
     * @throws SupplierOrderProductRequestPayloadException
     * @throws \Exception
     */
    public function test_create(): void
    {
        $supplier_order_product_dto = $this->getSerializer()->denormalize(
            ['product_id' => 81079, 'quantity' => 3, 'price' => 15],
            SupplierOrderProductCreationContextDto::class
        );
        $supplier_order_id = 3;

        // Add new product to a supplier order
        $supplier_order_product_id = $this->getTestedInstance()->create(
            $supplier_order_id,
            $supplier_order_product_dto
        );

        $supplier_order_product = $this->fetchSupplierOrderProduct($supplier_order_product_id);
        $this->assertEquals(81079, (int) $supplier_order_product['id_produit']);
        $this->assertEquals(3, (int) $supplier_order_product['quantite_commandee']);
        $this->assertEquals(15, (int) $supplier_order_product['prix_achat']);

        // Update product quantity and price on an existing supplier order
        $supplier_order_product_dto->quantity = 7;
        $supplier_order_product_dto->price = 17;
        $this->getTestedInstance()->create($supplier_order_id, $supplier_order_product_dto);

        $supplier_order_product = $this->fetchSupplierOrderProduct($supplier_order_product_id);
        $this->assertEquals(81079, (int) $supplier_order_product['id_produit']);
        $this->assertEquals(10, (int) $supplier_order_product['quantite_commandee']);
        $this->assertEquals(17, (int) $supplier_order_product['prix_achat']);

        // Quantity cannot be null
        $supplier_order_product_dto->quantity = 0;
        $this->expectException(SupplierOrderProductRequestPayloadException::class);
        $this->expectExceptionMessage('Invalid request parameters : quantity: This value should be positive.');
        $this->getTestedInstance()->create($supplier_order_id, $supplier_order_product_dto);
    }

    /**
     * Tests that price cannot be null.
     *
     * @throws ExceptionInterface
     * @throws SupplierOrderProductRequestPayloadException
     * @throws \Exception
     */
    public function test_price_cannot_be_null(): void
    {
        $supplier_order_product_dto = $this->getSerializer()->denormalize(
            ['product_id' => 81079, 'quantity' => 3, 'price' => -30],
            SupplierOrderProductCreationContextDto::class
        );
        $supplier_order_id = 3;

        $this->expectException(SupplierOrderProductRequestPayloadException::class);
        $this->expectExceptionMessage('Invalid request parameters : price: This value should be positive.');
        $this->getTestedInstance()->create($supplier_order_id, $supplier_order_product_dto);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches a supplier order product by ID. */
    protected function fetchSupplierOrderProduct(int $supplier_order_product_id): array
    {
        $sql = <<<SQL
        SELECT *
          FROM backOffice.produit_commande_fournisseur
        WHERE id = :supplier_order_product_id
        ;
        SQL;

        return $this->getPdo()->fetchOne($sql, [
            'supplier_order_product_id' => $supplier_order_product_id,
        ]);
    }
}
