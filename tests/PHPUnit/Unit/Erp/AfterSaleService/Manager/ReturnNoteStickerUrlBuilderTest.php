<?php

namespace PHPUnit\Unit\Erp\AfterSaleService\Manager;

use App\Exception\NotFoundException;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\AfterSaleService\Manager\ReturnNoteStickerUrlBuilder;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ReturnNoteStickerUrlBuilderTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'after_sale_service/product_return_notes.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ReturnNoteStickerUrlBuilder. */
    protected function getTestedInstance(): ReturnNoteStickerUrlBuilder
    {
        return self::$container->get(ReturnNoteStickerUrlBuilder::class);
    }

    /** Tests the getUrl method with various inputs. */
    public function test_get_url(): void
    {
        $return_note_sticker_url = $this->getTestedInstance();

        // Test generating URL with all parameters
        $url = $return_note_sticker_url->getUrl(83938);
        $this->assertIsString($url);
        $this->assertEquals(
            'https://retours.mondialrelay.com/d/FRSONVID/?SiteId=RetourErp&CustomReference=83938&Email=9j8hhz1b8b6bfss%40marketplace.amazon.fr&Adress1=Abegg&Adress2=Mark&Adress3=23+rue+richard+lenoir&PostCode=75011&City=Paris&WeightInGramm=8450.000',
            $url
        );

        // Test generating URL with some missing parameters
        $url = $return_note_sticker_url->getUrl(83944);
        $this->assertIsString($url);
        $this->assertEquals(
            'https://retours.mondialrelay.com/d/FRSONVID/?SiteId=RetourErp&CustomReference=83944&Email=toto%40oange.fr&Adress3=23+rue+richard+lenoir&City=Paris&WeightInGramm=2000.000',
            $url
        );

        // Test product return note not found
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Product return note not found with id "999"');
        $return_note_sticker_url->getUrl(999);
    }
}
