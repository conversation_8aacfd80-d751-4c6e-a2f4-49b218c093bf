<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Carrier\Manager\ParcelTracking;

use App\DataLoader\EntityDataLoader;
use App\Tests\Mock\Erp\Filesystem\MondialRelayS3Mock;
use League\Flysystem\MountManager;
use SonVideo\Erp\Carrier\Manager\ParcelTracking\MondialRelayParcelTrackingUpdater;
use SonVideo\Erp\Carrier\Mysql\Repository\ParcelTrackingReadRepository;
use SonVideo\Erp\Carrier\Mysql\Repository\ParcelTrackingWriteRepository;
use SonVideo\Erp\Filesystem\Manager\MondialRelayFtp;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class MondialRelayParcelTrackingUpdaterTest extends KernelTestCase
{
    protected ParcelTrackingReadRepository $parcel_tracking_read_repository_mock;

    protected ParcelTrackingWriteRepository $parcel_tracking_write_repository_mock;

    protected MondialRelayS3Mock $mondial_relay_s3_mock;

    protected MondialRelayFtp $mondial_relay_ftp_mock;

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests the import method. */
    public function test_import(): void
    {
        $updater = $this->getInstance();

        // Test: Load a tracking entity with the data extracted from the loaded content
        $tracking = $updater->extractTracking($this->getContent());

        $this->assertCount(2, $tracking);

        $this->assertEquals('1', $tracking[0]->toArray()['parcel_id']);
        $this->assertEquals('en cours', $tracking[0]->toArray()['status']);
        $this->assertEquals('2008-08-20 11:33:00', $tracking[0]->toArray()['status_date']);
        $this->assertEquals(
            'Prise en charge / réceptionné par le Point Relais ou transporteur',
            $tracking[0]->toArray()['description']
        );

        $this->assertEquals('2', $tracking[1]->toArray()['parcel_id']);
        $this->assertEquals('en cours', $tracking[1]->toArray()['status']);
        $this->assertEquals('2008-08-20 00:00:00', $tracking[1]->toArray()['status_date']);
        $this->assertEquals('Prise en charge par agence', $tracking[1]->toArray()['description']);
    }

    /** Creates a test instance of MondialRelayParcelTrackingUpdater. */
    protected function getInstance(): MondialRelayParcelTrackingUpdater
    {
        $this->parcel_tracking_read_repository_mock = $this->createMock(ParcelTrackingReadRepository::class);
        $this->parcel_tracking_read_repository_mock
            ->method('getColisIdFromColisNumber')
            ->willReturnCallback(function ($no_colis): string {
                if ('22216490' === $no_colis) {
                    return '2';
                }

                return '1';
            });

        $this->parcel_tracking_write_repository_mock = $this->createMock(ParcelTrackingWriteRepository::class);

        $this->mondial_relay_s3_mock = new MondialRelayS3Mock(
            self::$container->get('test.service_container')->get(MountManager::class)
        );

        $this->mondial_relay_ftp_mock = $this->createMock(MondialRelayFtp::class);

        $updater = new MondialRelayParcelTrackingUpdater(
            $this->parcel_tracking_read_repository_mock,
            $this->parcel_tracking_write_repository_mock,
            $this->mondial_relay_ftp_mock,
            $this->mondial_relay_s3_mock
        );
        $updater->setDataLoader(self::$container->get(EntityDataLoader::class));

        return $updater;
    }

    /** Gets the test content. */
    private function getContent(): string
    {
        return 'B0MR BDI06020000006320.08.200801.00
B20688DP006882BD2221701401D24R20.08.200820.08.20081133TRNREC00AL21913-0822500AL21913                                     R23566XXXXXX
B20648DP006483BD2221649000DLDR18.08.200820.08.20080000PECCFM00AL21421-0822100AL21421                                           XXXXXX
B20648DP006483BD2221649000DLCC18.08.200820.08.20080000PECCFM00AL21421-0822100AL21421                                           XXXXXX
B20648DP006483BD2221649000D24R18.08.200820.08.20080000DPCCFM00AL21421-0822100AL21421                                           XXXXXX';
    }
}
