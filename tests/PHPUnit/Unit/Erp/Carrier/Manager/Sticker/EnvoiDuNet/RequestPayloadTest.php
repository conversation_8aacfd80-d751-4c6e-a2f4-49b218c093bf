<?php

namespace PHPUnit\Unit\Erp\Carrier\Manager\Sticker\EnvoiDuNet;

use App\Contract\DataLoaderAwareTrait;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Carrier\Manager\Sticker\EnvoiDuNet\RequestPayload;
use SonVideo\Erp\Carrier\Mysql\Repository\StickerReadRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class RequestPayloadTest extends KernelTestCase
{
    use DataLoaderAwareTrait;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'wms/sticker/generate-and-print-edn.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests the request payload. */
    public function test_request_payload(): void
    {
        $shipmentEntity = $this->getMockedRepository()->load(4403190, null);
        $requestPayload = new RequestPayload($shipmentEntity);
        $payload = $requestPayload->make();

        // Test: Product has hscode and country_orig parameters
        $this->assertEquals(
            [
                'name' => 'Recepteur bluetooth Fiio ',
                'price' => 99.17,
                'qty' => 1,
                'weight' => '0.15',
                'hscode' => '85176200',
                'country_orig' => 'FR',
            ],
            $payload['packages'][0]['products'][0]
        );
    }

    /** Gets the mocked repository. */
    private function getMockedRepository(): StickerReadRepository
    {
        return self::$container->get(StickerReadRepository::class);
    }
}
