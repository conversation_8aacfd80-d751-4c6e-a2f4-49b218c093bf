<?php

namespace PHPUnit\Unit\Erp\Carrier\Manager\Sticker\Chronopost;

use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Carrier\Entity\ShipmentProperty\ParcelProductEntity;
use SonVideo\Erp\Carrier\Manager\Sticker\Chronopost\RequestPayload;
use SonVideo\Erp\Carrier\Mysql\Repository\StickerReadRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class RequestPayloadTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'wms/sticker/generate-and-print-chronopost.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests the request payload. */
    public function test_request_payload(): void
    {
        $currentDateTime = new \DateTime();
        $account = new \stdClass();
        $account->account_number = 123;
        $account->password = 'abc';
        $shipmentEntity = $this->getMockedRepository()->load(4403191, null);
        $shipmentEntity->information->prepared_at = 'last Monday';

        // Test: Set product description in content1 parameter
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals(
            [
                'headerValue' => [
                    'accountNumber' => 123,
                    'idEmit' => 'CHRFR',
                    'identWebPro' => '',
                    'subAccount' => '',
                ],
                'shipperValue' => [
                    'shipperAdress1' => '314 rue du prof Paul Milliez',
                    'shipperCity' => 'CHAMPIGNY SUR MARNE',
                    'shipperCivility' => 'M',
                    'shipperContactName' => 'SON-VIDEO DISTRIBUTION',
                    'shipperCountry' => 'FR',
                    'shipperCountryName' => 'FRANCE',
                    'shipperEmail' => '<EMAIL>',
                    'shipperName' => 'SON-VIDEO DISTRIBUTION',
                    'shipperPhone' => '*********',
                    'shipperPreAlert' => 0,
                    'shipperZipCode' => '94500',
                ],
                'customerValue' => [
                    'customerAdress1' => '314 rue du prof Paul Milliez',
                    'customerCity' => 'CHAMPIGNY SUR MARNE',
                    'customerCivility' => 'M',
                    'customerContactName' => 'SON-VIDEO DISTRIBUTION',
                    'customerCountry' => 'FR',
                    'customerCountryName' => 'FRANCE',
                    'customerEmail' => '<EMAIL>',
                    'customerName' => 'SON-VIDEO DISTRIBUTION',
                    'customerPhone' => '*********',
                    'customerPreAlert' => 0,
                    'customerZipCode' => '94500',
                    'printAsSender' => 'N',
                ],
                'recipientValue' => [
                    'recipientAdress1' => '38 rue de la ville en bois',
                    'recipientCity' => 'NANTES',
                    'recipientContactName' => 'Guy Liguili',
                    'recipientCountry' => 'FR',
                    'recipientCountryName' => 'FRANCE',
                    'recipientEmail' => '<EMAIL>',
                    'recipientMobilePhone' => '0655092043',
                    'recipientName' => 'PHC Holding',
                    'recipientPhone' => '0155092043',
                    'recipientPreAlert' => 0,
                    'recipientZipCode' => '44100',
                    'recipientName2' => '',
                ],
                'refValue' => [
                    'shipperRef' => '4403191',
                    'customerSkybillNumber' => '4403191',
                    'recipientRef' => '',
                    'idRelais' => '',
                ],
                'skybillValue' => [
                    'evtCode' => 'DC',
                    'productCode' => '01',
                    'shipDate' => $currentDateTime->format('Y-m-d'),
                    'shipHour' => $currentDateTime->format('H'),
                    'weight' => 0.46,
                    'weightUnit' => 'KGM',
                    'content1' => 'FIIO BTR5 NOIR',
                    'service' => '0',
                    'objectType' => 'MAR',
                    'as' => '',
                ],
                'skybillParamsValue' => [
                    'duplicata' => 'N',
                    'mode' => 'THE',
                    'withReservation' => 0,
                ],
                'password' => 'abc',
                'modeRetour' => 2,
                'numberOfParcel' => 1,
                'version' => '2',
                'multiParcel' => 'N',
            ],
            $payload
        );

        // Test: Content1 has product quantity if quantity > 1
        $shipmentEntity->parcels[0]->products[0]->quantity = 3;
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('3 FIIO BTR5 NOIR', $payload['skybillValue']['content1']);
        $this->assertLessThan(46, strlen($payload['skybillValue']['content1']));

        // Test: Content1 is the concatenation of all products description and accents are removed
        $product = new ParcelProductEntity();
        $product->short_description = 'RéçÉpteür blûètöôth Fiio BTR5 Noir';
        $shipmentEntity->parcels[0]->products[] = $product;
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('3 FIIO BTR5 NOIR,RECEPTEUR BLUETOOTH FIIO BTR', $payload['skybillValue']['content1']);
        $this->assertLessThan(46, strlen($payload['skybillValue']['content1']));

        // Test: Content1 is limited to 45 char
        $shipmentEntity->parcels[0]->products[0]->quantity = 1;
        $shipmentEntity->parcels[0]->products[0]->short_description =
            'This is a 71 characters sentence and it should be truncated to only 45.';
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('THIS IS A 71 CHARACTERS SENTENCE AND IT SHOUL', $payload['skybillValue']['content1']);
        $this->assertLessThan(46, strlen($payload['skybillValue']['content1']));

        // Test: Content1 is empty when description is only spaces
        $shipmentEntity = $this->getMockedRepository()->load(4403191, null);
        $shipmentEntity->information->prepared_at = 'last Monday';
        $shipmentEntity->parcels[0]->products[0]->short_description = '                  ';
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('RECEPTEUR BLUETOOTH FIIO', $payload['skybillValue']['content1']);

        // Test: Content1 use description if description_short is empty
        $shipmentEntity = $this->getMockedRepository()->load(4403191, null);
        $shipmentEntity->information->prepared_at = 'last Monday';
        $shipmentEntity->parcels[0]->products[0]->short_description = '                  ';
        $shipmentEntity->parcels[0]->products[0]->description = 'Un super produit';
        $shipmentEntity->parcels[0]->products[0]->quantity = 5;
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('5 UN SUPER PRODUIT', $payload['skybillValue']['content1']);

        // Test: Content1 is empty when description_short and description are empty even if quantity is > 1
        $shipmentEntity = $this->getMockedRepository()->load(4403191, null);
        $shipmentEntity->information->prepared_at = 'last Monday';
        $shipmentEntity->parcels[0]->products[0]->short_description = '                  ';
        $shipmentEntity->parcels[0]->products[0]->description = '                  ';
        $shipmentEntity->parcels[0]->products[0]->quantity = 5;
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('', $payload['skybillValue']['content1']);

        // Test: Check we respect all rules above
        $shipmentEntity = $this->getMockedRepository()->load(4403191, null);
        $shipmentEntity->information->prepared_at = 'last Monday';
        $product = new ParcelProductEntity();
        $product->short_description = 'Produit super bien';
        $product->quantity = 1;
        $shipmentEntity->parcels[0]->products[0] = $product;
        $product = new ParcelProductEntity();
        $product->short_description = '                        ';
        $product->quantity = 5;
        $shipmentEntity->parcels[0]->products[1] = $product;
        $product = new ParcelProductEntity();
        $product->description = 'produit cool';
        $product->quantity = 2;
        $shipmentEntity->parcels[0]->products[2] = $product;
        $product = new ParcelProductEntity();
        $product->short_description = 'Autre bon produit';
        $product->quantity = 3;
        $shipmentEntity->parcels[0]->products[3] = $product;
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('PRODUIT SUPER BIEN,2 PRODUIT COOL,3 AUTRE BON', $payload['skybillValue']['content1']);
        $this->assertLessThan(46, strlen($payload['skybillValue']['content1']));

        // Test: chrono express
        $shipmentEntity->information->shipment_method_code = RequestPayload::CHRONO_EXPRESS;
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('0', $payload['skybillValue']['service']);

        // Test: chrono 13 monday
        $shipmentEntity->information->shipment_method_code = RequestPayload::CHRONO_13H;
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('0', $payload['skybillValue']['service']);

        // Test: chrono express friday
        $shipmentEntity->information->prepared_at = 'last friday';
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('6', $payload['skybillValue']['service']);

        // Test: phone is empty
        $shipmentEntity = $this->getMockedRepository()->load(4403191, null);
        $shipmentEntity->recipient->phone = null;
        $shipmentEntity->recipient->mobile = '';
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertArrayNotHasKey('recipientMobilePhone', $payload['recipientValue']);
        $this->assertEquals('**********', $payload['recipientValue']['recipientPhone']);
    }

    /** Tests the request payload for Chrono Classic. */
    public function test_request_payload_chrono_classic(): void
    {
        $currentDateTime = new \DateTime();
        $account = new \stdClass();
        $account->account_number = 123;
        $account->password = 'abc';
        $shipmentEntity = $this->getMockedRepository()->load(5000000, null);

        // Test: b2c and weight < 3 kg
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals(
            [
                'headerValue' => [
                    'accountNumber' => 123,
                    'idEmit' => 'CHRFR',
                    'identWebPro' => '',
                    'subAccount' => '',
                ],
                'shipperValue' => [
                    'shipperAdress1' => '314 rue du prof Paul Milliez',
                    'shipperCity' => 'CHAMPIGNY SUR MARNE',
                    'shipperCivility' => 'M',
                    'shipperContactName' => 'SON-VIDEO DISTRIBUTION',
                    'shipperCountry' => 'FR',
                    'shipperCountryName' => 'FRANCE',
                    'shipperEmail' => '<EMAIL>',
                    'shipperName' => 'SON-VIDEO DISTRIBUTION',
                    'shipperPhone' => '*********',
                    'shipperPreAlert' => 0,
                    'shipperZipCode' => '94500',
                ],
                'customerValue' => [
                    'customerAdress1' => '314 rue du prof Paul Milliez',
                    'customerCity' => 'CHAMPIGNY SUR MARNE',
                    'customerCivility' => 'M',
                    'customerContactName' => 'SON-VIDEO DISTRIBUTION',
                    'customerCountry' => 'FR',
                    'customerCountryName' => 'FRANCE',
                    'customerEmail' => '<EMAIL>',
                    'customerName' => 'SON-VIDEO DISTRIBUTION',
                    'customerPhone' => '*********',
                    'customerPreAlert' => 0,
                    'customerZipCode' => '94500',
                    'printAsSender' => 'N',
                ],
                'recipientValue' => [
                    'recipientAdress1' => '38 rue de la ville en bois',
                    'recipientCity' => 'NANTES',
                    'recipientContactName' => 'Guy Liguili',
                    'recipientCountry' => 'FR',
                    'recipientCountryName' => 'FRANCE',
                    'recipientEmail' => '<EMAIL>',
                    'recipientMobilePhone' => '0655092043',
                    'recipientName' => 'Guy Liguili',
                    'recipientPhone' => '0155092043',
                    'recipientPreAlert' => 0,
                    'recipientZipCode' => '44100',
                    'recipientName2' => '',
                ],
                'refValue' => [
                    'shipperRef' => '5000000',
                    'customerSkybillNumber' => '5000000',
                    'recipientRef' => '',
                    'idRelais' => '',
                ],
                'skybillValue' => [
                    'evtCode' => 'DC',
                    'productCode' => '44',
                    'shipDate' => $currentDateTime->format('Y-m-d'),
                    'shipHour' => $currentDateTime->format('H'),
                    'weight' => 0.46,
                    'weightUnit' => 'KGM',
                    'content1' => 'FIIO BTR5 NOIR',
                    'service' => '328',
                    'objectType' => 'MAR',
                    'as' => '',
                ],
                'skybillParamsValue' => [
                    'duplicata' => 'N',
                    'mode' => 'THE',
                    'withReservation' => 0,
                ],
                'password' => 'abc',
                'modeRetour' => 2,
                'numberOfParcel' => 1,
                'version' => '2',
                'multiParcel' => 'N',
            ],
            $payload
        );

        // Test: b2b and weight < 3 kg
        $shipmentEntity->recipient->company_name = 'LOLO compagny';
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('136', $payload['skybillValue']['service']);
        $this->assertLessThan(46, strlen($payload['skybillValue']['service']));

        // Test: b2b and weight > 3 kg
        $shipmentEntity->parcels[0]->weight = 30;
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('101', $payload['skybillValue']['service']);
        $this->assertLessThan(46, strlen($payload['skybillValue']['service']));

        // Test: b2c and weight > 3 kg
        $shipmentEntity->recipient->company_name = null;
        $requestPayload = new RequestPayload($shipmentEntity, $account);
        $payload = $requestPayload->make();

        $this->assertEquals('327', $payload['skybillValue']['service']);
        $this->assertLessThan(46, strlen($payload['skybillValue']['service']));
    }

    /** Gets the mocked repository. */
    private function getMockedRepository(): StickerReadRepository
    {
        return self::$container->get(StickerReadRepository::class);
    }
}
