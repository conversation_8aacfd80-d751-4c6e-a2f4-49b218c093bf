<?php

namespace PHPUnit\Unit\Erp\SupplierOrder\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\SupplierOrder\Manager\SupplierOrderCreator;
use SonVideo\Erp\SupplierOrderProduct\Exception\SupplierOrderProductRequestPayloadException;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class SupplierOrderCreatorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'supplier_order/post_supplier_order.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): SupplierOrderCreator
    {
        return self::$container->get(SupplierOrderCreator::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /**
     * Tests the create method.
     *
     * @throws ExceptionInterface
     * @throws SupplierOrderProductRequestPayloadException
     * @throws \Exception
     */
    public function test_create(): void
    {
        // Create new supplier order
        $supplier_order_id = $this->getTestedInstance()->create(162);
        $supplier_order = $this->fetchSupplierOrder($supplier_order_id);

        $this->assertEquals(162, (int) $supplier_order['id_fournisseur']);
        $this->assertEquals(21, (int) $supplier_order['id_depot']);
        $this->assertEquals('en preparation', $supplier_order['status']);

        // Use already created supplier order
        $new_supplier_order_id = $this->getTestedInstance()->create(162);
        $this->assertEquals($supplier_order_id, $new_supplier_order_id);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches a supplier order by ID. */
    protected function fetchSupplierOrder(int $supplier_order_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.commande_fournisseur
        WHERE id_commande_fournisseur = :supplier_order_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['supplier_order_id' => $supplier_order_id]);
    }
}
