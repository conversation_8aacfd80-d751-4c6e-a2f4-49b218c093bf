<?php

namespace PHPUnit\Unit\Erp\SupplierContract\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\NotFoundException;
use App\Sql\Helper\Pager;
use App\Sql\LegacyPdo;
use App\Sql\Query\QueryBuilder;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Supplier\Manager\SupplierManager;
use SonVideo\Erp\SupplierContract\Dto\CreationContext\SupplierContractCreationContextDto;
use SonVideo\Erp\SupplierContract\Dto\UpdateContext\SupplierContractUpdateContext;
use SonVideo\Erp\SupplierContract\Manager\SupplierContractManager;
use SonVideo\Erp\SupplierContract\Mysql\Repository\SupplierContractRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SupplierContractManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'supplier_contract/supplier_contracts.sql']);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    private function getTestedInstance(
        SupplierContractRepository $supplier_contract_repository
    ): SupplierContractManager {
        return new SupplierContractManager(
            self::$container->get(QueryBuilder::class),
            $supplier_contract_repository,
            self::$container->get(ValidatorInterface::class),
            self::$container->get(SupplierManager::class),
            self::$container->get(LegacyPdo::class)
        );
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Gets the default data for testing. */
    private function getDefaultData(array $overrides = []): array
    {
        $default_data = [
            'supplier_id' => 400,
            'brand_id' => null,
            'supplier_contract_id' => null,
            'payment' => [
                'supplier_id' => 400,
                'supplier_payment_id' => 2,
                'discount_rate' => 0.0,
                'franco' => null,
                'delivery_cost' => '0',
                'discount_payment_deadline' => null,
                'payment_deadline_id' => 2,
                'comment' => '',
            ],
            'year' => 2025,
            'discount_description' => null,
            'pam' => [
                'website_promotion' => 2,
                'newsletters' => 2,
                'blog' => 1,
                'stores_presence' => 1,
                'training' => 1,
                'sales_inventory_reporting' => 1,
                'comment' => '',
            ],
            'rfa' => [
                'landing' => [
                    [
                        'landing' => 1,
                        'starting_value' => 0,
                        'end_value' => 100000,
                        'rate_value' => 1,
                    ],
                    [
                        'landing' => 2,
                        'starting_value' => 100001,
                        'end_value' => 200000,
                        'rate_value' => 2,
                    ],
                    [
                        'landing' => 3,
                        'starting_value' => 200001,
                        'end_value' => 300000,
                        'rate_value' => 3,
                    ],
                    [
                        'landing' => 4,
                        'starting_value' => 300001,
                        'end_value' => 400000,
                        'rate_value' => 4,
                    ],
                    [
                        'landing' => 5,
                        'starting_value' => 400001,
                        'end_value' => 500000,
                        'rate_value' => 5,
                    ],
                ],
                'comment' => '',
            ],
            'additional_rewards' => [],
            'unconditional_discount' => 0.0,
        ];

        return array_replace_recursive($default_data, $overrides);
    }

    /**
     * Tests the create method.
     *
     * @throws ExceptionInterface
     */
    public function test_create(): void
    {
        $data = $this->getDefaultData([
            'supplier_id' => 400,
            'payment' => ['supplier_id' => 400],
        ]);

        $dto = $this->getSerializer()->denormalize($data, SupplierContractCreationContextDto::class);

        $supplier_contract_repository = $this->createMock(SupplierContractRepository::class);
        $supplier_contract_repository->method('findAllPaginated')->willReturn((new Pager())->setResults([]));

        $supplier_contract_repository
            ->expects($this->once())
            ->method('create')
            ->with($dto)
            ->willReturn(123);

        $supplier_contract_manager = $this->getTestedInstance($supplier_contract_repository)->create($dto);
        $this->assertIsInt($supplier_contract_manager);
    }

    /**
     * Tests the create method with an invalid supplier ID.
     *
     * @throws ExceptionInterface
     */
    public function test_create_with_invalid_supplier_id(): void
    {
        $data = $this->getDefaultData([
            'supplier_id' => 4000,
            'payment' => ['supplier_id' => 4000],
        ]);

        $dto = $this->getSerializer()->denormalize($data, SupplierContractCreationContextDto::class);

        $supplier_contract_repository = $this->createMock(SupplierContractRepository::class);
        $supplier_contract_repository->method('findAllPaginated')->willReturn((new Pager())->setResults([]));

        $supplier_contract_repository->expects($this->never())->method('create');

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Supplier does not exist with id "4000".');

        $this->getTestedInstance($supplier_contract_repository)->create($dto);
    }

    /**
     * Tests the create method with a supplier contract that already exists.
     *
     * @throws ExceptionInterface
     */
    public function test_create_a_supplier_contract_that_already_exists(): void
    {
        $data = $this->getDefaultData([
            'supplier_id' => 252,
            'brand_id' => null,
            'year' => 2025,
        ]);

        $dto = $this->getSerializer()->denormalize($data, SupplierContractCreationContextDto::class);

        $supplier_contract_repository = $this->createMock(SupplierContractRepository::class);
        $supplier_contract_repository->method('findAllPaginated')->willReturn(
            (new Pager())->setResults([
                [
                    'supplier_contract_id' => 2,
                    'supplier_id' => 252,
                    'brand_id' => null,
                    'year' => 2025,
                ],
            ])
        );

        $supplier_contract_repository->expects($this->never())->method('create');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage(
            'There is already a supplier contract with the parameters: supplier_id=252, brand_id=NULL, year=2025.'
        );

        $this->getTestedInstance($supplier_contract_repository)->create($dto);
    }

    /**
     * Tests the update method.
     *
     * @throws ExceptionInterface
     */
    public function test_update(): void
    {
        $data = $this->getDefaultData([
            'supplier_id' => 252,
            'supplier_contract_id' => 2,
            'payment' => ['supplier_id' => 252],
            'discount_description' => 'test',
        ]);

        $dto = $this->getSerializer()->denormalize($data, SupplierContractUpdateContext::class);

        $supplier_contract_repository = $this->createMock(SupplierContractRepository::class);
        $supplier_contract_repository->method('findAllPaginated')->willReturn(
            (new Pager())->setResults([
                [
                    'supplier_contract_id' => 2,
                ],
            ])
        );

        $supplier_contract_repository
            ->expects($this->once())
            ->method('update')
            ->with($dto)
            ->willReturn(1);

        $this->getTestedInstance($supplier_contract_repository)->update($dto);
    }

    /**
     * Tests the update method with an invalid supplier contract ID.
     *
     * @throws ExceptionInterface
     */
    public function test_update_with_invalid_supplier_contract_id(): void
    {
        $data = $this->getDefaultData([
            'supplier_id' => 252,
            'supplier_contract_id' => 22,
            'payment' => ['supplier_id' => 252],
            'discount_description' => 'test',
        ]);

        $dto = $this->getSerializer()->denormalize($data, SupplierContractUpdateContext::class);

        $supplier_contract_repository = $this->createMock(SupplierContractRepository::class);
        $supplier_contract_repository->method('findAllPaginated')->willReturn((new Pager())->setResults([]));

        $supplier_contract_repository->expects($this->never())->method('update');

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Supplier contract does not exist with id "22".');

        $this->getTestedInstance($supplier_contract_repository)->update($dto);
    }
}
