<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrderPayment\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use Doctrine\DBAL\Exception;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\AntiFraud\Manager\AntiFraudModule;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderRepository;
use SonVideo\Erp\CustomerOrderPayment\Manager\AutomaticPaymentsAcceptance;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Entity\CustomerOrderPaymentNotAcceptedEntity;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentReadRepository;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentWriteRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class AutomaticPaymentsAcceptanceTest extends KernelTestCase
{
    private CustomerOrderPaymentReadRepository $customer_order_payment_read_repository;
    private CustomerOrderPaymentWriteRepository $customer_order_payment_write_repository;
    private CustomerOrderRepository $customer_order_repository;
    private AntiFraudModule $anti_fraud_module;
    private LegacyPdo $legacy_pdo;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        self::bootKernel();

        // Create mock objects
        $this->customer_order_payment_read_repository = $this->createMock(CustomerOrderPaymentReadRepository::class);
        $this->customer_order_payment_write_repository = $this->createMock(CustomerOrderPaymentWriteRepository::class);
        $this->customer_order_repository = $this->createMock(CustomerOrderRepository::class);
        $this->anti_fraud_module = $this->createMock(AntiFraudModule::class);
        $this->legacy_pdo = $this->createMock(LegacyPdo::class);
        $this->logger = $this->createMock(LoggerInterface::class);
    }

    /** Tests that no payments are accepted when none are available. */
    public function test_it_should_not_accept_any_payments(): void
    {
        // Configure mocks
        $this->customer_order_payment_read_repository
            ->expects($this->once())
            ->method('fetchPaymentsNotAccepted')
            ->willReturn([]);
        $this->customer_order_payment_read_repository->expects($this->never())->method('getMaxUniqueId');

        $this->customer_order_payment_write_repository->expects($this->never())->method('accept');

        $this->customer_order_repository->expects($this->never())->method('updateComputedStatusAndPaymentCount');

        $this->anti_fraud_module
            ->expects($this->once())
            ->method('check')
            ->willReturn([]);

        $this->legacy_pdo->expects($this->never())->method('beginTransaction');
        $this->legacy_pdo->expects($this->never())->method('commit');
        $this->legacy_pdo->expects($this->never())->method('rollBack');

        // Set up the logger which will let us know what's happening inside the class
        $this->logger
            ->expects($this->once())
            ->method('info')
            ->with('0 payment(s) passed all the rules and can be accepted automatically');

        // Instantiate class to test
        $tested_instance = new AutomaticPaymentsAcceptance(
            $this->customer_order_payment_read_repository,
            $this->customer_order_payment_write_repository,
            $this->customer_order_repository,
            $this->anti_fraud_module,
            $this->legacy_pdo
        );

        $tested_instance->setLogger($this->logger);

        // Run the test
        $tested_instance->run();
        $this->assertTrue(true); // If we get here without exceptions, the test passes
    }

    /** Tests that payments are accepted successfully. */
    public function test_it_should_accept_some_payments_successfully(): void
    {
        $data = [
            [
                'customer_order_payment_id' => 1,
                'created_proof' => 'ok',
                'payment_id' => 1,
                'operation_id' => '1',
                'customer_order_id' => 1,
                'customer_blacklist' => false,
                'customer_order_origin' => '',
                'billing_address_country_id' => 1,
                'shipping_address_country_id' => 1,
            ],
            [
                'customer_order_payment_id' => 1,
                'created_proof' => 'ok',
                'payment_id' => 1,
                'operation_id' => '1',
                'customer_order_id' => 1,
                'customer_blacklist' => false,
                'customer_order_origin' => '',
                'billing_address_country_id' => 1,
                'shipping_address_country_id' => 1,
            ],
        ];

        $serializer = self::$container->get(SerializerInterface::class);
        $payments = $serializer->denormalize($data, CustomerOrderPaymentNotAcceptedEntity::class . '[]');

        // Configure mocks
        $this->customer_order_payment_read_repository
            ->expects($this->once())
            ->method('fetchPaymentsNotAccepted')
            ->willReturn($payments);

        $this->customer_order_payment_read_repository
            ->expects($this->exactly(2))
            ->method('getMaxUniqueId')
            ->willReturn(2);

        $this->customer_order_payment_write_repository->expects($this->exactly(2))->method('accept');

        $this->customer_order_repository->expects($this->exactly(2))->method('updateComputedStatusAndPaymentCount');

        $this->anti_fraud_module
            ->expects($this->once())
            ->method('check')
            ->willReturn($payments);

        $this->legacy_pdo->expects($this->exactly(2))->method('beginTransaction');
        $this->legacy_pdo->expects($this->exactly(2))->method('commit');
        $this->legacy_pdo->expects($this->never())->method('rollBack');

        // Set up the logger which will let us know what's happening inside the class
        $this->logger
            ->expects($this->once())
            ->method('info')
            ->with('2 payment(s) passed all the rules and can be accepted automatically');
        $this->logger->expects($this->exactly(2))->method('notice');

        // Instantiate class to test
        $tested_instance = new AutomaticPaymentsAcceptance(
            $this->customer_order_payment_read_repository,
            $this->customer_order_payment_write_repository,
            $this->customer_order_repository,
            $this->anti_fraud_module,
            $this->legacy_pdo
        );

        $tested_instance->setLogger($this->logger);

        // Run the test
        $tested_instance->run();
        $this->assertTrue(true); // If we get here without exceptions, the test passes
    }

    /** Tests that the system retries on SQL errors. */
    public function test_it_should_retry_on_sql_error(): void
    {
        $data = [
            [
                'customer_order_payment_id' => 1,
                'created_proof' => 'ok',
                'payment_id' => 1,
                'operation_id' => '1',
                'customer_order_id' => 1,
                'customer_blacklist' => false,
                'customer_order_origin' => '',
                'billing_address_country_id' => 1,
                'shipping_address_country_id' => 1,
            ],
        ];

        $serializer = self::$container->get(SerializerInterface::class);
        $payments = $serializer->denormalize($data, CustomerOrderPaymentNotAcceptedEntity::class . '[]');

        // Configure mocks
        $this->customer_order_payment_read_repository
            ->expects($this->once())
            ->method('fetchPaymentsNotAccepted')
            ->willReturn($payments);

        $this->customer_order_payment_read_repository
            ->expects($this->exactly(3))
            ->method('getMaxUniqueId')
            ->willReturn(2);

        $this->customer_order_payment_write_repository->expects($this->exactly(3))->method('accept');

        $this->customer_order_repository
            ->expects($this->exactly(3))
            ->method('updateComputedStatusAndPaymentCount')
            ->willThrowException(new Exception("I'm not feeling it"));

        $this->anti_fraud_module
            ->expects($this->once())
            ->method('check')
            ->willReturn($payments);

        $this->legacy_pdo->expects($this->exactly(3))->method('beginTransaction');
        $this->legacy_pdo->expects($this->never())->method('commit');
        $this->legacy_pdo->expects($this->exactly(3))->method('rollBack');

        // Set up the logger which will let us know what's happening inside the class
        $this->logger
            ->expects($this->once())
            ->method('info')
            ->with('1 payment(s) passed all the rules and can be accepted automatically');
        $this->logger->expects($this->exactly(3))->method('warning');

        // Instantiate class to test
        $tested_instance = new AutomaticPaymentsAcceptance(
            $this->customer_order_payment_read_repository,
            $this->customer_order_payment_write_repository,
            $this->customer_order_repository,
            $this->anti_fraud_module,
            $this->legacy_pdo
        );

        $tested_instance->setLogger($this->logger);

        // Run the test and expect an exception
        $this->expectException(Exception::class);
        $tested_instance->run();
    }
}
