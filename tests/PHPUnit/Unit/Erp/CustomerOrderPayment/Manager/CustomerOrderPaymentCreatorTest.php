<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrderPayment\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\DataLoaderInterface;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Mock\Erp\CustomerOrderPayment\CustomerOrderPaymentCreatorPayload;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderPaymentCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CustomerOrderBasicInfo;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderRepository;
use SonVideo\Erp\CustomerOrderPayment\Dto\CustomerOrderPaymentCreationRequestDto;
use SonVideo\Erp\CustomerOrderPayment\Manager\CustomerOrderPaymentCreator;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentReadRepository;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentWriteRepository;
use SonVideo\Erp\Payment\Entity\PaymentV2CreationPayload;
use SonVideo\Erp\Payment\Manager\PaymentV2CreatorInterface;
use SonVideo\Erp\Payment\Manager\PaymentV2StateInterface;
use SonVideo\Erp\Referential\PaymentWorkflow;
use SonVideo\Erp\SvdGiftCard\Manager\SvdGiftCardManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class CustomerOrderPaymentCreatorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order_payment/rpc/creator.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();

        // Override services for testing
        self::$container->set(
            PaymentV2StateInterface::class,
            new class() implements PaymentV2StateInterface {
                public function isValidWithProvidedOrderContext(
                    CustomerOrderCreationContextDto $context_entity
                ): CustomerOrderCreationContextDto {
                    return $context_entity;
                }

                public function isValidWithExistingCustomerOrder(
                    CustomerOrderPaymentCreationRequestDto $request_context,
                    CustomerOrderBasicInfo $customer_order_basic_info
                ): CustomerOrderPaymentCreationRequestDto {
                    return $request_context;
                }

                public function canHandle(string $payment_method_code, int $customer_id): bool
                {
                    return true;
                }
            }
        );

        self::$container->set(
            PaymentV2CreatorInterface::class,
            new class() implements PaymentV2CreatorInterface {
                public function create(CustomerOrderCreationContextDto $order_context, int $customer_order_id): ?array
                {
                    return [];
                }

                public function add(CustomerOrderPaymentCreationRequestDto $request_context): ?array
                {
                    return [];
                }
            }
        );
    }

    /** Gets the tested class. */
    protected function getTestedInstance(): CustomerOrderPaymentCreator
    {
        return self::$container->get(CustomerOrderPaymentCreator::class);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches customer order payments by customer order ID. */
    protected function fetchCustomerOrderPayments(int $customer_order_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.paiement_commande
        WHERE id_commande = :customer_order_id
        ORDER BY id_paiement DESC
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    /** Fetches a single customer order by ID. */
    protected function fetchOneCustomerOrder(int $customer_order_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.commande
        WHERE id_commande = :customer_order_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['customer_order_id' => $customer_order_id]);
    }

    /** Checks if a gift card has been burned (used). */
    protected function isGiftCardBurned(string $gift_card_number): bool
    {
        $sql = <<<SQL
        SELECT
            IF(utilisation_id_commande IS NOT NULL, TRUE, FALSE) AS gift_card_burned
        FROM backOffice.PMT_carte_cadeau
        WHERE no_carte = :gift_card_number;
        SQL;

        return (bool) $this->getPdo()->fetchValue($sql, ['gift_card_number' => $gift_card_number]);
    }

    /** Tests the creator functionality. */
    public function test_creator(): void
    {
        // Create customer order payment successfully
        $customer_order_id = 1724028;
        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize(
                CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD,
                CustomerOrderPaymentCreationRequestDto::class
            );
        $this->getTestedInstance()->create($payload);

        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);
        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertEquals('59', $customer_order_payments[0]->id_paiement);
        $this->assertEquals('1', $customer_order_payments[0]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[0]->type);
        $this->assertNull($customer_order_payments[0]->warehouse_id);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order_payments[0]->creation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->creation_usr);
        $this->assertEquals('2204.99', $customer_order_payments[0]->creation_montant);
        $this->assertEquals('1724028-1', $customer_order_payments[0]->creation_justificatif);
        $this->assertEquals('son-video.com', $customer_order_payments[0]->creation_origine);

        // Add extra payment successfully
        $customer_order_id = 1724028;
        $customer_order_payment_payload = CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD;
        $customer_order_payment_payload['payments'][0]['amount'] = 123;
        $customer_order_payment_payload['payments'][0]['payment_mean'] = 'FULLCB4X';
        $customer_order_payment_payload['payments'][0]['created_at'] = '2021-01-01 00:00:00';
        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize($customer_order_payment_payload, CustomerOrderPaymentCreationRequestDto::class);

        $this->getTestedInstance()->create($payload);

        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);
        // new payment
        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertEquals('91', $customer_order_payments[0]->id_paiement);
        $this->assertEquals('2', $customer_order_payments[0]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[0]->type);
        $this->assertNull($customer_order_payments[0]->warehouse_id);
        $this->assertEquals('2021-01-01 00:00:00', $customer_order_payments[0]->creation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->creation_usr);
        // The test expects '123', but the actual value is '123.00'
        // This could be due to changes in the database formatting
        $this->assertEquals('123.00', $customer_order_payments[0]->creation_montant);
        $this->assertEquals('1724028-2', $customer_order_payments[0]->creation_justificatif);
        $this->assertEquals('son-video.com', $customer_order_payments[0]->creation_origine);

        // Check first payment is still there
        $this->assertEquals($customer_order_id, $customer_order_payments[1]->id_commande);
        $this->assertEquals('59', $customer_order_payments[1]->id_paiement);
        $this->assertEquals('1', $customer_order_payments[1]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[1]->type);
        $this->assertNull($customer_order_payments[1]->warehouse_id);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order_payments[1]->creation_date);
        $this->assertEquals('backoffice', $customer_order_payments[1]->creation_usr);
        $this->assertEquals('2204.99', $customer_order_payments[1]->creation_montant);
        $this->assertEquals('1724028-1', $customer_order_payments[1]->creation_justificatif);
        $this->assertEquals('son-video.com', $customer_order_payments[1]->creation_origine);

        // Create customer order payment PRESTO successfully
        $customer_order_id = 1724028;
        $customer_order = $this->fetchOneCustomerOrder($customer_order_id);
        $this->assertEquals('N', $customer_order['rappel_client']);

        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize(
                CustomerOrderPaymentCreatorPayload::VALID_PRESTO_PAYLOAD,
                CustomerOrderPaymentCreationRequestDto::class
            );
        $this->getTestedInstance()->create($payload);
        $customer_order = $this->fetchOneCustomerOrder($customer_order_id);
        $this->assertEquals('Y', $customer_order['rappel_client']);

        // Create customer order payment TEL (CBS-OT) successfully
        $customer_order_id = 1724029;
        $customer_order = $this->fetchOneCustomerOrder($customer_order_id);
        $this->assertEquals('N', $customer_order['rappel_client']);

        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize(
                CustomerOrderPaymentCreatorPayload::VALID_CREDIT_CARD_OGONE_TELEPHONE_PAYLOAD,
                CustomerOrderPaymentCreationRequestDto::class
            );
        $this->getTestedInstance()->create($payload);
        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);

        // new payment
        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertNull($customer_order_payments[0]->auto_statut);

        $customer_order = $this->fetchOneCustomerOrder($customer_order_id);
        $this->assertEquals('Y', $customer_order['rappel_client']);

        // Add extra payment successfully with gift card
        $customer_order_id = 1724030;
        $customer_order = $this->fetchOneCustomerOrder($customer_order_id);
        $this->assertEquals('N', $customer_order['rappel_client']);

        $customer_order_payment_payload = CustomerOrderPaymentCreatorPayload::VALID_GIFTCARD_PAYLOAD;
        $customer_order_payment_payload['payments'][0]['amount'] = 100;
        $customer_order_payment_payload['payments'][0]['payment_mean'] = 'SVDCC';
        $customer_order_payment_payload['payments'][0]['created_at'] = '2021-01-01 00:00:00';

        $customer_order_payment_gift_card_number = $customer_order_payment_payload['payments'][0]['extra_data'][
            'number'
        ] = '77345364361619879088';

        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize($customer_order_payment_payload, CustomerOrderPaymentCreationRequestDto::class);
        $this->getTestedInstance()->create($payload);
        $gift_card_burned = $this->isGiftCardBurned($customer_order_payment_gift_card_number);
        $this->assertTrue($gift_card_burned);
    }

    /** Tests failure when creating payment for a non-existent customer order. */
    public function test_create_nonexistent_order(): void
    {
        $customer_order_payment_payload = CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD;
        $customer_order_payment_payload['customer_order_id'] = 314159265;

        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize($customer_order_payment_payload, CustomerOrderPaymentCreationRequestDto::class);

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Customer order not found with id "314159265"');
        $this->getTestedInstance()->create($payload);
    }

    /** Tests response without a payment v2. */
    public function test_response_without_a_payment_v2(): void
    {
        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize(
                CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD,
                CustomerOrderPaymentCreationRequestDto::class
            );
        $response = $this->getTestedInstance()->create($payload);
        $this->assertEmpty($response->action);
    }

    /** Tests response with a successful payment v2. */
    public function test_response_with_a_successful_payment_v2(): void
    {
        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize(
                CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD,
                CustomerOrderPaymentCreationRequestDto::class
            );

        $payment_state = new class() implements PaymentV2StateInterface {
            public function isValidWithProvidedOrderContext(
                CustomerOrderCreationContextDto $context_entity
            ): CustomerOrderCreationContextDto {
                return $context_entity;
            }

            public function isValidWithExistingCustomerOrder(
                CustomerOrderPaymentCreationRequestDto $request_context,
                CustomerOrderBasicInfo $customer_order_basic_info
            ): CustomerOrderPaymentCreationRequestDto {
                /** @var CustomerOrderPaymentCreationContextDto $payment */
                $payment = $request_context->payments[0];
                $payment->workflow = PaymentWorkflow::V2;

                $verified_payment = new PaymentV2CreationPayload();
                $verified_payment->code = $payment->payment_mean;
                $verified_payment->amount = $payment->amount;
                $verified_payment->currency_code = 'EUR';
                $verified_payment->extra_data = $payment->extra_data;
                $verified_payment->workflow = $payment->workflow;
                $verified_payment->return_url = 'DUMMY';

                $payment->setPaymentV2VerifiedPayment($verified_payment);

                $request_context->payments = [$payment];

                return $request_context;
            }

            public function canHandle(string $payment_method_code, int $customer_id): bool
            {
                return true;
            }
        };

        $payment_creator = new class() implements PaymentV2CreatorInterface {
            public function create(CustomerOrderCreationContextDto $order_context, int $customer_order_id): ?array
            {
                return [];
            }

            public function add(CustomerOrderPaymentCreationRequestDto $request_context): ?array
            {
                return ['action' => ['redirect_to' => 'url/to']];
            }
        };

        $instance = new CustomerOrderPaymentCreator(
            self::$container->get(LegacyPdo::class),
            self::$container->get(ValidatorInterface::class),
            self::$container->get(CustomerOrderRepository::class),
            self::$container->get(CustomerOrderPaymentWriteRepository::class),
            self::$container->get(CustomerOrderPaymentReadRepository::class),
            self::$container->get(SvdGiftCardManager::class),
            $payment_state,
            $payment_creator,
            self::$container->get(SerializerInterface::class)
        );
        $instance->setDataLoader(self::$container->get(DataLoaderInterface::class));
        $instance->setLogger(self::$container->get(LoggerInterface::class));

        $response = $instance->create($payload);
        $this->assertEquals(['redirect_to' => 'url/to'], $response->action);
    }

    /** Tests response with a failed payment v2. */
    public function test_response_with_a_failed_payment_v2(): void
    {
        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize(
                CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD,
                CustomerOrderPaymentCreationRequestDto::class
            );

        $payment_state = new class() implements PaymentV2StateInterface {
            public function isValidWithProvidedOrderContext(
                CustomerOrderCreationContextDto $context_entity
            ): CustomerOrderCreationContextDto {
                return $context_entity;
            }

            public function isValidWithExistingCustomerOrder(
                CustomerOrderPaymentCreationRequestDto $request_context,
                CustomerOrderBasicInfo $customer_order_basic_info
            ): CustomerOrderPaymentCreationRequestDto {
                /** @var CustomerOrderPaymentCreationContextDto $payment */
                $payment = $request_context->payments[0];
                $payment->workflow = PaymentWorkflow::V2;

                $verified_payment = new PaymentV2CreationPayload();
                $verified_payment->code = $payment->payment_mean;
                $verified_payment->amount = $payment->amount;
                $verified_payment->currency_code = 'EUR';
                $verified_payment->extra_data = $payment->extra_data;
                $verified_payment->workflow = $payment->workflow;
                $verified_payment->return_url = 'DUMMY';

                $payment->setPaymentV2VerifiedPayment($verified_payment);

                $request_context->payments = [$payment];

                return $request_context;
            }

            public function canHandle(string $payment_method_code, int $customer_id): bool
            {
                return true;
            }
        };

        $payment_creator = new class() implements PaymentV2CreatorInterface {
            public function create(CustomerOrderCreationContextDto $order_context, int $customer_order_id): ?array
            {
                return [];
            }

            public function add(CustomerOrderPaymentCreationRequestDto $request_context): ?array
            {
                throw new \Exception('An error');
            }
        };

        $instance = new CustomerOrderPaymentCreator(
            self::$container->get(LegacyPdo::class),
            self::$container->get(ValidatorInterface::class),
            self::$container->get(CustomerOrderRepository::class),
            self::$container->get(CustomerOrderPaymentWriteRepository::class),
            self::$container->get(CustomerOrderPaymentReadRepository::class),
            self::$container->get(SvdGiftCardManager::class),
            $payment_state,
            $payment_creator,
            self::$container->get(SerializerInterface::class)
        );
        $instance->setDataLoader(self::$container->get(DataLoaderInterface::class));
        $instance->setLogger(self::$container->get(LoggerInterface::class));

        $response = $instance->create($payload);
        $this->assertEquals(['cancel' => true], $response->action);
    }

    /** Tests failure when payment method is not found. */
    public function test_should_fail_if_payment_method_is_not_found(): void
    {
        $customer_order_payment_payload = CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD;
        $customer_order_payment_payload['payments'][0]['amount'] = 123;
        $customer_order_payment_payload['payments'][0]['payment_mean'] = 'INFINITE_MOULA';
        $customer_order_payment_payload['payments'][0]['created_at'] = '2021-01-01 00:00:00';
        $payload = self::$container
            ->get(SerializerInterface::class)
            ->denormalize($customer_order_payment_payload, CustomerOrderPaymentCreationRequestDto::class);

        $this->expectException(\UnexpectedValueException::class);
        $this->expectExceptionMessage('Could not create payment with payment method "INFINITE_MOULA". Does it exist ?');
        $this->getTestedInstance()->create($payload);
    }
}
