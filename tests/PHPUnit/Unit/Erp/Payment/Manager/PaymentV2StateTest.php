<?php

namespace PHPUnit\Unit\Erp\Payment\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Tests\Mock\Erp\CustomerOrder\CustomerOrderPayload;
use App\Tests\Mock\Erp\CustomerOrderPayment\CustomerOrderPaymentCreatorPayload;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\Carrier\Entity\ShipmentMethodCarrierEntity;
use SonVideo\Erp\Carrier\Entity\ShipmentMethodEntity;
use SonVideo\Erp\Carrier\Mysql\Repository\ShipmentMethodReadRepository;
use SonVideo\Erp\Customer\Contract\CustomerFromCmsInterface;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CustomerOrderBasicInfo;
use SonVideo\Erp\CustomerOrderPayment\Dto\CustomerOrderPaymentCreationRequestDto;
use SonVideo\Erp\Payment\Client\PaymentV2Client;
use SonVideo\Erp\Payment\Entity\PaymentV2CreationPayload;
use SonVideo\Erp\Payment\Manager\PaymentV2State;
use SonVideo\Erp\Referential\Payment\PaymentMean;
use SonVideo\Erp\Referential\PaymentWorkflow;
use SonVideo\Erp\System\Manager\FeatureStatus;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PaymentV2StateTest extends KernelTestCase
{
    private const WITH_ACTIVE_FEATURE_FLAG = true;
    private const WITHOUT_ACTIVE_FEATURE_FLAG = false;

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets an active feature status. */
    private function getActiveFeatureStatus(): FeatureStatus
    {
        $customer_manager = new class() implements CustomerFromCmsInterface {
            public function getCmsAccountInformations(int $customer_id, array $fields = []): array
            {
                return ['preferences' => ['feature_flags' => ['payment_v2']]];
            }
        };

        return new FeatureStatus(
            self::$container->get(ParameterModel::class),
            $customer_manager,
            self::$container->get('logger')
        );
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(
        MockHandler $mock,
        bool $use_feature_flags = self::WITH_ACTIVE_FEATURE_FLAG
    ): PaymentV2State {
        $handler_stack = HandlerStack::create($mock);

        $feature_status_class = $use_feature_flags
            ? $this->getActiveFeatureStatus()
            : self::$container->get(FeatureStatus::class);

        $tested_class = new PaymentV2State(
            new class() extends ShipmentMethodReadRepository {
                public function __construct()
                {
                }

                public function findOneById(int $shipment_method_id, $include_carrier = 'FALSE'): ShipmentMethodEntity
                {
                    $carrier = new ShipmentMethodCarrierEntity();
                    $carrier->code = 'DUMMY';
                    $carrier->is_express = false;

                    $entity = new ShipmentMethodEntity();
                    $entity->shipment_method_id = 1;
                    $entity->store_pickup_id = null;
                    $entity->carrier = $carrier;

                    return $entity;
                }
            },
            new PaymentV2Client(
                '',
                '',
                self::$container->get(LoggerInterface::class),
                new Client(['handler' => $handler_stack])
            ),
            self::$container->get(SerializerInterface::class),
            $feature_status_class
        );

        $tested_class->setLogger(self::$container->get(LoggerInterface::class));

        return $tested_class;
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests that the API is called successfully. */
    public function test_api_is_called_successfully(): void
    {
        // FIRST METHOD isValidWithProvidedOrderContext
        /** @var CustomerOrderCreationContextDto $order */
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode(),
            CustomerOrderCreationContextDto::class
        );

        $order->return_url = 'url/to';

        $payment_v2 = new PaymentV2CreationPayload();
        $payment_v2->code = $order->payments[0]->payment_mean;
        $payment_v2->amount = 0;
        $payment_v2->currency_code = 'EUR';
        $payment_v2->workflow = PaymentWorkflow::V2;
        $payment_v2->return_url = 'url/to';
        // Internal reference for payment mapping after verification in payment v2
        $payment_v2->external_reference = 'NEW_0';

        $result = $this->getTestedInstance(
            new MockHandler([
                new Response(200, [], json_encode(['data' => [(array) $payment_v2]], JSON_THROW_ON_ERROR)),
            ])
        )->isValidWithProvidedOrderContext($order);

        $this->assertStringContainsString(PaymentWorkflow::V2, $result->payments[0]->workflow);
        $this->assertStringContainsString('url/to', $result->payments[0]->getPaymentV2VerifiedPayment()->return_url);

        // SECOND METHOD isValidWithExistingCustomerOrder
        /** @var CustomerOrderPaymentCreationRequestDto $order */
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD,
            CustomerOrderPaymentCreationRequestDto::class
        );
        /** @var CustomerOrderBasicInfo $basic_infos */
        $basic_infos = $this->getSerializer()->denormalize(
            CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD,
            CustomerOrderBasicInfo::class
        );

        $order->return_url = 'url/to';

        $payment_v2 = new PaymentV2CreationPayload();
        $payment_v2->code = $order->payments[0]->payment_mean;
        $payment_v2->amount = 0;
        $payment_v2->currency_code = 'EUR';
        $payment_v2->workflow = PaymentWorkflow::V2;
        $payment_v2->return_url = 'url/to';
        // Internal reference for payment mapping after verification in payment v2
        $payment_v2->external_reference = 'NEW_0';

        $result = $this->getTestedInstance(
            new MockHandler([
                new Response(200, [], json_encode(['data' => [(array) $payment_v2]], JSON_THROW_ON_ERROR)),
            ])
        )->isValidWithExistingCustomerOrder($order, $basic_infos);

        $this->assertStringContainsString(PaymentWorkflow::V2, $result->payments[0]->workflow);
        $this->assertStringContainsString('url/to', $result->payments[0]->getPaymentV2VerifiedPayment()->return_url);
    }

    /** Tests that the API call fails. */
    public function test_api_call_fails(): void
    {
        // FIRST METHOD isValidWithProvidedOrderContext
        /** @var CustomerOrderCreationContextDto $order */
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode(),
            CustomerOrderCreationContextDto::class
        );

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage(
            'This origin uses Payment V2, the return URL (context->return_url) should not be null'
        );

        $this->getTestedInstance(new MockHandler([new Response(200, [], '{}')]))->isValidWithProvidedOrderContext(
            $order
        );
    }

    /** Tests that the API call fails for existing customer order. */
    public function test_api_call_fails_for_existing_customer_order(): void
    {
        // SECOND METHOD isValidWithExistingCustomerOrder
        /** @var CustomerOrderPaymentCreationRequestDto $order */
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD,
            CustomerOrderPaymentCreationRequestDto::class
        );
        /** @var CustomerOrderBasicInfo $basic_infos */
        $basic_infos = $this->getSerializer()->denormalize(
            CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD,
            CustomerOrderBasicInfo::class
        );

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage(
            'This origin uses Payment V2, the return URL (context->return_url) should not be null'
        );

        $this->getTestedInstance(new MockHandler([new Response(200, [], '{}')]))->isValidWithExistingCustomerOrder(
            $order,
            $basic_infos
        );
    }

    /** Tests that it should do nothing if feature flag is not enabled. */
    public function test_it_should_do_nothing_if_feature_flag_is_not_enabled(): void
    {
        // FIRST METHOD isValidWithProvidedOrderContext
        $customer_order = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $customer_order['payments'][0]['payment_mean'] = PaymentMean::WORLDLINE_FLOA;

        /** @var CustomerOrderCreationContextDto $order */
        $order = $this->getSerializer()->denormalize($customer_order, CustomerOrderCreationContextDto::class);

        $result = $this->getTestedInstance(
            new MockHandler([
                new RequestException('Test failed: Payment API v2 should not be called', new Request('GET', 'anything')),
            ]),
            self::WITHOUT_ACTIVE_FEATURE_FLAG
        )->isValidWithProvidedOrderContext($order);

        $this->assertStringContainsString(PaymentWorkflow::LEGACY, $result->payments[0]->workflow);

        // SECOND METHOD isValidWithExistingCustomerOrder
        $customer_order_payment = CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD;
        $customer_order_payment['payments'][0]['payment_mean'] = PaymentMean::WORLDLINE_FLOA;

        /** @var CustomerOrderPaymentCreationRequestDto $order */
        $order = $this->getSerializer()->denormalize(
            $customer_order_payment,
            CustomerOrderPaymentCreationRequestDto::class
        );
        /** @var CustomerOrderBasicInfo $basic_infos */
        $basic_infos = $this->getSerializer()->denormalize($customer_order_payment, CustomerOrderBasicInfo::class);

        $result = $this->getTestedInstance(
            new MockHandler([
                new RequestException('Test failed: Payment API v2 should not be called', new Request('GET', 'anything')),
            ]),
            self::WITHOUT_ACTIVE_FEATURE_FLAG
        )->isValidWithExistingCustomerOrder($order, $basic_infos);

        $this->assertStringContainsString(PaymentWorkflow::LEGACY, $result->payments[0]->workflow);
    }
}
