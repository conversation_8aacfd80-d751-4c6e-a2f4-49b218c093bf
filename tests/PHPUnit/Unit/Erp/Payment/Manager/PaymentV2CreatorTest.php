<?php

namespace PHPUnit\Unit\Erp\Payment\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Tests\Mock\Erp\CustomerOrder\CustomerOrderPayload;
use App\Tests\Mock\Erp\CustomerOrderPayment\CustomerOrderPaymentCreatorPayload;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrderPayment\Dto\CustomerOrderPaymentCreationRequestDto;
use SonVideo\Erp\Payment\Client\PaymentV2Client;
use SonVideo\Erp\Payment\Manager\PaymentV2Creator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PaymentV2CreatorTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(MockHandler $mock): PaymentV2Creator
    {
        $handler_stack = HandlerStack::create($mock);

        return new PaymentV2Creator(
            new PaymentV2Client(
                '',
                '',
                self::$container->get(LoggerInterface::class),
                new Client(['handler' => $handler_stack])
            ),
            self::$container->get(SerializerInterface::class)
        );
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests that the API is called successfully. */
    public function test_api_is_called_successfully(): void
    {
        // FIRST METHOD create
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode(),
            CustomerOrderCreationContextDto::class
        );

        $order->return_url = 'url/to';

        $result = $this->getTestedInstance(
            new MockHandler([new Response(200, [], '{"data":{"action":"none"}}')])
        )->create($order, 123);

        $this->assertStringContainsString('none', $result['action']);

        // SECOND METHOD add
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPaymentCreatorPayload::VALID_PAYLOAD,
            CustomerOrderPaymentCreationRequestDto::class
        );

        $order->return_url = 'url/to';

        $result = $this->getTestedInstance(new MockHandler([new Response(200, [], '{"data":{"action":"toto"}}')]))->add(
            $order
        );

        $this->assertStringContainsString('toto', $result['action']);
    }
}
