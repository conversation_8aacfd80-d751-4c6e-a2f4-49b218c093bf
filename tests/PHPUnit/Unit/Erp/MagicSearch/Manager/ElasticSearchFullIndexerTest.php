<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\MagicSearch\Manager;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\MagicSearch\Manager\ElasticSearchFullIndexer as TestedClass;
use SonVideo\Erp\MagicSearch\Manager\Index\DataProvider\FullIndex\ArticlesFullIndexIndexDataProvider;
use SonVideo\Erp\MagicSearch\Manager\Index\DataProvider\FullIndex\CustomersFullIndexIndexDataProvider;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Output\StreamOutput;

class ElasticSearchFullIndexerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql', 'magic_search/data.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested class. */
    protected function getTestedClass(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** Tests the generate method with no data providers. */
    public function test_generate_result_is_empty(): void
    {
        $output = new StreamOutput(fopen('php://memory', 'w', false));
        $result = $this->getTestedClass()->generate($output, []);

        // Nothing should be treated when no data providers is given
        $this->assertCount(0, $result);
    }

    /** Tests the generate method with data providers. */
    public function test_generate_result_when_not_empty(): void
    {
        $output = new StreamOutput(fopen('php://memory', 'w', false));
        $articles_data_provider = self::$container->get(ArticlesFullIndexIndexDataProvider::class);
        $result = $this->getTestedClass()->generate($output, [$articles_data_provider]);

        // Articles are retrieved successfully via the data provider process
        $this->assertCount(1, $result);
        $this->assertCount(5, $result['articles']);
        $this->assertEquals('articles', $result['articles']['name']);
        $this->assertEquals(4, $result['articles']['collected']);
        $this->assertEquals(4, $result['articles']['buffered']);
        $this->assertEquals(0, $result['articles']['errors']);
        $this->assertEquals('TRUE', $result['articles']['finished']);

        $output = new StreamOutput(fopen('php://memory', 'w', false));
        $customers_data_provider = self::$container->get(CustomersFullIndexIndexDataProvider::class);
        $result = $this->getTestedClass()->generate($output, [$customers_data_provider]);

        // Customers are retrieved successfully via the data provider process
        $this->assertCount(1, $result);
        $this->assertCount(5, $result['customers']);
        $this->assertEquals('customers', $result['customers']['name']);
        $this->assertEquals(6, $result['customers']['collected']);
        $this->assertEquals(6, $result['customers']['buffered']);
        $this->assertEquals(0, $result['customers']['errors']);
        $this->assertEquals('TRUE', $result['customers']['finished']);
    }
}
