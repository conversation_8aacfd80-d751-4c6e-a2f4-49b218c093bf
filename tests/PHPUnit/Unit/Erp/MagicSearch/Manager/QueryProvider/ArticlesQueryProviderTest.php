<?php

namespace PHPUnit\Unit\Erp\MagicSearch\Manager\QueryProvider;

use PHPUnit\Framework\TestCase;
use SonVideo\Erp\MagicSearch\Entity\ElasticSearchQueryRequest;
use SonVideo\Erp\MagicSearch\Entity\MagicSearchHttpRequestPayload;
use SonVideo\Erp\MagicSearch\Manager\QueryProvider\ArticlesQueryProvider;
use SonVideo\Erp\Referential\ArticleStatus;
use SonVideo\Erp\Referential\MagicSearch;

class ArticlesQueryProviderTest extends TestCase
{
    private ArticlesQueryProvider $query_provider;

    public function setUp(): void
    {
        parent::setUp();
        $this->query_provider = new ArticlesQueryProvider();
    }

    public function test_can_handle(): void
    {
        $this->assertTrue($this->query_provider->canHandle(MagicSearch::ARTICLE_INDEX));
        $this->assertTrue($this->query_provider->canHandle(MagicSearch::ALL_INDEX));
        $this->assertFalse($this->query_provider->canHandle('invalid_index'));
    }

    public function test_get_context(): void
    {
        $this->assertEquals('articles', $this->query_provider->getContext());
    }

    public function test_get_body_with_empty_search_terms(): void
    {
        $payload = new MagicSearchHttpRequestPayload();
        $payload->search_terms = '';
        $payload->context = 'articles';

        $result = $this->query_provider->getBody($payload);

        $this->assertInstanceOf(ElasticSearchQueryRequest::class, $result);

        $result_array = $result->toArray();
        $this->assertArrayHasKey('_source', $result_array);
        $this->assertArrayHasKey('query', $result_array);
        $this->assertArrayHasKey('function_score', $result_array['query']);
        $this->assertArrayHasKey('query', $result_array['query']['function_score']);
        $this->assertArrayHasKey('functions', $result_array['query']['function_score']);

        // Verify the source columns are present
        $this->assertContains('article_id', $result_array['_source']);
        $this->assertContains('sku', $result_array['_source']);
        $this->assertContains('name', $result_array['_source']);
    }

    public function test_get_body_with_string_search_terms(): void
    {
        $payload = new MagicSearchHttpRequestPayload();
        $payload->search_terms = 'test';
        $payload->context = 'articles';

        $result = $this->query_provider->getBody($payload);
        $result_array = $result->toArray();

        // Verify the query structure
        $this->assertArrayHasKey('query', $result_array);
        $this->assertArrayHasKey('function_score', $result_array['query']);
        $this->assertArrayHasKey('query', $result_array['query']['function_score']);
        $this->assertArrayHasKey('bool', $result_array['query']['function_score']['query']);
        $this->assertArrayHasKey('should', $result_array['query']['function_score']['query']['bool']);

        // Verify search conditions are present
        $should_conditions = $result_array['query']['function_score']['query']['bool']['should'];
        $this->assertIsArray($should_conditions);
        $this->assertNotEmpty($should_conditions);

        // Check for specific boosts that should be applied for string search terms
        $functions = $result_array['query']['function_score']['functions'];

        // Check for name match boost (should have weight 5)
        $name_match_boost = null;
        foreach ($functions as $function) {
            if (isset($function['filter']['match']['name']) && 'test' === $function['filter']['match']['name']) {
                $name_match_boost = $function;
                break;
            }
        }
        $this->assertNotNull($name_match_boost, 'Name match boost should be present for string search terms');
        $this->assertEquals(2, $name_match_boost['weight'], 'Name match weight should be 2 for string search terms');

        // Check for category name match boost (should have weight 1)
        $category_name_match_boost = null;
        foreach ($functions as $function) {
            if (
                isset($function['filter']['match']['category.name']) &&
                'test' === $function['filter']['match']['category.name']
            ) {
                $category_name_match_boost = $function;
                break;
            }
        }
        $this->assertNotNull(
            $category_name_match_boost,
            'Category name match boost should be present for string search terms'
        );
        $this->assertEquals(
            1,
            $category_name_match_boost['weight'],
            'Category name match weight should be 1 for string search terms'
        );

        // Check for subcategory name match boost (should have weight 1)
        $subcategory_name_match_boost = null;
        foreach ($functions as $function) {
            if (
                isset($function['filter']['match']['subcategory.name']) &&
                'test' === $function['filter']['match']['subcategory.name']
            ) {
                $subcategory_name_match_boost = $function;
                break;
            }
        }
        $this->assertNotNull(
            $subcategory_name_match_boost,
            'Subcategory name match boost should be present for string search terms'
        );
        $this->assertEquals(
            1,
            $subcategory_name_match_boost['weight'],
            'Subcategory name match weight should be 1 for string search terms'
        );

        // Check that article_id exact match is NOT present for string search terms
        $article_id_exact_match_boost = null;
        foreach ($functions as $function) {
            if (
                isset($function['filter']['match']['article_id.keyword']) &&
                'test' === $function['filter']['match']['article_id.keyword']
            ) {
                $article_id_exact_match_boost = $function;
                break;
            }
        }
        $this->assertNull(
            $article_id_exact_match_boost,
            'Article ID exact match should not be present for string search terms'
        );
        $destock_function = null;
        foreach ($functions as $function) {
            if (
                isset($function['filter']['bool']['must'][0]['match']['type']) &&
                'destock' === $function['filter']['bool']['must'][0]['match']['type']
            ) {
                $destock_function = $function;
                break;
            }
        }

        $this->assertNotNull($destock_function, 'Destock function not found');
        $this->assertEquals(
            0.3,
            $destock_function['weight'],
            'Destock weight should be 0.3 when searching for destock'
        );
    }

    public function test_get_body_with_destock_search_term(): void
    {
        $payload = new MagicSearchHttpRequestPayload();
        $payload->search_terms = 'destock test';
        $payload->context = 'articles';

        $result = $this->query_provider->getBody($payload);
        $result_array = $result->toArray();

        // Verify the query structure
        $this->assertArrayHasKey('query', $result_array);
        $this->assertArrayHasKey('function_score', $result_array['query']);

        // Check that functions contain the destock boost
        $functions = $result_array['query']['function_score']['functions'];

        // Find the destock function (should have weight 10 when searching for destock)
        $destock_function = null;
        foreach ($functions as $function) {
            if (
                isset($function['filter']['bool']['must'][0]['match']['type']) &&
                'destock' === $function['filter']['bool']['must'][0]['match']['type']
            ) {
                $destock_function = $function;
                break;
            }
        }

        $this->assertNotNull($destock_function, 'Destock function not found');
        $this->assertEquals(10, $destock_function['weight'], 'Destock weight should be 10 when searching for destock');
    }

    public function test_get_body_with_filters(): void
    {
        $payload = new MagicSearchHttpRequestPayload();
        $payload->search_terms = 'test';
        $payload->context = 'articles';
        $payload->filters = [['term' => ['brand.id' => 123]]];

        $result = $this->query_provider->getBody($payload);
        $result_array = $result->toArray();

        // Verify filters are present
        $this->assertArrayHasKey('filter', $result_array['query']['function_score']['query']['bool']);
        $this->assertEquals($payload->filters, $result_array['query']['function_score']['query']['bool']['filter']);
    }

    public function test_get_body_with_excludes(): void
    {
        $payload = new MagicSearchHttpRequestPayload();
        $payload->search_terms = 'test';
        $payload->context = 'articles';
        $payload->excludes = [['term' => ['status' => ArticleStatus::NON]]];

        $result = $this->query_provider->getBody($payload);
        $result_array = $result->toArray();

        // Verify excludes are present
        $this->assertArrayHasKey('must_not', $result_array['query']['function_score']['query']['bool']);
        $this->assertEquals($payload->excludes, $result_array['query']['function_score']['query']['bool']['must_not']);
    }

    public function test_get_body_with_search_terms_and_filters(): void
    {
        $payload = new MagicSearchHttpRequestPayload();
        $payload->search_terms = 'test';
        $payload->context = 'articles';
        $payload->filters = [['term' => ['brand.id' => 123]]];

        $result = $this->query_provider->getBody($payload);
        $result_array = $result->toArray();

        // Verify the query structure for search terms with filters
        $this->assertArrayHasKey('should', $result_array['query']['function_score']['query']['bool']);
        $should_conditions = $result_array['query']['function_score']['query']['bool']['should'];

        // When both search terms and filters are present, there should be a specific multi_match condition
        $has_multi_match = false;
        foreach ($should_conditions as $condition) {
            if (
                isset($condition['multi_match']['fields']) &&
                in_array('big_search_field', $condition['multi_match']['fields'], true) &&
                in_array('sku', $condition['multi_match']['fields'], true)
            ) {
                $has_multi_match = true;
                break;
            }
        }

        $this->assertTrue($has_multi_match, 'Should have multi_match condition for search terms with filters');
    }

    public function test_contains_space(): void
    {
        $reflection_class = new \ReflectionClass(ArticlesQueryProvider::class);
        $method = $reflection_class->getMethod('containsSpace');
        $method->setAccessible(true);

        $this->assertTrue($method->invoke($this->query_provider, 'test term'));
        $this->assertFalse($method->invoke($this->query_provider, 'test'));
    }

    public function test_is_searching_destock(): void
    {
        $reflection_class = new \ReflectionClass(ArticlesQueryProvider::class);
        $method = $reflection_class->getMethod('isSearchingDestock');
        $method->setAccessible(true);

        // Set up the special_keys property
        $special_keys_property = $reflection_class->getProperty('special_keys');
        $special_keys_property->setAccessible(true);

        // Test when not searching for destock
        $special_keys_property->setValue($this->query_provider, []);
        $this->assertFalse($method->invoke($this->query_provider));

        // Test when searching for destock
        $special_keys_property->setValue($this->query_provider, ['destock']);
        $this->assertTrue($method->invoke($this->query_provider));
    }

    public function test_get_body_with_digit_search_term(): void
    {
        $payload = new MagicSearchHttpRequestPayload();
        $payload->search_terms = '12345';
        $payload->context = 'articles';

        $result = $this->query_provider->getBody($payload);
        $result_array = $result->toArray();

        // Verify the query structure
        $this->assertArrayHasKey('query', $result_array);
        $this->assertArrayHasKey('function_score', $result_array['query']);
        $this->assertArrayHasKey('functions', $result_array['query']['function_score']);

        $functions = $result_array['query']['function_score']['functions'];

        // Debug the functions array to understand its structure
        $has_article_id_match = false;
        foreach ($functions as $function) {
            if (
                isset($function['filter'], $function['weight']) &&
                is_array($function['filter']) &&
                10 === $function['weight']
            ) {
                $has_article_id_match = true;
                break;
            }
        }

        $this->assertTrue($has_article_id_match, 'Should have a boost function with weight 10 for digit search terms');

        // Check that brand name match IS present for digit search terms with weight 3
        $brand_name_match_boost = null;
        foreach ($functions as $function) {
            if (
                isset($function['filter']['match']['brand.name']) &&
                '12345' === $function['filter']['match']['brand.name']
            ) {
                $brand_name_match_boost = $function;
                break;
            }
        }

        $this->assertNotNull($brand_name_match_boost, 'Brand name match should be present for digit search terms');
        $this->assertEquals(
            3,
            $brand_name_match_boost['weight'],
            'Brand name match weight should be 3 for digit search terms'
        );
    }

    public function test_get_body_with_digit_search_term_should_conditions(): void
    {
        $payload = new MagicSearchHttpRequestPayload();
        $payload->search_terms = '12345';
        $payload->context = 'articles';

        $result = $this->query_provider->getBody($payload);
        $result_array = $result->toArray();

        // Verify the should conditions for digit search terms
        $this->assertArrayHasKey('should', $result_array['query']['function_score']['query']['bool']);
        $should_conditions = $result_array['query']['function_score']['query']['bool']['should'];

        // Check for multi_match with barcodes and sku fields
        $has_barcodes_match = false;
        foreach ($should_conditions as $condition) {
            if (
                isset($condition['multi_match']['fields']) &&
                in_array('barcodes.code128', $condition['multi_match']['fields'], true) &&
                in_array('barcodes.eans', $condition['multi_match']['fields'], true) &&
                in_array('sku', $condition['multi_match']['fields'], true)
            ) {
                $has_barcodes_match = true;
                break;
            }
        }

        $this->assertTrue(
            $has_barcodes_match,
            'Should have multi_match condition with barcodes for digit search terms'
        );
    }
}
