<?php

namespace PHPUnit\Unit\Erp\Customer\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Customer\Dto\CustomerEncoursUpdateDto;
use SonVideo\Erp\Customer\Manager\CustomerEncoursUpdater;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerEncoursRepository;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerEncoursUpdaterTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users_backoffice.sql', 'customer/customers.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): CustomerEncoursUpdater
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        return new CustomerEncoursUpdater(
            self::$container->get(LegacyPdo::class),
            self::$container->get(SystemEventLogger::class),
            self::$container->get(CustomerEncoursRepository::class),
            self::$container->get(SerializerInterface::class),
            self::$container->get(AccountQueryRepository::class),
            self::$container->get(LoggerInterface::class)
        );
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        $customer_encours_updater = $this->getTestedInstance();
        $data = $this->getSerializer()->denormalize(
            ['id_prospect' => 5, 'encours' => 451],
            CustomerEncoursUpdateDto::class
        );

        // Test update encours with cron
        $nb_affected = $customer_encours_updater->update($data);
        $this->assertEquals(1, $nb_affected);

        $system_event = $this->fetchLastSystemEvents();
        $this->assertIsArray($system_event);
        $this->assertEquals('customer_encours.update', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"customer": 5}, "data": {"encours_interne": {"new": 451, "old": 0}}, "meta": {"updated_by": {"user_id": 1000, "lastname": "backoffice", "username": "backoffice", "firstname": ""}}}',
            $system_event['payload']
        );
    }

    /** Tests the update method with a non-existent customer. */
    public function test_update_with_non_existent_customer(): void
    {
        $customer_encours_updater = $this->getTestedInstance();
        $data = $this->getSerializer()->denormalize(
            ['id_prospect' => 5555, 'encours' => 666],
            CustomerEncoursUpdateDto::class
        );

        // Update fail customer not found
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Customer with id 5555 not found.');
        $customer_encours_updater->update($data);
    }

    /** Fetches the last system events. */
    private function fetchLastSystemEvents(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
