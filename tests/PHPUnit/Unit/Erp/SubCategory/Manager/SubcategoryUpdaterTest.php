<?php

namespace PHPUnit\Unit\Erp\SubCategory\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\SubCategory\Dto\UpdateContext\SubcategoryContextDto;
use SonVideo\Erp\SubCategory\Manager\SubcategoryUpdater;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class SubcategoryUpdaterTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'category/categories.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): SubcategoryUpdater
    {
        return self::$container->get(SubcategoryUpdater::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /**
     * Tests the update method.
     *
     * @throws NotFoundException
     * @throws ExceptionInterface
     * @throws \JsonException
     * @throws InternalErrorException
     * @throws \Exception
     */
    public function test_update(): void
    {
        $subcategory_data = [
            'subcategory_id' => 95,
            'name' => 'Enceintes de tunning',
            'parent_category_id' => 96,
            'warranty_type' => 'NON',
            'outsize' => true,
            'bbac_subtype_id' => 33,
            'charged_delivery' => 8.0,
            'subcategory_type' => 'IMAGE',
            'marketplace_categories' => '[
                {
                    id: 368,
                    marketplace_id: 11
                }
            ]',
            'seller_commission_config' => json_encode(['ldw_exclude' => true, 'cultural_product' => true]),
            'custom_code' => '00011000',
            'ecotax_code' => '75368',
            'user_id' => 5,
        ];

        /** @var SubcategoryContextDto $dto_success */
        $dto_success = $this->getSerializer()->denormalize($subcategory_data, SubcategoryContextDto::class);
        $subcategory_updater = $this->getTestedInstance();

        // Update subcategory success
        $subcategory_id = 95;

        $subcategory_before_update = $this->fetchSubcategory($subcategory_id);
        $this->assertEquals('Enceintes encastrables', $subcategory_before_update['souscategorie']);
        $this->assertEquals('12345678', $subcategory_before_update['code_douanier']);
        $this->assertEquals('01010', $subcategory_before_update['code_ecotaxe']);
        $this->assertEquals(null, $subcategory_before_update['user_id']);

        $subcategory_updater->update($dto_success);

        $subcategory_after_update = $this->fetchSubcategory($subcategory_id);
        $this->assertEquals($dto_success->name, $subcategory_after_update['souscategorie']);
        $this->assertEquals($dto_success->custom_code, $subcategory_after_update['code_douanier']);
        $this->assertEquals($dto_success->ecotax_code, $subcategory_after_update['code_ecotaxe']);
        $this->assertEquals($dto_success->user_id, $subcategory_after_update['user_id']);
    }

    /** Tests the update method with an unknown warranty type. */
    public function test_update_with_unknown_warranty_type(): void
    {
        $subcategory_data = [
            'subcategory_id' => 95,
            'name' => 'Enceintes de tunning',
            'parent_category_id' => 96,
            'warranty_type' => 'YATCH',
            'outsize' => true,
            'bbac_subtype_id' => 33,
            'charged_delivery' => 8.0,
            'subcategory_type' => 'IMAGE',
            'marketplace_categories' => '[
                {
                    id: 368,
                    marketplace_id: 11
                }
            ]',
            'seller_commission_config' => json_encode(['ldw_exclude' => true, 'cultural_product' => true]),
            'custom_code' => '00011000',
            'ecotax_code' => '75368',
        ];

        /** @var SubcategoryContextDto $dto */
        $dto = $this->getSerializer()->denormalize($subcategory_data, SubcategoryContextDto::class);
        $subcategory_updater = $this->getTestedInstance();

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessageMatches('/Integrity constraint violation/');
        $subcategory_updater->update($dto);
    }

    /** Tests the update method with an empty warranty type. */
    public function test_update_with_empty_warranty_type(): void
    {
        $subcategory_data = [
            'subcategory_id' => 95,
            'name' => 'Enceintes de tunning',
            'parent_category_id' => 96,
            'warranty_type' => '',
            'outsize' => true,
            'bbac_subtype_id' => 33,
            'charged_delivery' => 8.0,
            'subcategory_type' => 'IMAGE',
            'marketplace_categories' => '[
                {
                    id: 368,
                    marketplace_id: 11
                }
            ]',
            'seller_commission_config' => json_encode(['ldw_exclude' => true, 'cultural_product' => true]),
            'custom_code' => '00011000',
            'ecotax_code' => '75368',
        ];

        /** @var SubcategoryContextDto $dto */
        $dto = $this->getSerializer()->denormalize($subcategory_data, SubcategoryContextDto::class);
        $subcategory_updater = $this->getTestedInstance();

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Invalid parameters');
        $subcategory_updater->update($dto);
    }

    /** Tests the update method with a null warranty type. */
    public function test_update_with_null_warranty_type(): void
    {
        $subcategory_data = [
            'subcategory_id' => 95,
            'name' => 'Enceintes de tunning',
            'parent_category_id' => 96,
            'warranty_type' => null,
            'outsize' => true,
            'bbac_subtype_id' => 33,
            'charged_delivery' => 8.0,
            'subcategory_type' => 'IMAGE',
            'marketplace_categories' => '[
                {
                    id: 368,
                    marketplace_id: 11
                }
            ]',
            'seller_commission_config' => json_encode(['ldw_exclude' => true, 'cultural_product' => true]),
            'custom_code' => '00011000',
            'ecotax_code' => '75368',
        ];

        /** @var SubcategoryContextDto $dto */
        $dto = $this->getSerializer()->denormalize($subcategory_data, SubcategoryContextDto::class);
        $subcategory_updater = $this->getTestedInstance();

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Invalid parameters');
        $subcategory_updater->update($dto);
    }

    /** Tests the update method with a null charged delivery. */
    public function test_update_with_null_charged_delivery(): void
    {
        $subcategory_data = [
            'subcategory_id' => 95,
            'name' => 'Enceintes de tunning',
            'parent_category_id' => 96,
            'warranty_type' => 'NON',
            'outsize' => true,
            'bbac_subtype_id' => 33,
            'charged_delivery' => null,
            'subcategory_type' => 'IMAGE',
            'marketplace_categories' => '[
                {
                    id: 368,
                    marketplace_id: 11
                }
            ]',
            'seller_commission_config' => json_encode(['ldw_exclude' => true, 'cultural_product' => true]),
            'custom_code' => '00011000',
            'ecotax_code' => '75368',
        ];

        /** @var SubcategoryContextDto $dto */
        $dto = $this->getSerializer()->denormalize($subcategory_data, SubcategoryContextDto::class);
        $subcategory_updater = $this->getTestedInstance();

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Invalid parameters');
        $subcategory_updater->update($dto);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches a subcategory by ID. */
    protected function fetchSubcategory(int $subcategory_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.CTG_TXN_souscategorie
        WHERE id = :subcategory_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['subcategory_id' => $subcategory_id]);
    }
}
