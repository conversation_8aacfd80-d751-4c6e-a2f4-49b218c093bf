<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Manager\Payment;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Manager\Payment\OgoneStatusManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class OgoneStatusManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order_payment/get_customer_order_payment_ogone.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested class. */
    protected function getTestedClass(): OgoneStatusManager
    {
        return self::$container->get(OgoneStatusManager::class);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Tests the check method. */
    public function test_check(): void
    {
        $this->getTestedClass()->check();

        // Test on ogone response for canceled payment
        $variable = $this->getCustomerOrderPayment(2052062);
        $this->assertEquals('2052062', $variable['id_commande']);
        $this->assertEquals('64', $variable['id_paiement']);
        $this->assertEquals('2052062-4', $variable['creation_justificatif']);
        $this->assertEquals('refuse', $variable['auto_statut']);
        $this->assertEquals('Import manuel (STATUS : 6)', $variable['auto_statut_detail']);

        $corrective = $this->getPaymentCorrective(400);
        $this->assertCount(1, $corrective);
        $this->assertEquals('400', $corrective[0]->id_paiement);
        $this->assertEquals('ogone', $corrective[0]->banque);
        $this->assertEquals('OGN5837496109', $corrective[0]->id_trans_banque);
        $this->assertEquals('CBS-OT', $corrective[0]->moyen);
        $this->assertEquals('euro', $corrective[0]->devise);
        $this->assertEquals('refuse', $corrective[0]->statut);
        $this->assertEquals('Import manuel (STATUS : 6)', $corrective[0]->statut_detail);
        $this->assertNull($corrective[0]->date_acceptation);
        $this->assertEquals($corrective[0]->date_creation, $corrective[0]->date_annulation);

        // Test on ogone response for refused payment
        $variable = $this->getCustomerOrderPayment(2051667);
        $this->assertEquals('2051667', $variable['id_commande']);
        $this->assertEquals('80', $variable['id_paiement']);
        $this->assertEquals('2051667-1', $variable['creation_justificatif']);
        $this->assertEquals('refuse', $variable['auto_statut']);
        $this->assertEquals('Import manuel (STATUS : 1)', $variable['auto_statut_detail']);

        $corrective = $this->getPaymentCorrective(100);
        $this->assertCount(1, $corrective);
        $this->assertEquals('100', $corrective[0]->id_paiement);
        $this->assertEquals('ogone', $corrective[0]->banque);
        $this->assertEquals('OGN5836106907', $corrective[0]->id_trans_banque);
        $this->assertEquals('CBS-COT', $corrective[0]->moyen);
        $this->assertEquals('euro', $corrective[0]->devise);
        $this->assertEquals('refuse', $corrective[0]->statut);
        $this->assertEquals('Import manuel (STATUS : 1)', $corrective[0]->statut_detail);
        $this->assertNull($corrective[0]->date_acceptation);
        $this->assertEquals($corrective[0]->date_creation, $corrective[0]->date_annulation);

        // Test on ogone response for accepted payment
        $variable = $this->getCustomerOrderPayment(2051933);
        $this->assertEquals('2051933', $variable['id_commande']);
        $this->assertEquals('64', $variable['id_paiement']);
        $this->assertEquals('2051933-4', $variable['creation_justificatif']);
        $this->assertEquals('accepte', $variable['auto_statut']);
        $this->assertEquals('Import manuel (STATUS : 5)', $variable['auto_statut_detail']);

        $corrective = $this->getPaymentCorrective(300);
        $this->assertCount(1, $corrective);
        $this->assertEquals('300', $corrective[0]->id_paiement);
        $this->assertEquals('ogone', $corrective[0]->banque);
        $this->assertEquals('OGN5837049670', $corrective[0]->id_trans_banque);
        $this->assertEquals('CBS-OT', $corrective[0]->moyen);
        $this->assertEquals('euro', $corrective[0]->devise);
        $this->assertEquals('accepte', $corrective[0]->statut);
        $this->assertEquals('Import manuel (STATUS : 5)', $corrective[0]->statut_detail);
        $this->assertNull($corrective[0]->date_annulation);
        $this->assertEquals($corrective[0]->date_creation, $corrective[0]->date_acceptation);

        // Test on ogone response for payment with existing corrective -> does not create a second one
        $corrective = $this->getPaymentCorrective(500);
        $this->assertEquals('500', $corrective[0]->id_paiement);
        $this->assertEquals('2000-01-01 00:00:00', $corrective[0]->date_correctif);

        // Test on ogone response for payment not in "paiements" database creates the required entries in corrective
        $corrective = $this->getPaymentCorrective(501);
        $this->assertEquals('501', $corrective[0]->id_paiement);
        $this->assertEquals(date('Y-m-d'), date('Y-m-d', strtotime($corrective[0]->date_correctif)));
    }

    /** Gets a customer order payment by ID. */
    protected function getCustomerOrderPayment(int $customer_order_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.paiement_commande
        WHERE id_commande = :customer_order_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['customer_order_id' => $customer_order_id]);
    }

    /** Gets a payment corrective by ID. */
    protected function getPaymentCorrective(int $payment_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM paiements.correctif
        WHERE id_paiement = :payment_id
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['payment_id' => $payment_id]);
    }
}
