<?php

namespace PHPUnit\Unit\Erp\System\Manager;

use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Customer\Contract\CustomerFromCmsInterface;
use SonVideo\Erp\System\Manager\FeatureStatus;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class FeatureStatusTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
        PgDatabase::loadSpecificFixtures(['system/feature_flags.sql']);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): FeatureStatus
    {
        return self::$container->get(FeatureStatus::class);
    }

    /** Tests that a feature is not active. */
    public function test_is_not_active(): void
    {
        $feature_status = $this->getTestedInstance();
        $this->assertFalse($feature_status->isActive('nope'));
    }

    /** Tests that a feature is active globally. */
    public function test_is_active_globally(): void
    {
        $feature_status = $this->getTestedInstance();
        $this->assertTrue($feature_status->isActive('my_feature:scope_1'));
    }

    /** Tests that a feature suffix can be activated with only a prefix. */
    public function test_feature_suffix_can_be_activated_with_only_a_prefix(): void
    {
        $feature_status = $this->getTestedInstance();
        $this->assertTrue($feature_status->isActive('another_feature'));
        $this->assertTrue($feature_status->isActive('another_feature:scoped'));
    }

    /** Tests that a suffixed feature only activates said feature, not its parent. */
    public function test_a_suffixed_feature_only_activate_said_feature_not_his_parent(): void
    {
        $feature_status = $this->getTestedInstance();
        $this->assertTrue($feature_status->isActive('my_feature:scope_1'));
        $this->assertFalse($feature_status->isActive('my_feature:scope_3'));
        $this->assertFalse($feature_status->isActive('my_feature'));
    }

    /** Tests the isActive method as a service. */
    public function test_is_active_method_as_a_service(): void
    {
        $manager_feature_flag = self::$container->get(FeatureStatus::class);
        $this->assertTrue($manager_feature_flag->isActive('my_feature:scope_1'));
        $this->assertTrue($manager_feature_flag->isActive('my_feature:scope_2'));
    }

    /** Tests that a feature is not active for a CMS customer. */
    public function test_is_not_active_for_cms_customer(): void
    {
        $feature_status = $this->getTestedInstance();
        $this->assertFalse($feature_status->isActiveForCustomer('yet_another_feature:scoped', 123));
    }

    /** Tests that a feature is active for a CMS customer. */
    public function test_is_active_for_cms_customer(): void
    {
        $customer_manager = new class() implements CustomerFromCmsInterface {
            public function getCmsAccountInformations(int $customer_id, array $fields = []): array
            {
                return ['preferences' => ['feature_flags' => ['yet_another_feature:scoped']]];
            }
        };

        $tested_instance = new FeatureStatus(
            self::$container->get(ParameterModel::class),
            $customer_manager,
            self::$container->get('logger')
        );

        $this->assertTrue($tested_instance->isActiveForCustomer('yet_another_feature:scoped', 123));
    }

    /** Tests that a feature is active with suffix only for a CMS customer. */
    public function test_is_active_with_suffix_only_for_cms_customer(): void
    {
        $customer_manager = new class() implements CustomerFromCmsInterface {
            public function getCmsAccountInformations(int $customer_id, array $fields = []): array
            {
                return ['preferences' => ['feature_flags' => ['yet_another_feature']]];
            }
        };

        $tested_instance = new FeatureStatus(
            self::$container->get(ParameterModel::class),
            $customer_manager,
            self::$container->get('logger')
        );

        $this->assertTrue($tested_instance->isActiveForCustomer('yet_another_feature:scoped', 123));
    }
}
