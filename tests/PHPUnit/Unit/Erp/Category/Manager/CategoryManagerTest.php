<?php

namespace PHPUnit\Unit\Erp\Category\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Category\Entity\CategoryEntity;
use SonVideo\Erp\Category\Exception\CategoryNameAlreadyExistException;
use SonVideo\Erp\Category\Manager\CategoryManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class CategoryManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'category/categories.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): CategoryManager
    {
        return self::$container->get(CategoryManager::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Gets the validator. */
    private function getValidator(): ValidatorInterface
    {
        return self::$container->get('validator');
    }

    /**
     * Tests the create method.
     *
     * @throws CategoryNameAlreadyExistException
     * @throws ExceptionInterface
     * @throws InternalErrorException
     * @throws NotFoundException
     */
    public function test_create(): void
    {
        // Initialize
        $domain_id = 13;
        $category_name = 'category test';
        $category_creator = $this->getTestedInstance();

        // Create category success case
        $new_category_id = $category_creator->create($domain_id, $category_name);
        $category = $this->fetchCategory($new_category_id);

        $this->assertEquals($new_category_id, (int) $category['id_categorie']);
        $this->assertEquals($category_name, $category['categorie']);
        $this->assertEquals($domain_id, (int) $category['dft_domaine_id']);

        $category_domain = $this->fetchCategoryDomain($new_category_id);
        $this->assertEquals($new_category_id, (int) $category_domain['id_categorie']);
        $this->assertEquals($domain_id, (int) $category_domain['id_domaine']);

        // Create category success with trim
        $category_name = '   nom de categorie   ';
        $new_category_id = $category_creator->create($domain_id, $category_name);
        $category = $this->fetchCategory($new_category_id);

        $this->assertEquals($new_category_id, (int) $category['id_categorie']);
        $this->assertEquals(trim($category_name), $category['categorie']);
        $this->assertEquals($domain_id, (int) $category['dft_domaine_id']);
    }

    /** Tests the create method with a name that already exists. */
    public function test_create_with_existing_name(): void
    {
        $domain_id = 13;
        $category_name = 'category test';
        $category_creator = $this->getTestedInstance();

        $this->expectException(CategoryNameAlreadyExistException::class);
        $this->expectExceptionMessageMatches('/Category name already exists/');
        $category_creator->create($domain_id, $category_name);
    }

    /** Tests the create method with a non-existent domain. */
    public function test_create_with_non_existent_domain(): void
    {
        $category_name = 'domain id doesnt exist';
        $category_creator = $this->getTestedInstance();

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessageMatches('/No domain found with id 666/');
        $category_creator->create(666, $category_name);
    }

    /**
     * Tests the update method.
     *
     * @throws ExceptionInterface
     */
    public function test_update(): void
    {
        $category_data_success = [
            'category_id' => 59,
            'name' => 'categorie test',
            'domain_id' => 13,
            'custom_code' => '666',
        ];

        $category_manager = $this->getTestedInstance();

        // Update category success case
        $category = $this->getSerializer()->denormalize($category_data_success, CategoryEntity::class);
        $category_manager->update($category);

        // Verify the update was successful
        $updated_category = $this->fetchCategory($category_data_success['category_id']);
        $this->assertEquals($category_data_success['name'], $updated_category['categorie']);
        $this->assertEquals($category_data_success['domain_id'], (int) $updated_category['dft_domaine_id']);
        $this->assertEquals($category_data_success['custom_code'], $updated_category['code_douanier']);
    }

    /**
     * Tests the update method with a non-existent category ID.
     *
     * @throws ExceptionInterface
     */
    public function test_update_with_non_existent_id(): void
    {
        $category_data_fail = [
            'category_id' => 54,
            'name' => 'categorie test',
            'domain_id' => 13,
            'custom_code' => '666',
        ];

        $category_manager = $this->getTestedInstance();
        $category = $this->getSerializer()->denormalize($category_data_fail, CategoryEntity::class);

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Category does not exist with id "54".');
        $category_manager->update($category);
    }

    /**
     * Tests the update method with a non-existent domain.
     *
     * @throws ExceptionInterface
     */
    public function test_update_with_non_existent_domain(): void
    {
        $category_data_fail = [
            'category_id' => 59,
            'name' => 'categorie test',
            'domain_id' => 666,
            'custom_code' => '666',
        ];

        $category_manager = $this->getTestedInstance();
        $category = $this->getSerializer()->denormalize($category_data_fail, CategoryEntity::class);

        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('No domain found with id 666');
        $category_manager->update($category);
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches a category by ID. */
    protected function fetchCategory(int $category_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.CTG_TXN_categorie
        WHERE id_categorie = :category_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['category_id' => $category_id]);
    }

    /** Fetches a category domain by category ID. */
    protected function fetchCategoryDomain(int $category_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.CTG_TXN_categorie_CTG_TXN_domaine
        WHERE id_categorie = :category_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['category_id' => $category_id]);
    }
}
