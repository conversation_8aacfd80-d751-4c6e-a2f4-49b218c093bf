<?php

namespace App\Tests\PHPUnit;

use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class TestCase extends KernelTestCase
{
    /**
     * Creates a mock object for the specified class.
     *
     * @template T of object
     *
     * @param class-string<T> $className
     *
     * @return T&MockObject
     */
    public function createMock(string $className): MockObject
    {
        return parent::createMock($className);
    }
}
