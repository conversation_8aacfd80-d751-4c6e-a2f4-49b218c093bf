<?php

namespace PHPUnit\EndToEnd\Api\AntiFraud;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class GetAntiFraudForCustomerOrderControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/anti_fraud.sql',
        ]);

        PgDatabase::reloadFixtures();
        PgDatabase::loadSpecificFixtures(['customer_order/anti_fraud.sql']);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request('GET', '/api/v1/anti-fraud/1');
        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request('GET', '/api/v1/anti-fraud/1', [], [], ['HTTP_AUTHORIZATION' => 'Bearer unknown-token']);
        $this->assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_token_linked_to_account_without_permission(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/anti-fraud/1',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function test_get_no_anti_fraud_for_specified_customer_order(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/anti-fraud/1',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals('success', $responseData['status']);

        $expectedMeta = [
            [
                'type' => 'brand',
                'id' => 263,
                'sku' => null,
                'name' => 'Arcam2',
            ],
            [
                'type' => 'brand',
                'id' => 264,
                'sku' => null,
                'name' => 'Arcam3',
            ],
            [
                'type' => 'subcategory',
                'id' => 259,
                'sku' => null,
                'name' => 'Récepteurs Bluetooth 2',
            ],
            [
                'type' => 'subcategory',
                'id' => 260,
                'sku' => null,
                'name' => 'Récepteurs Bluetooth 3',
            ],
            [
                'id' => null,
                'name' => 'Arcam rBlink 2',
                'sku' => 'ARCAMRBLINKNR2',
                'type' => 'sku',
            ],
            [
                'id' => null,
                'name' => 'Arcam rBlink 6',
                'sku' => 'BUT_SHOULD_BE_MERGED',
                'type' => 'sku',
            ],
            [
                'id' => null,
                'name' => 'Arcam rBlink 5',
                'sku' => 'IRRELEVANT',
                'type' => 'sku',
            ],
        ];

        $this->assertEquals($expectedMeta, $responseData['data']['meta']);

        $expectedStatuses = [
            'customer_order_id' => 1,
            'anti_fraud_customer_order' => null,
            'anti_fraud_customer_order_payments' => [],
        ];

        $this->assertEquals($expectedStatuses, $responseData['data']['statuses']);
    }

    public function test_get_some_anti_fraud_for_customer_order(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/anti-fraud/15',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals('success', $responseData['status']);
        $this->assertNotNull($responseData['data']['meta']);
        $this->assertEquals('15', $responseData['data']['statuses']['customer_order_id']);
        $this->assertNotNull($responseData['data']['statuses']['anti_fraud_customer_order']['created_at']);
        $this->assertEquals('REJECTED', $responseData['data']['statuses']['anti_fraud_customer_order']['status']);

        $expectedReason = [
            'details' => [
                [
                    'details' => [
                        'subcategory_id' => 259,
                    ],
                    'key' => 'INVALID_SUBCATEGORY',
                    'sku' => 'ARCAMRBLINKNR3',
                ],
                [
                    'details' => [
                        'subcategory_id' => 260,
                    ],
                    'key' => 'INVALID_SUBCATEGORY',
                    'sku' => 'ARCAMRBLINKNR4',
                ],
                [
                    'details' => [
                        'brand_id' => 263,
                    ],
                    'key' => 'INVALID_BRAND',
                    'sku' => 'IRRELEVANT',
                ],
                [
                    'details' => [
                        'brand_id' => 264,
                    ],
                    'key' => 'INVALID_BRAND',
                    'sku' => 'BUT_SHOULD_BE_MERGED',
                ],
                [
                    'key' => 'INVALID_SKU',
                    'sku' => 'ARCAMRBLINKNR2',
                ],
            ],
            'name' => 'HAS_INVALID_ARTICLES',
        ];

        $this->assertEquals($expectedReason, $responseData['data']['statuses']['anti_fraud_customer_order']['reason']);
        $this->assertEquals([], $responseData['data']['statuses']['anti_fraud_customer_order_payments']);
    }

    public function test_get_some_anti_fraud_for_customer_order_payments(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/anti-fraud/16',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals('success', $responseData['status']);
        $this->assertNotNull($responseData['data']['meta']);
        $this->assertEquals('16', $responseData['data']['statuses']['customer_order_id']);
        $this->assertNull($responseData['data']['statuses']['anti_fraud_customer_order']);
        $this->assertCount(1, $responseData['data']['statuses']['anti_fraud_customer_order_payments']);

        $payment = $responseData['data']['statuses']['anti_fraud_customer_order_payments'][0];
        $this->assertNotNull($payment['created_at']);
        $this->assertEquals('16-1', $payment['creation_proof']);
        $this->assertEquals('16', $payment['customer_order_payment_id']);
        $this->assertEquals('NON_ELIGIBLE', $payment['status']);
        $this->assertEquals(['name' => 'PAYMENT_NOT_SUPPORTED'], $payment['reason']);
    }
}
