<?php

namespace PHPUnit\EndToEnd\Api\Webhook\Hasura;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class EavAttributeControllerTest extends WebTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        // Load fixtures once for the entire test suite
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['sales_channel/sales_channels.sql', 'eav/base.sql']);

        PgDatabase::reloadFixtures();
        PgDatabase::loadSpecificFixtures(['eav/base.sql']);
    }

    public function test_attribute_update_event(): void
    {
        $client = static::createClient();

        // Set up RPC mock responses
        RpcClientServiceMock::savedResult(
            'bo-cms',
            'eav:update_facets',
            json_encode(
                [
                    'updated_subcategory_ids' => [56],
                    'updated_skus' => [],
                    'ignored_skus' => [],
                ],
                JSON_THROW_ON_ERROR
            )
        );

        RpcClientServiceMock::savedResult(
            'bo-cms',
            'eav:update_articles_attributes',
            json_encode(
                [
                    'updated_skus' => ['QEDQE1455'],
                    'ignored_skus' => [],
                ],
                JSON_THROW_ON_ERROR
            )
        );

        // Send request
        $client->request(
            'POST',
            '/api/v1/webhook/hasura',
            [],
            [],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_X_WEBHOOK_AUTH_SECRET' => '123soleil',
            ],
            json_encode(
                [
                    'event' => [
                        'session_variables' => [
                            'x-hasura-role' => 'user',
                            'x-hasura-user-id' => 'f8284275-b1c4-42c0-b37b-639335ef730d',
                        ],
                        'op' => 'UPDATE',
                        'data' => [
                            'old' => [
                                'definition' => [
                                    'type' => 'text',
                                ],
                                'name' => 'Section cable enceinte',
                                'meta' => [
                                    'suffix' => null,
                                    'prefix' => null,
                                    'unit' => null,
                                    'label' => 'Section du conducteur',
                                ],
                                'attribute_id' => 701,
                                'i18n' => null,
                            ],
                            'new' => [
                                'definition' => [
                                    'type' => 'text',
                                ],
                                'name' => 'Section cable enceinte',
                                'meta' => [
                                    'suffix' => null,
                                    'prefix' => null,
                                    'unit' => null,
                                    'label' => 'Section du truc là',
                                ],
                                'attribute_id' => 701,
                                'i18n' => null,
                            ],
                        ],
                    ],
                    'created_at' => '2020-10-28T13:13:57.765301',
                    'id' => '37790ee9-d076-48b6-93be-3ee5c4f842a2',
                    'delivery_info' => [
                        'max_retries' => 0,
                        'current_retry' => 0,
                    ],
                    'trigger' => [
                        'name' => 'eav_attribute',
                    ],
                    'table' => [
                        'schema' => 'eav',
                        'name' => 'attribute',
                    ],
                ],
                JSON_THROW_ON_ERROR
            )
        );

        // Assert response status code
        self::assertEquals(Response::HTTP_OK, $client->getResponse()->getStatusCode());

        // Get response content
        $responseContent = json_decode($client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);

        // Assert response content
        self::assertEquals('success', $responseContent['status']);

        // Assert RPC calls for subcategories facets
        $updateFacetsCall = RpcClientServiceMock::getLastCall('bo-cms', 'eav:update_facets');
        self::assertCount(1, $updateFacetsCall['args'][0]);
        self::assertEquals(56, $updateFacetsCall['args'][0][0]['subcategory_id']);
        self::assertCount(3, $updateFacetsCall['args'][0][0]['eav_facet_definitions']);
        self::assertArrayHasKey('attribute_id_162', $updateFacetsCall['args'][0][0]['eav_facet_definitions']);
        self::assertArrayHasKey('attribute_id_887', $updateFacetsCall['args'][0][0]['eav_facet_definitions']);
        self::assertArrayHasKey('attribute_id_922', $updateFacetsCall['args'][0][0]['eav_facet_definitions']);

        // Assert RPC calls for articles facets
        self::assertCount(0, $updateFacetsCall['args'][1]);

        // Assert RPC calls for attributes updates
        $updateArticlesAttributesCall = RpcClientServiceMock::getLastCall('bo-cms', 'eav:update_articles_attributes');
        self::assertCount(1, $updateArticlesAttributesCall['args'][0]);
        self::assertEquals('QEDQE1455', $updateArticlesAttributesCall['args'][0][0]['sku']);
        self::assertCount(5, $updateArticlesAttributesCall['args'][0][0]['eav_attributes']);

        // Assert response data
        self::assertArrayHasKey('data', $responseContent);
        self::assertArrayHasKey('facets', $responseContent['data']);
        self::assertArrayHasKey('updated_subcategory_ids', $responseContent['data']['facets']);
        self::assertArrayHasKey('updated_skus', $responseContent['data']['facets']);
        self::assertArrayHasKey('ignored_skus', $responseContent['data']['facets']);
        self::assertArrayHasKey('attributes', $responseContent['data']);
        self::assertArrayHasKey('updated_skus', $responseContent['data']['attributes'][0]);
        self::assertArrayHasKey('ignored_skus', $responseContent['data']['attributes'][0]);
        self::assertEquals('QEDQE1455', $responseContent['data']['attributes'][0]['updated_skus'][0]);
    }
}
