<?php

namespace PHPUnit\EndToEnd\Api\Wms\Warehouse;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class PutWarehouseCommentControllerTest extends WebTestCase
{
    private \Symfony\Bundle\FrameworkBundle\KernelBrowser $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'warehouses.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    /** @test */
    public function it_fails_with_no_token(): void
    {
        $this->client->request(
            'PUT',
            '/api/v2/wms/warehouse/12/comment',
            [],
            [],
            [
                'CONTENT_TYPE' => 'application/json',
            ],
            json_encode(['comment' => 'Test commentaire'])
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    /** @test */
    public function it_fails_with_invalid_token(): void
    {
        $this->client->request(
            'PUT',
            '/api/v2/wms/warehouse/12/comment',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer unknown-token',
                'CONTENT_TYPE' => 'application/json',
            ],
            json_encode(['comment' => 'Test commentaire'])
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    /** @test */
    public function it_fails_with_invalid_payload(): void
    {
        $this->client->request(
            'PUT',
            '/api/v2/wms/warehouse/12/comment',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'CONTENT_TYPE' => 'application/json',
            ],
            json_encode(['comment' => 123])
        ); // not a string

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        $response = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('Le paramètre comment est requis et doit être une string.', $response['message'] ?? '');
    }

    /** @test */
    public function it_fails_if_warehouse_not_found(): void
    {
        $this->client->request(
            'PUT',
            '/api/v2/wms/warehouse/9999/comment',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'CONTENT_TYPE' => 'application/json',
            ],
            json_encode(['comment' => 'Test commentaire'])
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $response = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('Warehouse 9999 not found', $response['message'] ?? '');
    }

    /** @test */
    public function it_successfully_updates_the_comment(): void
    {
        $this->client->request(
            'PUT',
            '/api/v2/wms/warehouse/12/comment',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'CONTENT_TYPE' => 'application/json',
            ],
            json_encode(['comment' => 'Commentaire mis à jour E2E'])
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertEmpty($this->client->getResponse()->getContent());
    }
}
