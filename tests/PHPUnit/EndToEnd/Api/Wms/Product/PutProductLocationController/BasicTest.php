<?php

namespace App\Tests\PHPUnit\EndToEnd\Api\Wms\Product\PutProductLocationController;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\BasicE2eJsonRequest;
use App\Tests\Utils\PHPUnit\EndToEnd\BearerReferential;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class BasicTest extends WebTestCase
{
    private $client;
    private BasicE2eJsonRequest $request_helper;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'locations/put_location_products.sql',
        ]);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
        $this->request_helper = BasicE2eJsonRequest::with($this->client);
    }

    public function test_without_authorization(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81078/location/1',
            <<<JSON
            {
              "quantity": 2,
              "comment": "produit non trouvé"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => '']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81078/location/1',
            <<<JSON
            {
              "quantity": 2,
              "comment": "produit non trouvé"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::UNKNOWN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_token_linked_to_account_without_permission(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81078/location/1',
            <<<JSON
            {
              "quantity": 2,
              "comment": "produit non trouvé"
            }
            JSON
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
    }

    public function test_failed_on_negative_quantity(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81078/location/1',
            <<<JSON
            {
              "quantity": -1,
              "comment": "produit non trouvé"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    public function test_failed_if_comment_is_too_short(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81078/location/1',
            <<<JSON
            {
              "quantity": 2,
              "comment": "pouet"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    public function test_failed_if_quantity_below_unused_quantity(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81078/location/1',
            <<<JSON
            {
              "quantity": 2,
              "comment": "produit non trouvé"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals(1003, $response_content['code']);
        self::assertCount(3, $response_content['data']);
        self::assertEquals(
            'Corrected quantity does not cover locked quantity, should be higher than "4".',
            $response_content['message']
        );
        self::assertEquals(-11, $response_content['data']['computed_diff_quantity']);
        self::assertEquals(9, $response_content['data']['computed_unused_quantities']);
        self::assertEquals(4, $response_content['data']['computed_locked_quantities']);
    }

    public function test_do_correction_on_usable_products(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81078/location/1',
            <<<JSON
            {
              "quantity": 10,
              "comment": "produit non trouvé"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('success', $response_content['status']);
        self::assertCount(3, $response_content['data']['quantities']);
        self::assertNull($response_content['data']['quantities'][0]['delivery_ticket_id']);
        self::assertNull($response_content['data']['quantities'][0]['move_mission_id']);
        self::assertEquals(6, $response_content['data']['quantities'][0]['quantity']);
        self::assertNull($response_content['data']['quantities'][1]['delivery_ticket_id']);
        self::assertEquals(1, $response_content['data']['quantities'][1]['move_mission_id']);
        self::assertEquals(1, $response_content['data']['quantities'][1]['quantity']);
        self::assertEquals(4263254, $response_content['data']['quantities'][2]['delivery_ticket_id']);
        self::assertNull($response_content['data']['quantities'][2]['move_mission_id']);
        self::assertEquals(3, $response_content['data']['quantities'][2]['quantity']);
    }

    public function test_do_correction_on_another_warehouse(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81078/location/3',
            <<<JSON
            {
              "quantity": 10,
              "comment": "finalement on a plein de trucs ici"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('success', $response_content['status']);
        self::assertCount(1, $response_content['data']['quantities']);
        self::assertNull($response_content['data']['quantities'][0]['delivery_ticket_id']);
        self::assertNull($response_content['data']['quantities'][0]['move_mission_id']);
        self::assertEquals(10, $response_content['data']['quantities'][0]['quantity']);
    }

    public function test_correction_on_stock_b_product_cannot_be_greater_than1(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/1/location/3',
            <<<JSON
            {
              "quantity": 2,
              "comment": "finalement on a plein de trucs ici"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals(1001, $response_content['code']);
        self::assertCount(0, $response_content['data']);
        self::assertEquals('Too much quantity for product in stock B', $response_content['message']);
    }

    public function test_correction_on_stock_b_product_cannot_be_greater_than1_on_entire_database(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/1/location/3',
            <<<JSON
            {
              "quantity": 1,
              "comment": "finalement on a plein de trucs ici"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals(1002, $response_content['code']);
        self::assertCount(0, $response_content['data']);
        self::assertEquals('Stock B already exists for this product', $response_content['message']);
    }

    public function test_failed_if_total_quantity_drops_under_delivery_notes_requirements(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/143088/location/1',
            <<<JSON
            {
              "quantity": 1,
              "comment": "oups, il manque des produits ici"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals(1004, $response_content['code']);
        self::assertCount(4, $response_content['data']);
        self::assertEquals(
            'Corrected quantity does not cover stock needs for the following delivery notes: "1111111"',
            $response_content['message']
        );
        self::assertEquals(3, $response_content['data']['delivery_needs']);
        self::assertEquals(3, $response_content['data']['qty_in_warehouse']);
        self::assertEquals('1111111', $response_content['data']['delivery_notes']);
        self::assertEquals(-1, $response_content['data']['computed_diff_quantity']);
    }
}
