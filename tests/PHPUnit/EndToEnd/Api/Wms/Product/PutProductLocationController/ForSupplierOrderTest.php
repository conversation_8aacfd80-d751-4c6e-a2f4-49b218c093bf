<?php

namespace App\Tests\PHPUnit\EndToEnd\Api\Wms\Product\PutProductLocationController;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\BasicE2eJsonRequest;
use App\Tests\Utils\PHPUnit\EndToEnd\BearerReferential;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class ForSupplierOrderTest extends WebTestCase
{
    private $client;
    private BasicE2eJsonRequest $request_helper;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql']);
        MySqlDatabase::loadSpecificFixtures([
            'sales_channel/sales_channels.sql',
            'locations/put_location_products.sql',
            'wms/product/put_product_location_with_supplier_order.sql',
        ]);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
        $this->request_helper = BasicE2eJsonRequest::with($this->client);
    }

    public function test_fail_to_correct_stock_linked_to_supplier_order_that_does_not_exist(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81079/location/1',
            <<<JSON
            {
              "quantity": 1,
              "comment": "DUMMY_COMMENT_OVERRIDDEN",
              "supplier_order_id": 666
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals('1000', $response_content['code']);
        self::assertEquals(
            'Le produit "81079" n\'a pas été trouvé dans la commande fournisseur "666"',
            $response_content['message']
        );
    }

    public function test_fail_to_correct_stock_linked_to_supplier_order_because_of_order_status(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81079/location/1',
            <<<JSON
            {
              "quantity": 1,
              "comment": "DUMMY_COMMENT_OVERRIDDEN",
              "supplier_order_id": 1
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals('1000', $response_content['code']);
        self::assertEquals(
            'La mise à jour de la quantité livrée n\'est pas autorisée sur une commande fournisseur en statut: "annulee"',
            $response_content['message']
        );
    }

    public function test_fail_to_correct_stock_because_would_exceed_ordered_quantity(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81079/location/1',
            <<<JSON
            {
              "quantity": 3,
              "comment": "DUMMY_COMMENT_OVERRIDDEN",
              "supplier_order_id": 3
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals('1000', $response_content['code']);
        self::assertEquals(
            'La quantité livrée sur la commande fournisseur "1 (quantité livrée) + 1 (correction)" ne peut pas excéder la quantité commandée "1"',
            $response_content['message']
        );
    }

    public function test_fail_to_correct_stock_because_delivered_quantity_would_be_negative(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81079/location/1',
            <<<JSON
            {
              "quantity": 0,
              "comment": "DUMMY_COMMENT_OVERRIDDEN",
              "supplier_order_id": 3
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals('1000', $response_content['code']);
        self::assertEquals(
            'La quantité livrée sur la commande fournisseur "1 (quantité livrée) -2 (correction)" ne peut pas être négative',
            $response_content['message']
        );
    }

    public function test_fail_to_correct_stock_because_does_not_contain_given_product(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81079/location/1',
            <<<JSON
            {
              "quantity": 0,
              "comment": "DUMMY_COMMENT_OVERRIDDEN",
              "supplier_order_id": 2
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals('1000', $response_content['code']);
        self::assertEquals(
            'Le produit "81079" n\'a pas été trouvé dans la commande fournisseur "2"',
            $response_content['message']
        );
    }

    public function test_successful_correction_with_supplier_order(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/81080/location/1',
            <<<JSON
            {
              "quantity": 0,
              "comment": "DUMMY_COMMENT_OVERRIDDEN",
              "supplier_order_id": 2
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('success', $response_content['status']);
        self::assertCount(0, $response_content['data']['quantities']);
    }
}
