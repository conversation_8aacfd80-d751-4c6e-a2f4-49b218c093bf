<?php

namespace App\Tests\PHPUnit\EndToEnd\Api\Wms\Product\PutProductLocationController;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\BasicE2eJsonRequest;
use App\Tests\Utils\PHPUnit\EndToEnd\BearerReferential;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class ForAutoPickingTest extends WebTestCase
{
    private $client;
    private BasicE2eJsonRequest $request_helper;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql']);
        MySqlDatabase::loadSpecificFixtures([
            'sales_channel/sales_channels.sql',
            'locations/put_location_products.sql',
            'wms/product/put_product_location_with_supplier_order.sql',
        ]);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
        $this->request_helper = BasicE2eJsonRequest::with($this->client);
    }

    public function test_correction_on_auto_picked_product_fails_if_target_location_not_virtual(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/139789/location/1',
            <<<JSON
            {
              "quantity": 2,
              "comment": "produit non trouvé"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('error', $response_content['status']);
        self::assertEquals(1005, $response_content['code']);
        self::assertEquals(
            '"1" is not an appropriate location for an auto picked product',
            $response_content['message']
        );
    }

    public function test_correction_on_auto_picked_product_succeeds_if_target_location_is_virtual(): void
    {
        $this->request_helper->put(
            '/api/v1/wms/product/139789/location/3',
            <<<JSON
            {
              "quantity": 2,
              "comment": "produit non trouvé"
            }
            JSON
            ,
            ['HTTP_AUTHORIZATION' => BearerReferential::ADMIN_TOKEN]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertIsArray($response_content);
        self::assertEquals('success', $response_content['status']);
    }
}
