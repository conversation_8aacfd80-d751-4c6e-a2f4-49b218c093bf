<?php

namespace PHPUnit\EndToEnd\Api\Account;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class CGetAccountSellerCommissionInfoControllerTest extends WebTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        // Load fixtures
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql']);

        PgDatabase::reloadFixtures();
    }

    public function test_without_authorization(): void
    {
        $client = static::createClient();

        // Send request without authorization
        $client->request('GET', '/api/v1/account/seller-commission-infos');

        // Assert response
        $this->assertEquals(401, $client->getResponse()->getStatusCode());
    }

    public function test_with_non_valid_authorization(): void
    {
        $client = static::createClient();

        // Send request with invalid token
        $client->request(
            'GET',
            '/api/v1/account/seller-commission-infos',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer unknown-token']
        );

        // Assert response
        $this->assertEquals(401, $client->getResponse()->getStatusCode());
    }

    public function test_retrieve_seller_commission_infos(): void
    {
        $client = static::createClient();

        // Send request with valid token
        $client->request(
            'GET',
            '/api/v1/account/seller-commission-infos',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );

        // Assert response
        $this->assertEquals(200, $client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        // Get response content
        $responseContent = json_decode($client->getResponse()->getContent(), true);

        // Assert JSON structure
        $this->assertEquals('success', $responseContent['status']);

        // Assert roles
        $expectedRoles = [
            'RETAIL_STORE_SELLER',
            'RETAIL_STORE_MANAGER',
            'CALL_CENTER_SELLER',
            'CALL_CENTER_MANAGER',
            'BUSINESS_DEPARTMENT_SELLER',
        ];
        $this->assertEquals($expectedRoles, $responseContent['data']['roles']);

        // Assert role levels
        $expectedRoleLevels = ['1', '2', '3', '4'];
        $this->assertEquals($expectedRoleLevels, $responseContent['data']['role_levels']);
    }
}
