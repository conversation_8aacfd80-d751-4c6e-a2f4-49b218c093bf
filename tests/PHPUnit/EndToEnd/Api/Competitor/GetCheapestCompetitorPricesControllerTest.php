<?php

namespace PHPUnit\EndToEnd\Api\Competitor;

use App\Tests\Utils\Database\MySqlDatabase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class GetCheapestCompetitorPricesControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Load fixtures based on the @clear-database tag in the first scenario
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'competitor/competitor_pricing.sql']);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/article/987654/cheapest-competitor-prices',
            [],
            [],
            ['HTTP_CONTENT_TYPE' => 'application/json']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/article/987654/cheapest-competitor-prices',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer unknown-token',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_token_linked_to_account_without_permission(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/article/987654/cheapest-competitor-prices',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('Content-Type', 'application/json');
    }

    public function test_check_successful_response(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/article/81123/cheapest-competitor-prices',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals('success', $response_data['status']);
        self::assertCount(5, $response_data['data']['competitor_prices']);

        // Verify the second competitor price entry matches the expected data
        $competitor_price = $response_data['data']['competitor_prices'][0];
        $expected_competitor_price = [
            'sku' => 'BOSEQCULTRABC',
            'ean' => null,
            'competitor_code' => 'BOULANGER',
            'selling_price_with_taxes' => 399.99,
            'shipping_price_with_taxes' => 400.99,
            'shipping_delay_in_days' => 2,
            'is_available' => true,
            'site' => 'BOULANGER',
            'url' => 'https://www.boulanger.com/ref/1195442',
            'created_at' => '2025-02-24 10:22:46',
            'updated_at' => '2025-02-24 10:22:46',
            'crawled_at' => '2025-02-24 10:22:46',
            'is_excluded' => true,
        ];

        self::assertEquals($expected_competitor_price, $competitor_price);

        // Verify the second competitor price entry matches the expected data
        $competitor_price = $response_data['data']['competitor_prices'][1];
        $expected_competitor_price = [
            'sku' => 'BOSEQCULTRABC',
            'ean' => null,
            'competitor_code' => 'DARTY',
            'selling_price_with_taxes' => 399.99,
            'shipping_price_with_taxes' => 400.99,
            'shipping_delay_in_days' => 2,
            'is_available' => true,
            'site' => 'DARTY',
            'url' => 'https://www.darty.com/nav/achat/accessoires/casque_ecouteurs/casque_arceau/bose_qc_ultra_blanc.html',
            'created_at' => '2025-02-24 10:22:46',
            'updated_at' => '2025-02-24 10:22:46',
            'crawled_at' => '2025-02-19 04:33:35',
            'is_excluded' => true,
        ];

        self::assertEquals($expected_competitor_price, $competitor_price);
    }
}
