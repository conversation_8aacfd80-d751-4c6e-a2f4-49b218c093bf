<?php

namespace PHPUnit\EndToEnd\CustomerOrder;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\ArrayHelper;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class GetCustomerOrderForEditionPageControllerTest extends WebTestCase
{
    protected $client;

    public static function setUpBeforeClass(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql', 'customer_order.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request('GET', '/api/v1/customer-order/1/for-edition-page');

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $this->client->getResponse()->getStatusCode());
    }

    public function test_with_a_non_valid_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/customer-order/1/for-edition-page',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer unknown-token']
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $this->client->getResponse()->getStatusCode());
    }

    public function test_load_information_on_a_customer_order(): void
    {
        RpcClientServiceMock::savedResult(
            'bridge',
            'carrier:get_carriers_for_order',
            '[{"shipment_method_id": 1, "cost": 9.9}]'
        );

        $this->client->request(
            'GET',
            '/api/v1/customer-order/1/for-edition-page',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('success', $content['status']);
        $this->assertEquals('1', $content['data']['customer_order_id']);
        $this->assertFalse($content['data']['has_inconsistent_carrier']);
    }

    public function test_load_information_on_a_customer_order_with_an_inconsistent_shipment_method_id(): void
    {
        RpcClientServiceMock::savedResult(
            'bridge',
            'carrier:get_carriers_for_order',
            '[{"shipment_method_id": 11, "cost": 9.9}, {"shipment_method_id": 22, "cost": 9.9}]'
        );

        $this->client->request(
            'GET',
            '/api/v1/customer-order/1/for-edition-page',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('success', $content['status']);
        $this->assertEquals('1', $content['data']['customer_order_id']);
        $this->assertTrue($content['data']['has_inconsistent_carrier']);
    }

    public function test_load_information_on_an_amazon_business_and_duty_free_customer_order(): void
    {
        RpcClientServiceMock::savedResult(
            'bridge',
            'carrier:get_carriers_for_order',
            '[{"shipment_method_id": 1, "cost": 9.9}]'
        );

        $this->client->request(
            'GET',
            '/api/v1/customer-order/3/for-edition-page',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals('success', $content['status']);

        $expected_tag = [
            'meta' => [
                'HT' => true,
            ],
            'name' => 'amazon_business',
            'label' => 'Amazon Business',
            'taxonomy_meta' => null,
        ];

        $this->assertEquals(
            $expected_tag,
            ArrayHelper::removeKeysFrom($content['data']['tags'][0], ['modified_at', 'remitted_at'])
        );
    }
}
