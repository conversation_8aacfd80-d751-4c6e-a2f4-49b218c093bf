<?php

namespace PHPUnit\EndToEnd\Api\Quote;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class GetQuoteEventsControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/get_quote_events.sql',
        ]);

        PgDatabase::reloadFixtures();
        PgDatabase::loadSpecificFixtures(['quote/events.sql']);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request('GET', '/api/v1/quote/13/events');
        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/quote/13/events',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer unknown-token']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_on_non_existing_quote(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/quote/999/events',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals('Quote not found with id "999"', $responseData['message']);
    }

    public function test_success_with_pg_events(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/quote/14/events',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals('success', $responseData['status']);
        self::assertCount(1, $responseData['data']['system_events']);

        $firstEvent = $responseData['data']['system_events'][0];
        self::assertEquals('590683ca-6348-44d9-9af3-e41b28b7807a', $firstEvent['id_for_type']);
        self::assertEquals('2022-03-22 17:44:12', $firstEvent['created_at']);
        self::assertEquals('email.quote', $firstEvent['type']);
        self::assertIsArray($firstEvent['payload']);
        self::assertNull($firstEvent['main_id']);
        self::assertIsArray($firstEvent['emitter']);

        self::assertEquals('svd', $firstEvent['emitter']['type']);
        self::assertEquals(1, $firstEvent['emitter']['data']['user_id']);
    }

    public function test_success_with_mysql_events(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/quote/15/events',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals('success', $responseData['status']);
        self::assertCount(8, $responseData['data']['system_events']);

        $firstEvent = $responseData['data']['system_events'][0];
        self::assertEquals('531', $firstEvent['id_for_type']);
        self::assertEquals('2023-03-25 10:41:46', $firstEvent['created_at']);
        self::assertEquals('quote_line_product.update', $firstEvent['type']);
        self::assertIsArray($firstEvent['payload']);
        self::assertEquals(15, $firstEvent['main_id']);
        self::assertNull($firstEvent['emitter']);

        $secondEvent = $responseData['data']['system_events'][1];
        self::assertEquals('530', $secondEvent['id_for_type']);
        self::assertEquals('2022-03-25 10:41:41', $secondEvent['created_at']);
        self::assertEquals('quote_line_product.update', $secondEvent['type']);
        self::assertIsArray($secondEvent['payload']);
        self::assertEquals(15, $secondEvent['main_id']);
        self::assertNull($secondEvent['emitter']);

        self::assertArrayHasKey('_rel', $secondEvent['payload']);
        self::assertArrayHasKey('data', $secondEvent['payload']);
        self::assertArrayHasKey('meta', $secondEvent['payload']);
    }

    public function test_success_with_pg_and_mysql_events(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/quote/13/events',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals('success', $responseData['status']);
        self::assertCount(6, $responseData['data']['system_events']);

        $firstEvent = $responseData['data']['system_events'][0];
        self::assertEquals('436c81c6-e8b9-4823-80b3-76d081a6dabc', $firstEvent['id_for_type']);
        self::assertEquals('2024-03-22 17:36:43', $firstEvent['created_at']);
        self::assertEquals('email.quote', $firstEvent['type']);
        self::assertIsArray($firstEvent['payload']);
        self::assertNull($firstEvent['main_id']);
        self::assertIsArray($firstEvent['emitter']);

        $secondEvent = $responseData['data']['system_events'][1];
        self::assertEquals('455', $secondEvent['id_for_type']);
        self::assertEquals('2023-03-22 14:16:52', $secondEvent['created_at']);
        self::assertEquals('quote.clone', $secondEvent['type']);
        self::assertIsArray($secondEvent['payload']);
        self::assertEquals(13, $secondEvent['main_id']);
        self::assertNull($secondEvent['emitter']);
    }

    public function test_success_with_pg_and_mysql_events_for_type_all(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/quote/13/events?type=all',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals('success', $responseData['status']);
        self::assertCount(6, $responseData['data']['system_events']);

        $firstEvent = $responseData['data']['system_events'][0];
        self::assertEquals('436c81c6-e8b9-4823-80b3-76d081a6dabc', $firstEvent['id_for_type']);
        self::assertEquals('2024-03-22 17:36:43', $firstEvent['created_at']);
        self::assertEquals('email.quote', $firstEvent['type']);

        $secondEvent = $responseData['data']['system_events'][1];
        self::assertEquals('455', $secondEvent['id_for_type']);
        self::assertEquals('2023-03-22 14:16:52', $secondEvent['created_at']);
        self::assertEquals('quote.clone', $secondEvent['type']);
    }

    public function test_success_with_pg_and_mysql_events_for_type_comment(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/quote/13/events?type=comment',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals('success', $responseData['status']);
        self::assertCount(1, $responseData['data']['system_events']);

        $firstEvent = $responseData['data']['system_events'][0];
        self::assertEquals('436c81c6-e8b9-4823-80b3-76d081a6dabc', $firstEvent['id_for_type']);
        self::assertEquals('2024-03-22 17:36:43', $firstEvent['created_at']);
        self::assertEquals('email.quote', $firstEvent['type']);
    }

    public function test_success_with_pg_and_mysql_events_for_type_history(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/quote/13/events?type=history',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals('success', $responseData['status']);
        self::assertCount(5, $responseData['data']['system_events']);

        $firstEvent = $responseData['data']['system_events'][0];
        self::assertEquals('455', $firstEvent['id_for_type']);
        self::assertEquals('2023-03-22 14:16:52', $firstEvent['created_at']);
        self::assertEquals('quote.clone', $firstEvent['type']);

        $secondEvent = $responseData['data']['system_events'][1];
        self::assertEquals('535', $secondEvent['id_for_type']);
        self::assertEquals('2022-03-25 17:41:46', $secondEvent['created_at']);
        self::assertEquals('quote_line_product.update', $secondEvent['type']);
    }
}
