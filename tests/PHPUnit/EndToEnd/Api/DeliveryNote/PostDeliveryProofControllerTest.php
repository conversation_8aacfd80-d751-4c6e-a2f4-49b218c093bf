<?php

namespace PHPUnit\EndToEnd\Api\DeliveryNote;

use App\Tests\Utils\Database\MySqlDatabase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;

class PostDeliveryProofControllerTest extends WebTestCase
{
    private $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql']);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $file_path = self::$container->getParameter('kernel.project_dir') . '/tests/fixtures/files/test.jpg';
        $file = new UploadedFile($file_path, 'test.jpg', 'image/jpeg', null, true);

        $this->client->request(
            'POST',
            '/api/v1/delivery-note/1234/proof',
            [
                'warehouse_id' => 5,
                'created_at' => **********,
            ],
            [
                'picture' => $file,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $file_path = self::$container->getParameter('kernel.project_dir') . '/tests/fixtures/files/test.jpg';
        $file = new UploadedFile($file_path, 'test.jpg', 'image/jpeg', null, true);

        $this->client->request(
            'POST',
            '/api/v1/delivery-note/1234/proof',
            [
                'warehouse_id' => 5,
                'created_at' => **********,
            ],
            [
                'picture' => $file,
            ],
            [
                'HTTP_AUTHORIZATION' => 'Bearer unknown-token',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_ok_with_token_linked_to_account_without_permission(): void
    {
        $file_path = self::$container->getParameter('kernel.project_dir') . '/tests/fixtures/files/test.jpg';
        $file = new UploadedFile($file_path, 'test.jpg', 'image/jpeg', null, true);

        $this->client->request(
            'POST',
            '/api/v1/delivery-note/1234/proof',
            [
                'warehouse_id' => 5,
                'created_at' => **********,
            ],
            [
                'picture' => $file,
            ],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        $expected_response = [
            'status' => 'success',
            'data' => [
                'filename' => 'PreuveRetrait5-1234-**********.jpeg',
            ],
        ];

        self::assertEquals($expected_response, $response_data);
    }
}
