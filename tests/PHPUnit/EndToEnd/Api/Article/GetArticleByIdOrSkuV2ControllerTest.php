<?php

namespace PHPUnit\EndToEnd\Api\Article;

use App\Tests\Utils\ArrayHelper;
use App\Tests\Utils\Database\MySqlDatabase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class GetArticleByIdOrSkuV2ControllerTest extends WebTestCase
{
    protected $client;

    public static function setUpBeforeClass(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/get_article_by_id_or_sku_v2.sql',
        ]);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request('GET', '/api/v2/article/123');

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $this->client->getResponse()->getStatusCode());
    }

    public function test_with_a_non_valid_authorization(): void
    {
        $this->client->request('GET', '/api/v2/article/123', [], [], ['HTTP_AUTHORIZATION' => 'Bearer unknown-token']);

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $this->client->getResponse()->getStatusCode());
    }

    public function test_with_non_existing_article(): void
    {
        $this->client->request(
            'GET',
            '/api/v2/article/666',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertEquals(404, $content['code']);
        $this->assertEquals('error', $content['status']);
        $this->assertEquals('Article not found with id or sku "666".', $content['message']);
    }

    public function test_retrieve_an_article_by_its_id(): void
    {
        $this->client->request(
            'GET',
            '/api/v2/article/81123',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $content = json_decode($response->getContent(), true);
        $expected_data = [
            'article_id' => 81123,
            'sku' => 'LBCLD25BP',
            'created_at' => '2013-02-21',
            'last_log_date' => '2018-10-09 16:57:13',
            'status' => 'oui',
            'unbasketable_reason' => null,
            'stock' => 10,
            'delay' => 0,
            'article_url' => null,
            'is_package' => false,
            'is_destock' => false,
            'is_on_sale' => false,
            'name' => 'Pieds Noir laqué pour station HiFi LD120 / LD130',
            'basket_description' => 'Paire de pieds noirs laqués pour station multimédia La Boîte Concept LD120 et LD130',
            'short_description' => 'Pieds noirs laqués pour La Boîte Concept LD120 / LD130 (la paire)',
            'marketplace_description' => null,
            'embargo_date' => null,
            'manufacturer_warranty_years' => 2,
            'has_warranty_extension_5_years' => false,
            'warranty_extension_5_years_price' => null,
            'packages' => 2,
            'image' => '/images/ui/uiV3/graphics/no-img-300.png',
            'eans' => ['5060133602194', '5036694041987', '0636926014106'],
            'comparator' => 'oui',
            'sales_channels' => [
                [
                    'sales_channel_id' => 1,
                    'sales_channel' => 'son-video.com',
                    'minimum_margin_rate' => 0,
                    'average_commission_rate' => 11.2,
                    'selling_price' => 389,
                    'can_sell_on_sales_channel' => true,
                    'selling_price_tax_excluded' => 324.1666666666667,
                    'margin' => 164.41000000000003,
                    'margin_rate' => 0.5071773778920309,
                    'markup_rate' => 1.3317942486836778,
                    'sales_channel_commission' => 36.306666666666665,
                    'errors' => null,
                    'pricing_strategy_ids' => [],
                ],
                [
                    'sales_channel_id' => 3,
                    'sales_channel' => 'easylounge.com',
                    'minimum_margin_rate' => 7,
                    'average_commission_rate' => 20,
                    'selling_price' => 279,
                    'can_sell_on_sales_channel' => true,
                    'selling_price_tax_excluded' => 232.5,
                    'margin' => 62.55,
                    'margin_rate' => 0.26903225806451614,
                    'markup_rate' => 0.5066828675577156,
                    'sales_channel_commission' => 46.5,
                    'errors' => null,
                    'pricing_strategy_ids' => [],
                ],
                [
                    'sales_channel_id' => 9,
                    'sales_channel' => 'cdiscount',
                    'minimum_margin_rate' => 12,
                    'average_commission_rate' => 11.2,
                    'selling_price' => 279,
                    'can_sell_on_sales_channel' => true,
                    'selling_price_tax_excluded' => 232.5,
                    'margin' => 83.01,
                    'margin_rate' => 0.35703225806451616,
                    'markup_rate' => 0.6724179829890644,
                    'sales_channel_commission' => 26.039999999999996,
                    'errors' => null,
                    'pricing_strategy_ids' => [],
                ],
                [
                    'sales_channel_id' => 10,
                    'sales_channel' => 'darty',
                    'minimum_margin_rate' => 7,
                    'average_commission_rate' => 22,
                    'selling_price' => 279,
                    'can_sell_on_sales_channel' => true,
                    'selling_price_tax_excluded' => 232.5,
                    'margin' => 57.89999999999999,
                    'margin_rate' => 0.2490322580645161,
                    'markup_rate' => 0.4690157958687727,
                    'sales_channel_commission' => 51.15,
                    'errors' => null,
                    'pricing_strategy_ids' => [],
                ],
                [
                    'sales_channel_id' => 8,
                    'sales_channel' => 'fnac',
                    'minimum_margin_rate' => 10,
                    'average_commission_rate' => 11.2,
                    'selling_price' => 279,
                    'can_sell_on_sales_channel' => true,
                    'selling_price_tax_excluded' => 232.5,
                    'margin' => 83.01,
                    'margin_rate' => 0.35703225806451616,
                    'markup_rate' => 0.6724179829890644,
                    'sales_channel_commission' => 26.039999999999996,
                    'errors' => null,
                    'pricing_strategy_ids' => [],
                ],
            ],
            'prices' => [
                'pvgc' => 389,
                'ecotax' => 0,
                'sorecop' => 0,
                'weighted_cost' => 123.45,
                'intragroup' => 152.58,
                'margin_rate' => 0.619,
                'margin_tax_excluded' => 200.72,
                'promo_budget' => null,
                'selling_price' => 389,
                'selling_price_tax_excluded' => 324.166667,
                'tariff_tax_excluded' => 195,
                'tariff_tax_included' => 234,
                'unconditional_discount' => 0,
                'vat' => 0.2,
                'initial_selling_price' => 389,
                'purchase_tax_excluded' => 0,
                'reseller_price' => 0,
                'selling_price_history' => [],
                'cost_adjustments' => [
                    [
                        'id' => 1,
                        'type' => 'supplier_credit_note',
                        'amount' => 1234.56,
                        'meta' => [
                            'comment' => 'je suis bien content qu\'on me donne des sous',
                            'quantity' => 12,
                            'date_range' => [
                                'to' => '2012-06-12 00:00:00',
                                'from' => '2012-04-12 00:00:00',
                            ],
                        ],
                        'created_at' => '2023-10-23 16:56:55',
                        'created_by' => [
                            'id' => 2,
                            'fullname' => 'Gérard MANVUSSA',
                        ],
                    ],
                    [
                        'id' => 2,
                        'type' => 'devaluation',
                        'amount' => 123.45,
                        'meta' => [
                            'chrono' => 'AZERTY',
                            'comment' => 'Tout se perd ma bonne dame',
                            'quantity' => 9,
                        ],
                        'created_at' => '2023-10-23 16:56:55',
                        'created_by' => [
                            'id' => 2,
                            'fullname' => 'Gérard MANVUSSA',
                        ],
                    ],
                ],
            ],
            'logistic' => [
                'weight' => 8.45,
                'code_128' => '11230081123',
                'weight_tmp' => 'N',
                'package_unit' => 1,
                'customs_code' => '85182200',
                'is_packaged' => 'N',
                'source_country_id' => 67,
                'number_of_packages' => 1,
                'customs_code_origin' => 'article',
                'ecotax_code' => null,
                'ecotax_code_origin' => 'subcategory',
                'source_country_name' => 'FRANCE',
                'rotations' => [
                    '7_days' => 0,
                    '30_days' => 0,
                    '90_days' => 0,
                ],
            ],
            'supplier' => [
                'name' => 'LA BOITE CONCEPT',
                'supplier_id' => 400,
                'supplier_model' => 'Boîte Concept Pieds LD120 LD130',
                'supplier_reference' => 'LD-F-25mm-N-P',
                'mininum_order_quantity' => 1,
            ],
            'subcategory' => [
                'name' => "Pieds d'enceintes",
                'subcategory_id' => 144,
            ],
            'brand' => [
                'name' => 'La Boite Concept',
                'brand_id' => 959,
            ],
            'color' => [
                'color_id' => 1,
                'code' => 'XXX',
                'name' => 'indéfinie',
                'image_url' => '',
            ],
            'declinations' => [],
            'destocks' => [
                [
                    'article_id' => 128416,
                    'delay' => 0,
                    'image' => '/images/ui/uiV3/graphics/no-img-300.png',
                    'selling_price' => '300.00',
                    'short_description' => 'Arcam BW CCM',
                    'sku' => 'BWCCM74',
                    'status' => 'tmp',
                ],
            ],
            'initial_article' => null,
            'last_comment' => [
                'comment_id' => 1,
                'content' => 'coucou',
                'created_by' => 'Hugo LAHUTTE',
                'when' => '2018-10-09 16:57:05.000000',
            ],
            'related_packages' => [
                [
                    'delay' => 0,
                    'image' => '/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
                    'package_id' => 13896,
                    'short_description' => 'NorStone CL250 Classic (25 m)',
                    'sku' => 'NORST-DECL',
                    'status' => 'oui',
                    'stock' => 5,
                    'selling_price' => 45,
                ],
            ],
            'replacements' => [
                'replaced_by' => null,
                'replaces' => [],
            ],
            'havre' => [
                'sku' => null,
                'is_active' => 0,
                'quantity' => 0,
                'package_unit' => 1,
            ],
            'destock_condition' => null,
        ];

        $this->assertEquals($expected_data, ArrayHelper::removeKeysFrom($content['data'], ['updated_at']));
    }

    public function test_retrieve_an_article_by_its_sku(): void
    {
        $this->client->request(
            'GET',
            '/api/v2/article/ELIPSPRESTIGEPK2I',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $content = json_decode($response->getContent(), true);

        $expected_data = [
            'article_id' => 72218,
            'sku' => 'ELIPSPRESTIGEPK2I',
            'created_at' => '2011-12-09',
            'last_log_date' => '2024-01-19 11:32:38',
            'status' => 'last',
            'unbasketable_reason' => 'UNAVAILABLE',
            'stock' => 129,
            'delay' => 1,
            'article_url' => 'https://www.son-video.com/article/cables-d-enceintes/norstone/cl250-classic-2-5-mm2-25-m',
            'is_package' => true,
            'is_destock' => false,
            'is_on_sale' => false,
            'name' => 'Prestige Cinema 2i Calvados',
            'short_description' => 'Pack 5 enceintes home-cinéma Elipson Prestige Cinema 2i Calvados comprenant 2 paires d\'enceintes Elipson Prestige 2i et l\'enceinte centrale Elipson Prestige C2i',
            'basket_description' => 'Pack 5 enceintes home-cinéma Elipson Prestige Cinema 2i Calvados comprenant 2 paires d\'enceintes Elipson Prestige 2i et l\'enceinte centrale Elipson Prestige C2i',
            'marketplace_description' => null,
            'embargo_date' => null,
            'manufacturer_warranty_years' => 0,
            'has_warranty_extension_5_years' => true,
            'warranty_extension_5_years_price' => 123,
            'packages' => 2,
            'image' => '/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_300_square.jpg',
            'eans' => [],
            'comparator' => 'oui',
            'sales_channels' => [
                [
                    'sales_channel_id' => 1,
                    'sales_channel' => 'son-video.com',
                    'can_sell_on_sales_channel' => false,
                    'average_commission_rate' => 11.2,
                    'minimum_margin_rate' => 0,
                    'selling_price' => 279,
                    'pricing_strategy_ids' => [],
                    'selling_price_tax_excluded' => 232.5,
                    'margin' => -139.79,
                    'margin_rate' => -0.6044972972972973,
                    'markup_rate' => -0.4051884057971014,
                    'sales_channel_commission' => 26.039999999999996,
                    'errors' => [
                        [
                            'code' => 'selling_price_margin_is_too_low',
                            'message' => 'Product margin is too low',
                        ],
                        [
                            'code' => 'selling_price_margin_rate_is_too_low',
                            'message' => 'Margin rate should not be lower than 0%',
                        ],
                    ],
                ],
                [
                    'sales_channel_id' => 3,
                    'sales_channel' => 'easylounge.com',
                    'can_sell_on_sales_channel' => false,
                    'average_commission_rate' => 11.2,
                    'minimum_margin_rate' => 7,
                    'selling_price' => 279,
                    'pricing_strategy_ids' => [],
                    'selling_price_tax_excluded' => 232.5,
                    'margin' => -139.79,
                    'margin_rate' => -0.6044972972972973,
                    'markup_rate' => -0.4051884057971014,
                    'sales_channel_commission' => 26.039999999999996,
                    'errors' => [
                        [
                            'code' => 'selling_price_margin_is_too_low',
                            'message' => 'Product margin is too low',
                        ],
                        [
                            'code' => 'selling_price_margin_rate_is_too_low',
                            'message' => 'Margin rate should not be lower than 7%',
                        ],
                    ],
                ],
                [
                    'sales_channel_id' => 11,
                    'sales_channel' => 'cilo.dk',
                    'can_sell_on_sales_channel' => false,
                    'average_commission_rate' => 677,
                    'minimum_margin_rate' => 7,
                    'selling_price' => 279,
                    'pricing_strategy_ids' => [],
                    'selling_price_tax_excluded' => 232.5,
                    'margin' => -1687.7749999999999,
                    'margin_rate' => -7.298486486486486,
                    'markup_rate' => -4.892101449275362,
                    'sales_channel_commission' => 1574.0249999999999,
                    'errors' => [
                        [
                            'code' => 'selling_price_margin_is_too_low',
                            'message' => 'Product margin is too low',
                        ],
                        [
                            'code' => 'selling_price_margin_rate_is_too_low',
                            'message' => 'Margin rate should not be lower than 7%',
                        ],
                    ],
                ],
            ],
            'prices' => [
                'vat' => 0.2,
                'pvgc' => 1757,
                'ecotax' => 1.5,
                'sorecop' => 0,
                'weighted_cost' => 345,
                'intragroup' => 427.92,
                'margin_rate' => null,
                'selling_price' => 279,
                'reseller_price' => 0,
                'margin_tax_excluded' => -148.75,
                'promo_budget' => null,
                'tariff_tax_excluded' => 345,
                'tariff_tax_included' => 414,
                'unconditional_discount' => 0,
                'initial_selling_price' => 0,
                'purchase_tax_excluded' => 0,
                'selling_price_tax_excluded' => 232.5,
                'selling_price_history' => [
                    [
                        'declination_id' => 72216,
                        'selling_price' => 598,
                        'started_at' => '2022-01-24',
                    ],
                ],
                'cost_adjustments' => [],
            ],
            'logistic' => [
                'weight' => 52.4,
                'code_128' => '12180072218',
                'rotations' => [
                    '7_days' => 0,
                    '30_days' => 0,
                    '90_days' => 0,
                ],
                'weight_tmp' => 'Y',
                'is_packaged' => 'N',
                'customs_code' => '85182200',
                'package_unit' => 1,
                'source_country_id' => null,
                'number_of_packages' => 1,
                'customs_code_origin' => 'category',
                'ecotax_code' => null,
                'ecotax_code_origin' => 'subcategory',
                'source_country_name' => null,
            ],
            'supplier' => [
                'name' => 'Indefini',
                'supplier_id' => 1,
                'supplier_model' => 'Prestige Cinema 2i',
                'supplier_reference' => null,
                'mininum_order_quantity' => 1,
            ],
            'subcategory' => [
                'name' => 'Packs d\'enceintes grand spectacle',
                'subcategory_id' => 117,
            ],
            'brand' => [
                'name' => 'Elipson',
                'brand_id' => 343,
            ],
            'color' => [
                'color_id' => 47,
                'code' => 'CVS',
                'name' => 'Calvados',
                'image_url' => 'http://www.son-video.com/images/static/Coloris/Calvados_Fonce.gif',
            ],
            'declinations' => [
                [
                    'article_id' => 72216,
                    'delay' => 1,
                    'image' => '/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_300_square.jpg',
                    'selling_price' => 199,
                    'short_description' => 'Elipson Prestige C2i Calvados',
                    'sku' => 'ELIPSPRESTIGE_C_2I',
                    'status' => 'oui',
                    'stock' => 2,
                ],
            ],
            'destocks' => [],
            'initial_article' => null,
            'last_comment' => null,
            'related_packages' => [],
            'replacements' => [
                'replaces' => [],
                'replaced_by' => null,
            ],
            'havre' => [
                'is_active' => 0,
                'sku' => null,
                'quantity' => 0,
                'package_unit' => 1,
            ],
            'destock_condition' => null,
        ];

        $this->assertEquals($expected_data, ArrayHelper::removeKeysFrom($content['data'], ['updated_at']));
    }

    public function test_retrieve_an_article_destock_by_its_sku(): void
    {
        $this->client->request(
            'GET',
            '/api/v2/article/BWCCM74',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $content = json_decode($response->getContent(), true);
        $expected_data = [
            'article_id' => 128416,
            'sku' => 'BWCCM74',
            'created_at' => '2018-06-19',
            'last_log_date' => null,
            'status' => 'tmp',
            'unbasketable_reason' => null,
            'stock' => 1,
            'delay' => 0,
            'article_url' => null,
            'is_package' => false,
            'is_destock' => true,
            'is_on_sale' => true,
            'name' => 'BWCCM74',
            'short_description' => 'Arcam BW CCM',
            'basket_description' => 'Enceinte encastrable BW CCM 7.4',
            'marketplace_description' => null,
            'embargo_date' => null,
            'manufacturer_warranty_years' => 0,
            'has_warranty_extension_5_years' => false,
            'warranty_extension_5_years_price' => null,
            'packages' => 1,
            'image' => '/images/ui/uiV3/graphics/no-img-300.png',
            'eans' => [],
            'comparator' => 'non',
            'sales_channels' => [
                [
                    'sales_channel_id' => 1,
                    'sales_channel' => 'son-video.com',
                    'can_sell_on_sales_channel' => false,
                    'average_commission_rate' => 11.2,
                    'margin' => -190.5,
                    'margin_rate' => -0.762,
                    'markup_rate' => -0.4618181818181818,
                    'minimum_margin_rate' => 0,
                    'sales_channel_commission' => 27.999999999999996,
                    'selling_price_tax_excluded' => 250,
                    'selling_price' => 300,
                    'errors' => [
                        [
                            'code' => 'selling_price_margin_is_too_low',
                            'message' => 'Product margin is too low',
                        ],
                        [
                            'code' => 'selling_price_margin_rate_is_too_low',
                            'message' => 'Margin rate should not be lower than 0%',
                        ],
                    ],
                    'pricing_strategy_ids' => [],
                ],
                [
                    'sales_channel_id' => 2,
                    'sales_channel' => 'son-video.pro',
                    'can_sell_on_sales_channel' => false,
                    'average_commission_rate' => 0,
                    'margin' => -162.5,
                    'margin_rate' => -0.65,
                    'markup_rate' => -0.3939393939393939,
                    'minimum_margin_rate' => 0,
                    'sales_channel_commission' => 0,
                    'selling_price_tax_excluded' => 250,
                    'selling_price' => 300,
                    'errors' => [
                        [
                            'code' => 'selling_price_margin_is_too_low',
                            'message' => 'Product margin is too low',
                        ],
                        [
                            'code' => 'selling_price_margin_rate_is_too_low',
                            'message' => 'Margin rate should not be lower than 0%',
                        ],
                    ],
                    'pricing_strategy_ids' => [],
                ],
            ],
            'prices' => [
                'vat' => 0.2,
                'pvgc' => 900,
                'ecotax' => 0,
                'sorecop' => 0,
                'weighted_cost' => null,
                'intragroup' => 509.85,
                'margin_rate' => null,
                'selling_price' => 300,
                'reseller_price' => 0,
                'margin_tax_excluded' => -162.5,
                'promo_budget' => null,
                'tariff_tax_excluded' => 412.5,
                'tariff_tax_included' => 495,
                'unconditional_discount' => 0,
                'initial_selling_price' => 0,
                'purchase_tax_excluded' => 0,
                'selling_price_tax_excluded' => 250,
                'selling_price_history' => [],
                'cost_adjustments' => [],
            ],
            'logistic' => [
                'weight' => 4,
                'code_128' => '14160128416',
                'rotations' => [
                    '7_days' => 0,
                    '30_days' => 0,
                    '90_days' => 0,
                ],
                'weight_tmp' => 'Y',
                'is_packaged' => 'N',
                'customs_code' => '85182200',
                'package_unit' => 1,
                'source_country_id' => null,
                'number_of_packages' => 1,
                'customs_code_origin' => 'category',
                'ecotax_code' => null,
                'ecotax_code_origin' => 'subcategory',
                'source_country_name' => null,
            ],
            'supplier' => [
                'name' => 'Indefini',
                'supplier_id' => 1,
                'supplier_model' => null,
                'supplier_reference' => null,
                'mininum_order_quantity' => 1,
            ],
            'subcategory' => [
                'name' => 'Enceintes encastrables',
                'subcategory_id' => 95,
            ],
            'brand' => [
                'name' => 'B&W',
                'brand_id' => 292,
            ],
            'color' => [
                'color_id' => 5,
                'code' => 'NR',
                'name' => 'Noir',
                'image_url' => 'http://www.son-video.com/images/static/Coloris/Noir.gif',
            ],
            'declinations' => [],
            'destocks' => [
                [
                    'article_id' => 128416,
                    'delay' => 0,
                    'image' => '/images/ui/uiV3/graphics/no-img-300.png',
                    'selling_price' => '300.00',
                    'short_description' => 'Arcam BW CCM',
                    'sku' => 'BWCCM74',
                    'status' => 'tmp',
                ],
            ],
            'initial_article' => [
                'article_id' => 81123,
                'delay' => 0,
                'image' => '/images/ui/uiV3/graphics/no-img-300.png',
                'selling_price' => 389,
                'short_description' => 'Pieds noirs laqués pour La Boîte Concept LD120 / LD130 (la paire)',
                'sku' => 'LBCLD25BP',
                'status' => 'oui',
            ],
            'last_comment' => null,
            'related_packages' => [],
            'replacements' => [
                'replaces' => [],
                'replaced_by' => null,
            ],
            'havre' => [
                'sku' => null,
                'is_active' => 0,
                'quantity' => 0,
                'package_unit' => 1,
            ],
            'destock_condition' => [
                'public_description' => 'Peut mieux faire',
                'internal_comment' => 'Il est génial',
                'state' => 'ACCEPTABLE',
                'origin' => 'RECONDITIONNED',
                'defects' => ['ACCESSORY_MISSING', 'MICRO_SCRATCH'],
                'assignment' => 'DESTOCK',
            ],
        ];

        $this->assertEquals($expected_data, ArrayHelper::removeKeysFrom($content['data'], ['updated_at']));
    }

    public function test_retrieve_an_article_destock_by_its_id(): void
    {
        $this->client->request(
            'GET',
            '/api/v2/article/128416',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer user1-token-admin']
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $content = json_decode($response->getContent(), true);
        $this->assertTrue($content['data']['is_destock']);
        $this->assertEquals(
            [
                [
                    'article_id' => 128416,
                    'delay' => 0,
                    'image' => '/images/ui/uiV3/graphics/no-img-300.png',
                    'selling_price' => '300.00',
                    'short_description' => 'Arcam BW CCM',
                    'sku' => 'BWCCM74',
                    'status' => 'tmp',
                ],
            ],
            $content['data']['destocks']
        );
    }
}
