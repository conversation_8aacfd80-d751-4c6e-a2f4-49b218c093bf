<?php

namespace App\Tests\PHPUnit\EndToEnd\Api\Article\Stock;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class CPostArticleSafetyStockRecommendationsControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'safety_stock/safety_stock.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/article/safety-stock/recommendations',
            [],
            [],
            ['HTTP_CONTENT_TYPE' => 'application/json']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/article/safety-stock/recommendations',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer unknown-token',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_successful_response(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/article/safety-stock/recommendations',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ],
            '{"where": {}}'
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals('success', $response_data['status']);
        self::assertEquals(2, $response_data['data']['_pager']['total']);
        self::assertCount(2, $response_data['data']['safety_stock_recommendations']);

        $expected_first_recommendation = [
            'sku' => 'ARCAMRBLINKNR',
            'article_id' => 81078,
            'deliverable_stock' => 33,
            'original_safety_stock' => 6,
            'days_until_out_of_stock' => 330,
            'estimated_out_of_stock_date' => '2026-06-19',
            'product_weighted_avg_replenishment_lead_time' => 2,
            'product_min_replenishment_lead_time' => 2,
            'product_max_replenishment_lead_time' => 2,
            'recommended_safety_stock_quantity' => 2,
            'supplier_name' => 'PPL',
            'supplier_id' => 162,
            'subcategory_name' => 'Récepteurs Bluetooth',
            'subcategory_id' => 258,
            'brand_name' => 'Arcam',
            'brand_id' => 262,
            'average_daily_quantity' => 1,
            'minimum_order_quantity' => 1,
            'product_class' => 'CZ',
        ];

        self::assertEquals($expected_first_recommendation, $response_data['data']['safety_stock_recommendations'][0]);
    }

    public function test_filtering_by_brand_id(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'safety_stock/safety_stock.sql',
        ]);

        $json = <<<JSON
         {
             "where": {
                 "_and": [
                     {
                         "brand_id": {
                             "_eq": 959
                         }
                     }
                 ]
             }
         }
        JSON;

        $this->client->request(
            'POST',
            '/api/v1/article/safety-stock/recommendations',
            [],
            [],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
            ],
            '{"where":{"_and":[{"brand_id":{"_eq":959}}]}}'
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals('success', $response_data['status']);
        self::assertCount(1, $response_data['data']['safety_stock_recommendations']);

        $expected_filtered_recommendation = [
            'sku' => 'LBCLD25BP',
            'article_id' => 81123,
            'deliverable_stock' => 2,
            'original_safety_stock' => 6,
            'days_until_out_of_stock' => 100,
            'estimated_out_of_stock_date' => '2025-11-08',
            'product_weighted_avg_replenishment_lead_time' => 3,
            'product_min_replenishment_lead_time' => 3,
            'product_max_replenishment_lead_time' => 3,
            'recommended_safety_stock_quantity' => 6,
            'supplier_name' => 'LA BOITE CONCEPT',
            'supplier_id' => 400,
            'subcategory_name' => "Pieds d'enceintes",
            'subcategory_id' => 144,
            'brand_name' => 'La Boite Concept',
            'brand_id' => 959,
            'average_daily_quantity' => 2,
            'minimum_order_quantity' => 1,
            'product_class' => 'CY',
        ];

        self::assertEquals(
            $expected_filtered_recommendation,
            $response_data['data']['safety_stock_recommendations'][0]
        );
    }
}
