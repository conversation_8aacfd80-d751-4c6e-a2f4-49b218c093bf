<?php

namespace PHPUnit\EndToEnd\Api\Article;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\File\FilesystemHelper;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class DeleteArticleMediaFileControllerTest extends WebTestCase
{
    private $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql']);
        MySqlDatabase::loadSpecificFixtures(['sales_channel/sales_channels.sql']);
        MySqlDatabase::loadSpecificFixtures(['article/delete_article_media_file.sql']);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.jpg',
            [],
            [],
            ['CONTENT_TYPE' => 'application/json']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.jpg',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer unknown-token',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_token_linked_to_account_without_permission(): void
    {
        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.jpg',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_with_non_supported_file_extension(): void
    {
        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.bmp',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function test_file_does_not_exist(): void
    {
        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.jpg',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function test_delete_jpg_file_successfully(): void
    {
        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', 'path/to/file.jpg', 'test.jpg');

        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.jpg',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        // Test with uppercase extension
        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', 'path/to/file.JPG', 'test.jpg');

        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.JPG',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    public function test_delete_png_file_successfully(): void
    {
        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', 'path/to/file.png', 'test.jpg');

        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.png',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        // Test with uppercase extension
        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', 'path/to/file.PNG', 'test.jpg');

        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.PNG',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    public function test_delete_pdf_file_successfully(): void
    {
        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', 'path/to/file.pdf', 'test.jpg');

        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.pdf',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        // Test with uppercase extension
        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', 'path/to/file.PDF', 'test.jpg');

        $this->client->request(
            'DELETE',
            '/api/v1/article/media/path/to/file.PDF',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }
}
