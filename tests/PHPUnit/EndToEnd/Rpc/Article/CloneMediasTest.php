<?php

namespace PHPUnit\EndToEnd\Rpc\Article;

use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class CloneMediasTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'article:clone_medias';

    public function test_rpc_with_no_payload_fails(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, []);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '-32602', with message ===
        Could not fetch unexistent argument 0 (article_id_source).
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }
}
