<?php

namespace PHPUnit\EndToEnd\Rpc\SystemEvent;

use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class LogTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'system_event:log';

    public function test_fails_to_log_because_there_is_no_required_relationship_key(): void
    {
        $payload = [
            'meta' => [
                'foo' => 'bar',
            ],
            'data' => [
                [
                    'dummy' => 'value',
                ],
            ],
        ];

        $this->sendRpcRequest(self::RPC_METHOD, ['subject.action', $payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArray<PERSON><PERSON><PERSON>ey('result', $response_data);
        self::assertFalse($response_data['result']['success']);
        self::assertStringContainsString(
            'Fails to log system event via RPC. Detailed on error can be found in the application error logs',
            $response_data['result']['error_message']
        );
    }

    public function test_successfully_log_system_event(): void
    {
        $payload = [
            '_rel' => [
                'customer' => 123,
            ],
            'meta' => [
                'foo' => 'bar',
            ],
            'data' => [
                [
                    'dummy' => 'value',
                ],
            ],
        ];

        $this->sendRpcRequest(self::RPC_METHOD, ['subject.action', $payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('result', $response_data);
        self::assertTrue($response_data['result']['success']);
    }
}
