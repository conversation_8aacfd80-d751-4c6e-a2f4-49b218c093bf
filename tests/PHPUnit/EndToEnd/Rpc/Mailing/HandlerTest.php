<?php

namespace PHPUnit\EndToEnd\Rpc\Mailing;

use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class HandlerTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'mailing:handler';

    public function test_rpc_with_invalid_payload(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['none', ['foo' => 'bar']]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertEquals(
            'Internal server error. Details can be found in the application error logs',
            $response_data['result']['error_message']
        );
        self::assertFalse($response_data['result']['success']);
    }

    /**
     * Note: you can find tests on internal validation and non-publicly exposed response and exception in
     * SonVideo\Erp\Tests\Unit\Mailing\Manager\Debug\DebugEmailDispatcher The unit test is using a "debug" json request
     * payload usable in all environments via you REST client.
     */
    public function test_successfully_handle_rpc_request(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['dummy', ['foo' => 'bar']]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('result', $response_data);
    }
}
