<?php

namespace PHPUnit\EndToEnd\Rpc\Eav;

use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class FetchAttributeValuesForArticleTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'eav:fetch_attribute_values_for_article';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
        PgDatabase::loadSpecificFixtures(['eav/rpc_eav_fetch_attribute_values_for_article.sql']);
    }

    public function test_fetch_attribute_for_non_existing_sku(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['NONEXISTING', 935]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('result', $response_data);
        self::assertArrayHasKey('attribute', $response_data['result']);
        self::assertEquals([], $response_data['result']['attribute']);
    }

    public function test_fetch_attribute_for_non_existing_attribute_id(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['ARCAMRBLINKNR', 666]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('result', $response_data);
        self::assertArrayHasKey('attribute', $response_data['result']);
        self::assertEquals([], $response_data['result']['attribute']);
    }

    public function test_fetch_attribute_not_attached_to_article(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['ARCAMRBLINKNR', 887]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('result', $response_data);
        self::assertArrayHasKey('attribute', $response_data['result']);
        self::assertEquals([], $response_data['result']['attribute']);
    }

    public function test_fetch_attribute_for_article_successfully(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['ARCAMRBLINKNR', 935]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        $this->assertEquals($response_data['result']['attribute'], [
            [
                'sku' => 'ARCAMRBLINKNR',
                'name' => 'Sens Transmission',
                'meta' => [
                    'unit' => null,
                    'label' => 'Sens de transmission',
                    'prefix' => null,
                    'suffix' => null,
                ],
                'definition' => [
                    'type' => 'text',
                ],
                'attribute_value_id' => 25292,
                'attribute_id' => 935,
                'value' => 'Emetteur',
                'value_meta' => [],
            ],
        ]);
    }
}
