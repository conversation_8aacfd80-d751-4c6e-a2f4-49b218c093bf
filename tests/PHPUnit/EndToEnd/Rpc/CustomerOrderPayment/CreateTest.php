<?php

namespace PHPUnit\EndToEnd\Rpc\CustomerOrderPayment;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class CreateTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'customer_order_payment:create';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Load fixtures based on the @clear-database tag in the scenario
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order_payment/rpc/creator.sql',
        ]);
    }

    public function test_rpc_with_no_payload(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, []);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '-32602', with message ===
        Could not fetch unexistent argument 0 (payload).
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_rpc_with_wrong_payload(): void
    {
        $payload = [
            'customer_order_id' => '',
        ];

        $this->sendRpcRequest(self::RPC_METHOD, [$payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        RPC exception caught code '0', with message ===
        Failed to denormalize attribute "customer_order_id" value for class "SonVideo\Erp\CustomerOrderPayment\Dto\CustomerOrderPaymentCreationRequestDto": Expected argument of type "int", "string" given at property path "customer_order_id".
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(32099, $response_data['error']['code']);
    }

    public function test_rpc_with_payload_who_is_not_a_json(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, ['toto']);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertArrayHasKey('error', $response_data);

        $message = <<<TEXT
        Exception caught code '0', with message ===
        Payload must be an array.
        === END OF MESSAGE.
        TEXT;

        self::assertEquals($message, $response_data['error']['message']);
        self::assertEquals(-32603, $response_data['error']['code']);
    }

    public function test_rpc_response(): void
    {
        $payload = [
            'customer_order_id' => 1724028,
            'payments' => [
                [
                    'payment_mean' => 'CBS',
                    'created_at' => '2022-01-01 00:00:00',
                    'amount' => 1104.99,
                    'extra_data' => [],
                ],
            ],
        ];

        $this->sendRpcRequest(self::RPC_METHOD, [$payload]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        self::assertEquals([], $response_data['result']['action']);
    }
}
