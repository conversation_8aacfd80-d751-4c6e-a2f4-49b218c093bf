<?php

namespace SonVideo\Erp\Tests\Unit\SubCategory\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Unit\Test;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use SonVideo\Erp\SubCategory\Entity\SubcategorySalesChannelUpdaterEntity;
use SonVideo\Erp\SubCategory\Manager\SubcategorySalesChannelUpdater as TestedClass;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class SubcategorySalesChannelUpdater extends Test
{
    public function setUp(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'category/categories.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get(TestedClass::class);
    }

    /** @throws \Exception */
    protected function getSerializer(): SerializerInterface
    {
        return $this->getContainer()->get(SerializerInterface::class);
    }

    /**
     * @throws NotFoundException
     * @throws ExceptionInterface
     * @throws \JsonException
     * @throws InternalErrorException
     * @throws \Exception
     */
    public function test_update(): void
    {
        $tested_subcategory_id = 144;
        $old_sales_channel_subcategory = $this->getSubcategorySalesChannels($tested_subcategory_id);

        $this->assert('Contain before update');
        $this->array($old_sales_channel_subcategory)->hasSize(3);
        $this->integer((int) $old_sales_channel_subcategory[0]['sales_channel_id'])->isEqualTo(3);
        $this->float((float) $old_sales_channel_subcategory[0]['commission_rate'])->isEqualTo(13.5);
        $this->integer((int) $old_sales_channel_subcategory[1]['sales_channel_id'])->isEqualTo(4);
        $this->float((float) $old_sales_channel_subcategory[1]['commission_rate'])->isEqualTo(18.0);
        $this->integer((int) $old_sales_channel_subcategory[2]['sales_channel_id'])->isEqualTo(5);
        $this->float((float) $old_sales_channel_subcategory[2]['commission_rate'])->isEqualTo(12.0);

        $this->assert('Update');
        $dto = $this->getSerializer()->denormalize(
            [
                [
                    'subcategory_id' => 144,
                    'sales_channel_id' => 3,
                    'commission_rate' => 13.5,
                ],
                [
                    'subcategory_id' => 144,
                    'sales_channel_id' => 4,
                    'commission_rate' => null,
                ],
                [
                    'subcategory_id' => 144,
                    'sales_channel_id' => 5,
                    'commission_rate' => 15.5,
                ],
                [
                    'subcategory_id' => 144,
                    'sales_channel_id' => 6,
                    'commission_rate' => 11,
                ],
                [
                    'subcategory_id' => 144,
                    'sales_channel_id' => 7,
                    'commission_rate' => 11,
                ],
                [
                    'subcategory_id' => 144,
                    'sales_channel_id' => 1,
                    'commission_rate' => 11,
                ],
            ],
            SubcategorySalesChannelUpdaterEntity::class . '[]'
        );
        $this->getTestedInstance()->update($dto);

        $this->assert('Contain after update');
        $new_sales_channel_subcategory = $this->getSubcategorySalesChannels($tested_subcategory_id);
        $this->array($new_sales_channel_subcategory)->hasSize(4);
        $this->array($new_sales_channel_subcategory[0])->isEqualTo($old_sales_channel_subcategory[0]);
        $this->integer((int) $new_sales_channel_subcategory[1]['sales_channel_id'])->isEqualTo(5);
        $this->float((float) $new_sales_channel_subcategory[1]['commission_rate'])->isEqualTo(15.5);
        $this->integer((int) $new_sales_channel_subcategory[2]['sales_channel_id'])->isEqualTo(6);
        $this->float((float) $new_sales_channel_subcategory[2]['commission_rate'])->isEqualTo(11.0);
        $this->integer((int) $new_sales_channel_subcategory[3]['sales_channel_id'])->isEqualTo(7);
        $this->float((float) $new_sales_channel_subcategory[3]['commission_rate'])->isEqualTo(11.0);
    }

    public function test_dto_validation()
    {
        $dto = $this->getSerializer()->denormalize(
            [
                [
                    'subcategory_id' => 144,
                    'sales_channel_id' => 3,
                    'commission_rate' => -16.0,
                ],
            ],
            SubcategorySalesChannelUpdaterEntity::class . '[]'
        );
        $subcategory_sales_channel_updater = $this->getTestedInstance();
        $this->exception(static function () use ($dto, $subcategory_sales_channel_updater): void {
            $subcategory_sales_channel_updater->update($dto);
        })
            ->isInstanceOf(InternalErrorException::class)
            ->message->contains('Invalid parameters');

        $dto = $this->getSerializer()->denormalize(
            [
                [
                    'subcategory_id' => 144,
                    'sales_channel_id' => 666666666,
                    'commission_rate' => 16.0,
                ],
            ],
            SubcategorySalesChannelUpdaterEntity::class . '[]'
        );
        $subcategory_sales_channel_updater = $this->getTestedInstance();
        $this->exception(static function () use ($dto, $subcategory_sales_channel_updater): void {
            $subcategory_sales_channel_updater->update($dto);
        })
            ->isInstanceOf(ForeignKeyConstraintViolationException::class)
            ->message->contains('An exception occurred while executing');

        $dto = $this->getSerializer()->denormalize(
            [
                [
                    'subcategory_id' => 666666666,
                    'sales_channel_id' => 5,
                    'commission_rate' => 16.0,
                ],
            ],
            SubcategorySalesChannelUpdaterEntity::class . '[]'
        );
        $subcategory_sales_channel_updater = $this->getTestedInstance();
        $this->exception(static function () use ($dto, $subcategory_sales_channel_updater): void {
            $subcategory_sales_channel_updater->update($dto);
        })
            ->isInstanceOf(ForeignKeyConstraintViolationException::class)
            ->message->contains('An exception occurred while executing');
    }

    protected function getPdo(): LegacyPdo
    {
        return $this->getContainer()->get(LegacyPdo::class);
    }

    private function getSubcategorySalesChannels(int $subcategory_id)
    {
        $sql = <<<SQL
            SELECT * FROM backOffice.sales_channel_subcategories WHERE subcategory_id = :subcategory_id ORDER BY sales_channel_id;
        SQL;

        return $this->getPdo()->fetchAll($sql, ['subcategory_id' => $subcategory_id]);
    }
}
