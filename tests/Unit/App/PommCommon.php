<?php
/*
 * This file is part of CMS BackOffice package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Unit;

use PommProject\Foundation\Session\ResultHandler;
use PommProject\ModelManager\Session;

/**
 * PommCommon.
 *
 * Common trait to apply fixtures using a postgres database in the CMS BackOffice Application
 *
 * @copyright   2016 Son Video Distribution
 * <AUTHOR> Audic <<EMAIL>>
 */
trait PommCommon
{
    /** getPommSession */
    protected function getPommSession(string $session = 'wms_db'): Session
    {
        return $this->getContainer()
            ->get('pomm')
            ->getSession($session);
    }

    /**
     * Create a new Pomm session, useful for find data updated on a transaction.
     *
     * @return mixed
     */
    protected function getNewPommSession()
    {
        return $this->getContainer()
            ->get('pomm')
            ->createSession('wms_db');
    }

    /**
     * executeSqlQuery.
     *
     * @return ResultHandler|array
     */
    protected function executeSqlQuery(string $sql, string $session = 'postgres')
    {
        return $this->getPommSession($session)
            ->getConnection()
            ->executeAnonymousQuery($sql);
    }
}
