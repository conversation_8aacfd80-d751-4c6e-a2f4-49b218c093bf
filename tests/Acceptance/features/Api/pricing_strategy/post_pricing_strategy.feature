Feature: Create a pricing strategy

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "pricing_strategy/pricing_strategy.sql"
    And   I send a "POST" request to "/api/v1/pricing-strategy" with body:
    """
    {
      "name": "Stratégie test",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [],
      "competitors": []
    }
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/pricing-strategy" with body:
    """
    {
      "name": "Stratégie test",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [],
      "competitors": []
    }
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategy" with body:
    """
    {
      "name": "Stratégie test",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [],
      "competitors": []
    }
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Create a pricing strategy
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategy" with body:
    """
    {
      "name": "Stratégie test",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [7, 2],
      "competitors": []
    }
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data->pricing_strategy_id" should be equal to the number "15"

  Scenario: Create a pricing strategy fail
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategy" with body:
    """
    {}
    """
    Then  the response status code should be 400
    And the JSON node "status" should be equal to the string "error"
    And the JSON node "message" should be equal to the string "Invalid parameters"
    And the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "name": "This value should not be blank.",
        "activation_status": "This value should not be blank.",
        "ends_at": "This value should not be blank.",
        "name": "This value should not be blank.",
        "starts_at": "This value should not be blank."
      }
    }
    """

  Scenario: Create a pricing strategy should fail if son-video is not the only sales channel
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/pricing-strategy" with body:
    """
    {
      "name": "Stratégie test son-video et easylounge",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [1, 2],
      "competitors": []
    }
    """
    Then  the response status code should be 400
    And the JSON node "status" should be equal to the string "error"
    And the JSON node "message" should be equal to the string "Invalid parameters"
    And the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "sales_channels": "[key:son_video_only] : The son-video channel cannot be associated with other channels."
      }
    }
    """
