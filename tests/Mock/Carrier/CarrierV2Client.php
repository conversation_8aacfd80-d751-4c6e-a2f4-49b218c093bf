<?php

namespace App\Tests\Mock\Carrier;

use App\Contract\CarrierV2ClientInterface;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class ChronopostClient.
 */
class CarrierV2Client implements CarrierV2ClientInterface
{
    public function generateStickerAndPrint(
        int $delivery_note_id,
        string $printer_id,
        int $user_id,
        int $nb_stickers = 1
    ): void {
    }

    public function closeShipment(int $shipment_id): void
    {
    }

    public function closeTransfers(): void
    {
    }

    public function print(int $id, string $printer_id, string $type): void
    {
    }

    public function getDispatchNote(int $shipment_id): Response
    {
        return new Response('', 200);
    }

    public function getTransferDispatchNote(int $dispatch_note_id): Response
    {
        return new Response('', 500);
    }

    public function getSticker(int $shimpent_parcel_id): Response
    {
        return new Response('', 200);
    }
}
