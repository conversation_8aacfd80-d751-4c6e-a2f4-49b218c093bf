<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock\Carrier\Client;

use League\Flysystem\FileNotFoundException;
use League\Flysystem\FilesystemInterface;
use League\Flysystem\MountManager;
use SonVideo\Erp\Carrier\Contract\CarrierClientInterface;
use SonVideo\Erp\Carrier\Contract\EnvoiDuNetAwareTrait;

class EnvoiDuNetClient implements CarrierClientInterface
{
    use EnvoiDuNetAwareTrait;

    public const FIXTURE_FILESYSTEM = 'mock_filesystem';

    /** @var FilesystemInterface */
    private $filesystem;

    /** EnvoiDuNetClient constructor. */
    public function __construct(MountManager $mount_manager)
    {
        $this->filesystem = $mount_manager->getFilesystem(static::FIXTURE_FILESYSTEM);
    }

    /**
     * getClient.
     *
     * @return $this
     */
    public function getClient(): self
    {
        return $this;
    }

    /**
     * getAccount.
     *
     * @return $this
     */
    public function getAccount(): self
    {
        return $this;
    }

    /**
     * createShipment.
     *
     * @param $request
     *
     * @throws FileNotFoundException
     */
    public function createShipment(EnvoiDuNetClient $me, $request): array
    {
        $response = [];
        $response['error'] = new \stdClass();
        $response['error']->error_id = 0;
        $response['response'] = new \stdClass();
        $response['response']->pdf = $this->filesystem->read(
            sprintf('carrier/sticker/%s.pdf', strtolower($request->reference))
        );
        $response['response']->shipments = [];
        $response['response']->shipments[] = $this->addTrackingNumber(1);
        $response['response']->shipments[] = $this->addTrackingNumber(2);

        return $response;
    }

    /** addTrackingNumber */
    protected function addTrackingNumber(int $num): \stdClass
    {
        $sticker = new \stdClass();
        $sticker->trackingnumber = sprintf('WHATEVER_%d', $num);

        return $sticker;
    }

    /**
     * ping.
     *
     * @return mixed
     */
    public function ping(): bool
    {
        return true;
    }
}
