<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock\Carrier\Client;

use League\Flysystem\FilesystemInterface;
use League\Flysystem\MountManager;
use SonVideo\Erp\Carrier\Contract\CarrierClientInterface;
use SonVideo\Erp\Carrier\Contract\ChronopostAwareTrait;

/**
 * Class ChronopostClient.
 */
class ChronopostClient implements CarrierClientInterface
{
    use ChronopostAwareTrait;

    public const FIXTURE_FILESYSTEM = 'mock_filesystem';

    /** @var FilesystemInterface */
    private $filesystem;

    /** EnvoiDuNetClient constructor. */
    public function __construct(MountManager $mount_manager)
    {
        $this->filesystem = $mount_manager->getFilesystem(static::FIXTURE_FILESYSTEM);
    }

    /** {@inheritDoc} */
    public function init(string $end_point, array $options = [])
    {
        return $this;
    }

    /** {@inheritDoc} */
    public function shippingV6($params)
    {
        $xml = str_ireplace(
            // simple xml does not like the soap specific tags
            ['soap:', 'ns1:'],
            '',
            $this->filesystem->read(
                sprintf('carrier/sticker/chronopost-%s.xml', strtolower($params['refValue']['shipperRef']))
            )
        );

        return simplexml_load_string($xml);
    }

    /**
     * logRequestAndResponse.
     *
     * @return $this
     */
    public function logRequestAndResponse(string $status = 'success')
    {
        return $this;
    }
}
