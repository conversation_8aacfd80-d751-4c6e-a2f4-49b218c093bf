<?php
/*
 * This file is part of portal package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock;

use Psr\Cache\InvalidArgumentException;
use SonVideo\Synapps\Client\Manager\SynappsNotifier;
use SonVideo\Synapps\Client\Message;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;
use Symfony\Component\EventDispatcher\GenericEvent;

/**
 * Class SynappsNotifierMock.
 *
 * <AUTHOR> <<EMAIL>>
 */
class SynappsNotifierMock extends SynappsNotifier
{
    /**
     * getCache.
     *
     * Return cache object.
     */
    private function getCache(string $app_dir = ''): FilesystemAdapter
    {
        return new FilesystemAdapter('synapps_test', 0, $app_dir . 'var/cache/test');
    }

    /** @throws InvalidArgumentException */
    public function getMessages(): array
    {
        $cache = $this->getCache();
        $item = $cache->getItem('messages');

        return $item->isHit() ? $item->get() : [];
    }

    /** @throws InvalidArgumentException */
    public function save(array $messages): SynappsNotifier
    {
        $cache = $this->getCache();
        $item = $cache->getItem('messages');

        $item->set($messages);
        $cache->save($item);

        return $this;
    }

    /**
     * notify.
     *
     * Send a notification to synapps client.
     *
     * @throws InvalidArgumentException
     */
    public function notify(string $subject, array $payload): SynappsNotifier
    {
        $messages = $this->getMessages();
        $messages[] = new GenericEvent(new Message($subject, $payload));

        $this->save($messages);

        return $this;
    }

    /** flush */
    public function flush(): self
    {
        $this->getCache()->clear();

        return $this;
    }

    /** @throws InvalidArgumentException */
    public function getCountMessages(): int
    {
        return count($this->getMessages());
    }

    /** @throws InvalidArgumentException */
    public function hasSentEventWithSubject(string $subject): bool
    {
        foreach ($this->getMessages() as $message) {
            if ($message->getSubject()->getSubject() === $subject) {
                return true;
            }
        }

        return false;
    }

    /** @throws InvalidArgumentException */
    public function hasSentEventWithSubjectAndPayload(string $subject, array $payload): bool
    {
        foreach ($this->getMessages() as $message) {
            if (
                $message->getSubject()->getSubject() === $subject &&
                json_encode($message->getSubject()->getPayload(), JSON_THROW_ON_ERROR) ===
                    json_encode($payload, JSON_THROW_ON_ERROR)
            ) {
                return true;
            }
        }

        return false;
    }

    /** getLastMessage */
    public function getLastMessage(): ?Message
    {
        $messages = $this->getMessages();
        if ([] === $messages) {
            return null;
        }

        return $messages[count($messages) - 1]->getSubject();
    }

    /**
     * @return mixed[]
     *
     * @throws InvalidArgumentException
     */
    public function getMessagesArray(): array
    {
        $messages = $this->getMessages();
        if ([] === $messages) {
            return [];
        }

        return array_map(static function ($message): array {
            return [
                'subject' => $message->getSubject()->getSubject(),
                'payload' => $message->getSubject()->getPayload(),
            ];
        }, $messages);
    }
}
