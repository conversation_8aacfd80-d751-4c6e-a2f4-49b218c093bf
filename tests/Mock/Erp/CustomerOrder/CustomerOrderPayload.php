<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock\Erp\CustomerOrder;

use <PERSON>\Uuid\Uuid;
use SonVideo\Erp\Referential\CustomerOrderOrigin;

class CustomerOrderPayload
{
    private const DEFAULT_PAYLOAD = [
        'customer_order_id' => 123,
        'created_at' => '2022-01-01 00:00:00',
        'estimated_delivery_date' => null,
        'is_excluding_tax' => false,
        'quote_id' => null,
        'ip_address' => '***********',
        'billing_address' => [
            'company_name' => '',
            'civility' => 'M.',
            'firstname' => 'Alain',
            'lastname' => 'TERIEUR',
            'address' => '1 rue des fleurs',
            'postal_code' => '44100',
            'city' => 'NANTES',
            'country_code' => 'FR',
            'phone' => '**********',
            'cellphone' => '**********',
            'email' => '<EMAIL>',
        ],
        'shipping_address' => [
            'company_name' => '',
            'civility' => 'M.',
            'firstname' => 'Laure',
            'lastname' => 'DURE',
            'address' => '111 route de paris',
            'postal_code' => '44000',
            'city' => 'NANTES',
            'country_code' => 'FR',
            'phone' => '0707070707',
            'cellphone' => '0707070708',
            'email' => '<EMAIL>',
        ],
        'shipment_method' => [
            'cost' => 4.99,
        ],
        'payments' => [
            [
                'amount' => 44.99,
            ],
        ],
        'products' => [
            [
                'sku' => 'LBCLD25BP',
                'description' => 'Paire de supports d\'enceintes B-Tech Mountløgic BT77 Noir',
                'unit_discount_amount' => 0.0,
                'ecotax_price' => 0,
                'sorecop_price' => 0,
                'quantity' => 1,
                'selling_price_tax_included' => 40,
                'selected_warranties' => [],
            ],
        ],
    ];

    public static function getValidPayloadFromSiteWithAQuoteAndPromoCode(): array
    {
        $payload = self::DEFAULT_PAYLOAD;
        $payload['origin'] = CustomerOrderOrigin::SITE;
        $payload['customer_id'] = 1;
        $payload['quote_id'] = 10;
        $payload['promotion_id'] = 23;
        $payload['promotion_code'] = 'TOTO';
        $payload['promotion_linked_products'] = [];
        $payload['shipment_method'] = [
            'cost' => 4.99,
            'shipment_method_id' => 1,
            'shipping_delay' => 1,
        ];
        $payload['payments'] = [
            [
                'payment_mean' => 'CBS-O',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 2362.99,
            ],
        ];
        $payload['products'] = [
            [
                'sku' => 'ARCAMRBLINKNR',
                'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                'ecotax_price' => 0,
                'sorecop_price' => 0,
                'quantity' => 2,
                'selling_price_tax_included' => 1200,
                'unit_discount_amount' => -100,
                'selected_warranties' => [
                    [
                        'type' => 'extension',
                        'unit_selling_price_tax_included' => 79,
                        'duration' => 5,
                        'label' => 'Je suis une extension de garantie',
                    ],
                ],
            ],
        ];

        return $payload;
    }

    public static function getValidPayloadFromSiteWithAQuote(): array
    {
        $payload = self::DEFAULT_PAYLOAD;
        $payload['origin'] = CustomerOrderOrigin::SITE;
        $payload['customer_id'] = 1;
        $payload['quote_id'] = 11;
        $payload['shipment_method'] = [
            'cost' => 5.9,
            'shipment_method_id' => 65,
            'shipping_delay' => 1,
        ];
        $payload['payments'] = [
            [
                'payment_mean' => 'VIR',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 349.3,
            ],
        ];
        $payload['products'] = [
            [
                'sku' => 'ARCAMRBLINKNR',
                'description' => 'Câble d\'enceintes NorStone CL250 Classic - Conducteur en cuivre OFC, section 2 x 2,5 mm2, gaine transparente et longueur 1 m',
                'ecotax_price' => 0.2,
                'sorecop_price' => 0,
                'quantity' => 25,
                'selling_price_tax_included' => 2.99,
                'unit_discount_amount' => -0.3,
                'selected_warranties' => [],
            ],
            [
                'sku' => 'ARCAMRBLINKNR',
                'description' => 'Câble d\'enceintes NorStone CL250 Classic - Conducteur en cuivre OFC, section 2 x 2,5 mm2, gaine transparente et longueur 1 m',
                'ecotax_price' => 0.2,
                'sorecop_price' => 0,
                'quantity' => 5,
                'selling_price_tax_included' => 2.99,
                'unit_discount_amount' => -0.3,
                'selected_warranties' => [],
            ],
            [
                'sku' => 'LBCLD25BP',
                'description' => 'Paire de supports d\'enceintes B-Tech Mountlogic BT77 Noir',
                'ecotax_price' => 0,
                'sorecop_price' => 0,
                'quantity' => 1,
                'selling_price_tax_included' => 39.9,
                'unit_discount_amount' => -0.97,
                'selected_warranties' => [],
            ],
            [
                'sku' => 'BWCCM74',
                'description' => 'Paire d\'enceintes compactes Klipsch Reference Premiere RP-400M Noir',
                'ecotax_price' => 0.5,
                'sorecop_price' => 0,
                'quantity' => 1,
                'selling_price_tax_included' => 229,
                'unit_discount_amount' => -5.23,
                'selected_warranties' => [],
            ],
        ];

        return $payload;
    }

    public static function getValidPayloadFromSiteWithProductAndBundle(): array
    {
        $payload = self::DEFAULT_PAYLOAD;
        $payload['origin'] = CustomerOrderOrigin::SITE;
        $payload['customer_id'] = 1;
        $payload['shipment_method'] = [
            'cost' => 5.9,
            'shipment_method_id' => 65,
            'shipping_delay' => 1,
        ];
        $payload['payments'] = [
            [
                'payment_mean' => 'VIR',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 349.3,
            ],
        ];
        $payload['products'] = [
            [
                'sku' => 'ARCAMRBLINKNR',
                'description' => 'Câble d\'enceintes NorStone CL250 Classic - Conducteur en cuivre OFC, section 2 x 2,5 mm2, gaine transparente et longueur 1 m',
                'ecotax_price' => 0.2,
                'sorecop_price' => 0,
                'quantity' => 25,
                'selling_price_tax_included' => 2.99,
                'unit_discount_amount' => -0.3,
                'selected_warranties' => [],
            ],
            [
                'sku' => 'NORSTCL25025M',
                'description' => 'Toto',
                'ecotax_price' => 0.1,
                'sorecop_price' => 0.1,
                'quantity' => 1,
                'selling_price_tax_included' => 7.79,
                'unit_discount_amount' => 0,
                'selected_warranties' => [],
            ],
        ];

        return $payload;
    }

    public static function getValidPayloadFromSiteWithPromoCode(): array
    {
        $payload = self::DEFAULT_PAYLOAD;
        $payload['origin'] = CustomerOrderOrigin::SITE;
        $payload['customer_id'] = 1;
        $payload['promotion_id'] = 23;
        $payload['promotion_code'] = 'PUYDUFOU';
        $payload['promotion_linked_products'] = ['BWCCM74', 'LBCLD25BP'];
        $payload['shipment_method'] = [
            'cost' => 0,
            'shipment_method_id' => 65,
            'shipping_delay' => 1,
        ];
        $payload['payments'] = [
            [
                'payment_mean' => 'CBS-O',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 862.0,
            ],
        ];
        $payload['products'] = [
            [
                'sku' => 'ARCAMRBLINKNR',
                'quantity' => 1,
                'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                'ecotax_price' => 0.15,
                'sorecop_price' => 0,
                'selected_warranties' => [],
                'unit_discount_amount' => 0.0,
                'selling_price_tax_included' => 249.0,
            ],
            [
                'sku' => 'BWCCM74',
                'quantity' => 1,
                'description' => 'Enceinte encastrable BW CCM 7.4',
                'ecotax_price' => 0.15,
                'sorecop_price' => 0,
                'selected_warranties' => [],
                'unit_discount_amount' => 0.0,
                'selling_price_tax_included' => 500.0,
            ],
            [
                'sku' => 'LBCLD25BP',
                'quantity' => 1,
                'description' => 'Paire de pieds noirs laqués pour station multimédia La Boîte Concept LD120 et LD130',
                'ecotax_price' => 0.15,
                'sorecop_price' => 0,
                'selected_warranties' => [],
                'unit_discount_amount' => -40.0,
                'selling_price_tax_included' => 50.0,
            ],
        ];

        return $payload;
    }

    public static function getValidPayloadFromEzl(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::EASY_LOUNGE;
        $payload['original_customer_order_id'] = 'EC1234';
        $payload['source'] = 'web';
        $payload['shipment_method']['shipment_method_id'] = 1;
        $payload['payments'] = [
            [
                'payment_mean' => 'VIR',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 89.3,
            ],
            [
                'payment_mean' => 'VIR',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 1055.69,
            ],
        ];
        $payload['products'] = [
            [
                'sku' => 'ARCAMRBLINKNR',
                'description' => 'Récepteur Audio Bluetooth APTX Arcam rBlink',
                'ecotax_price' => 0,
                'sorecop_price' => 0,
                'quantity' => 1,
                'selling_price_tax_included' => 1200,
                'unit_discount_amount' => -100,
                'selected_warranties' => [],
            ],
            [
                'sku' => 'LBCLD25BP',
                'description' => 'Paire de supports d\'enceintes B-Tech Mountlogic BT77 Noir',
                'ecotax_price' => 0,
                'sorecop_price' => 0,
                'quantity' => 1,
                'selling_price_tax_included' => 40,
                'unit_discount_amount' => 0,
                'selected_warranties' => [],
            ],
        ];

        return $payload;
    }

    public static function getValidPayloadFromCilo(): array
    {
        return [
            'customer_order_id' => 123,
            'original_customer_order_id' => '*********',
            'origin' => CustomerOrderOrigin::CILO,
            'created_at' => '2019-07-18',
            'customer_id' => 1518400,
            'quote_id' => null,
            'ip_address' => '************',
            'source' => null,
            'is_excluding_tax' => true,
            'billing_address' => [
                'company_name' => 'CILO',
                'civility' => 'M.',
                'firstname' => 'Karsten',
                'lastname' => 'Holst',
                'address' => 'Birk Centerpark 40',
                'postal_code' => '7400',
                'city' => 'Herning',
                'country_code' => 'DK',
                'phone' => '',
                'cellphone' => '004553801044',
                'email' => '<EMAIL>',
                'vat_number' => 'NUMTVA',
            ],
            'shipping_address' => [
                'company_name' => '',
                'civility' => 'M.',
                'firstname' => 'Søren G.',
                'lastname' => 'Hansen',
                'address' => 'Strandlyst Alle 3 B',
                'postal_code' => '02670',
                'city' => 'Greve',
                'country_code' => 'DE',
                'phone' => '',
                'cellphone' => '+4522604660',
                'email' => '<EMAIL>',
            ],
            'shipment_method' => [
                'cost' => 0.0,
                'shipment_method_id' => 1,
                'relay_id' => null,
                'chrono_precise_appointment' => null,
            ],
            'payments' => [
                [
                    'payment_mean' => 'CILO',
                    'created_at' => '2019-07-18',
                    'amount' => 1000,
                ],
            ],
            'products' => [
                [
                    'sku' => 'ARCAMRBLINKNR',
                    'description' => 'Récepteur Audio Bluetoøth APTX Arcam rBlink',
                    'ecotax_price' => 0,
                    'sorecop_price' => 0,
                    'quantity' => 1,
                    'selling_price_tax_included' => 1200,
                    'unit_discount_amount' => 0,
                    'selected_warranties' => [],
                ],
                [
                    'sku' => 'LBCLD25BP',
                    'description' => 'Paire de supports d\'enceintes B-Tech Mountlogic BT77 Noir',
                    'ecotax_price' => 0,
                    'sorecop_price' => 0,
                    'quantity' => 1,
                    'selling_price_tax_included' => 40,
                    'unit_discount_amount' => 0,
                    'selected_warranties' => [],
                ],
            ],
        ];
    }

    public static function getValidPayloadFromAmazonDe(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::AMAZON_DE;
        $payload['original_customer_order_id'] = '028-1606154-3786710';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] =
            '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromAmazonEs(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::AMAZON_ES;
        $payload['original_customer_order_id'] = '028-1606154-3786710';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] =
            '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromAmazonFr(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::AMAZON_FR;
        $payload['original_customer_order_id'] = '028-1606154-3786710';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] =
            '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromAmazonIt(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::AMAZON_IT;
        $payload['original_customer_order_id'] = '028-1606154-3786710';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] =
            '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromFnac(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::FNAC;
        $payload['original_customer_order_id'] = 'HAOKCVVBIYX7C';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] = '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromCdiscount(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::CDISCOUNT;
        $payload['original_customer_order_id'] = '24022818344YYGD';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] = '<EMAIL>';

        $payload['products'][] = [
            'sku' => 'INTERETBCA',
            'description' => 'Frais de traitement',
            'ecotax_price' => 0,
            'sorecop_price' => 0,
            'quantity' => 1,
            'selling_price_tax_included' => 3,
            'unit_discount_amount' => 0,
            'selected_warranties' => [],
        ];

        return $payload;
    }

    public static function getValidPayloadFromCultura(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::CULTURA;
        $payload['original_customer_order_id'] = 'IdCustomerOrderCultura';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] = '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromDarty(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::DARTY;
        $payload['original_customer_order_id'] = 'IdCustomerOrderDarty';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] = '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromBoulanger(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::BOULANGER;
        $payload['original_customer_order_id'] = 'IdCustomerOrderBoulanger';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] = '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromRakuten(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::RAKUTEN;
        $payload['original_customer_order_id'] = 'IdCustomerOrderRakuten';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] = '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromConforama(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::CONFORAMA;
        $payload['original_customer_order_id'] = 'IdCustomerOrderConforama';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] = '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromBut(): array
    {
        $payload = self::DEFAULT_PAYLOAD;

        $payload['origin'] = CustomerOrderOrigin::BUT;
        $payload['original_customer_order_id'] = 'IdCustomerOrderBut';
        $payload['billing_address']['email'] = $payload['shipping_address']['email'] = '<EMAIL>';

        return $payload;
    }

    public static function getValidPayloadFromCustomerOrderCloner(): array
    {
        $payload = self::getValidPayloadFromSiteWithAQuoteAndPromoCode();

        $payload['clone_customer_order_id'] = 1;
        $payload['origin'] = CustomerOrderOrigin::BACKOFFICE;

        return $payload;
    }

    public static function generateUniqueOriginalCustomerOrderId(): string
    {
        return str_replace('-', '', Uuid::uuid4()->toString());
    }
}
