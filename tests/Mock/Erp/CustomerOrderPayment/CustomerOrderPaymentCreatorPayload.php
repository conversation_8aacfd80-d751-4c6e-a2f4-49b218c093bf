<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock\Erp\CustomerOrderPayment;

use SonVideo\Erp\Referential\CustomerOrderOrigin;

class CustomerOrderPaymentCreatorPayload
{
    public const VALID_PAYLOAD = [
        'customer_order_id' => 1724028,
        'customer_id' => 970481, // irrelevant but must be present
        'origin' => CustomerOrderOrigin::SITE,
        'payments' => [
            0 => [
                'payment_mean' => 'CBS-O',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 2204.99,
                'extra_data' => [],
            ],
        ],
    ];

    public const VALID_GIFTCARD_PAYLOAD = [
        'customer_order_id' => 1724030,
        'customer_id' => 970481, // irrelevant but must be present
        'origin' => CustomerOrderOrigin::SITE,
        'payments' => [
            0 => [
                'payment_mean' => 'SVDCC   ',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 100,
                'extra_data' => [
                    'amount' => 100,
                    'number' => '77345364361619879088',
                ],
            ],
        ],
    ];

    public const VALID_PRESTO_PAYLOAD = [
        'customer_order_id' => 1724028,
        'customer_id' => 970481, // irrelevant but must be present
        'origin' => CustomerOrderOrigin::SITE,
        'payments' => [
            0 => [
                'payment_mean' => 'PRESTO',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 1000,
                'extra_data' => [],
            ],
        ],
    ];

    public const VALID_CREDIT_CARD_OGONE_TELEPHONE_PAYLOAD = [
        'customer_order_id' => 1724029,
        'customer_id' => 970481, // irrelevant but must be present
        'origin' => CustomerOrderOrigin::SITE,
        'payments' => [
            0 => [
                'payment_mean' => 'TEL     ',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 1000,
                'extra_data' => [],
            ],
        ],
    ];
}
