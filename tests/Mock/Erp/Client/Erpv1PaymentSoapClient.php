<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock\Erp\Client;

use App\Client\AbstractSoapClient;
use SonVideo\Erp\Client\Erpv1PaymentSoapClient as Original;

class Erpv1PaymentSoapClient extends Original
{
    /** {@inheritDoc} */
    public function init(string $end_point, array $options = [])
    {
        return $this;
    }

    /** @return string|void */
    public function __soapCall(string $server, array $params)
    {
        if ('1-2' === $params[0]) {
            return html_entity_decode(
                <<<XML
                &lt;?xml version=&quot;1.0&quot; encoding=&quot;ISO-8859-1&quot; ?&gt;&lt;serveur&gt;&lt;url&gt;&lt;/url&gt;&lt;method&gt;POST&lt;/method&gt;&lt;query_string&gt;&lt;![CDATA[
                \n &lt;form method=&quot;post&quot; action=&quot;https://secure.ogone.com/ncol/test/orderstandard.asp&quot;
                id=&quot;form1&quot; name=&quot;form1&quot;&gt;\n\n&lt;input type=&quot;hidden&quot; name=&quot;PSPID&quot;
                value=&quot;SONVIDEODISTRIBUTION&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;orderID&quot;
                value=&quot;1-2&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;amount&quot; value=&quot;129900&quot;&gt;\n&lt;input
                type=&quot;hidden&quot; name=&quot;currency&quot; value=&quot;EUR&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;language&quot; value=&quot;fr_FR&quot;&gt;\n\n&lt;input type=&quot;hidden&quot; name=&quot;CN&quot;
                value=&quot;Gerard Manvussa&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;EMAIL&quot; value=&quot;<EMAIL>&quot;&gt;\n&lt;input
                type=&quot;hidden&quot; name=&quot;ownerZIP&quot; value=&quot;75018&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;owneraddress&quot; value=&quot;36 rue de la ville en bois&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;ownercty&quot; value=&quot;67&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;ownertown&quot;
                value=&quot;NANTES&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;ownertelno&quot; value=&quot;&quot;&gt;\n&lt;input
                type=&quot;hidden&quot; name=&quot;COM&quot; value=&quot;1-2&quot;&gt;\n\n&lt;input type=&quot;hidden&quot;
                name=&quot;SHASign&quot; value=&quot;afa5746d5c9716210c6c792626114c70189d510a&quot;&gt;\n\n&lt;input
                type=&quot;hidden&quot; name=&quot;accepturl&quot; value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;declineurl&quot; value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;exceptionurl&quot;
                value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;cancelurl&quot; value=&quot;&quot;&gt;\n\n&lt;input
                type=&quot;hidden&quot; name=&quot;operation&quot; value=&quot;RES&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;PMLIST&quot; value=&quot;VISA;MasterCard;CB&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;PM&quot; value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;BRAND&quot;
                value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;TXTOKEN&quot; value=&quot;&quot;&gt;\n\n&lt;input
                type=&quot;hidden&quot; name=&quot;TP&quot; value=&quot;&quot;&gt;\n\n&lt;input type=&quot;hidden&quot;
                name=&quot;PARAMPLUS&quot; value=&quot;PSPID=SONVIDEODISTRIBUTION&amp;MOYEN=CBS-O&quot;&gt;\n\n&lt;!--\n&lt;input
                type=&quot;hidden&quot; name=&quot;TITLE&quot; value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;BGCOLOR&quot; value=&quot;&quot;&gt;\n\n&lt;input type=&quot;hidden&quot; name=&quot;TXTCOLOR&quot;
                value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;TBLBGCOLOR&quot; value=&quot;&quot;&gt;\n&lt;input
                type=&quot;hidden&quot; name=&quot;TBLTXTCOLOR&quot; value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;BUTTONBGCOLOR&quot; value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;BUTTONTXTCOLOR&quot;
                value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;LOGO&quot; value=&quot;&quot;&gt;\n&lt;input
                type=&quot;hidden&quot; name=&quot;FONTTYPE&quot; value=&quot;&quot;&gt;\n\n&lt;input type=&quot;hidden&quot;
                name=&quot;PM&quot; value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;BRAND&quot;
                value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;WIN3DS&quot; value=&quot;&quot;&gt;\n&lt;input
                type=&quot;hidden&quot; name=&quot;PMLIST&quot; value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;PMListType&quot; value=&quot;&quot;&gt;\n\n&lt;input type=&quot;hidden&quot; name=&quot;homeurl&quot;
                value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot; name=&quot;catalogurl&quot; value=&quot;&quot;&gt;\n\n&lt;input
                type=&quot;hidden&quot; name=&quot;COMPLUS&quot; value=&quot;&quot;&gt;\n\n&lt;input type=&quot;hidden&quot;
                name=&quot;PARAMVAR&quot; value=&quot;&quot;&gt;\n\n\n&lt;input type=&quot;hidden&quot; name=&quot;USERID&quot;
                value=&quot;&quot;&gt;\n\n&lt;input type=&quot;hidden&quot; name=&quot;Alias&quot; value=&quot;&quot;&gt;\n&lt;input
                type=&quot;hidden&quot; name=&quot;AliasUsage&quot; value=&quot;&quot;&gt;\n&lt;input type=&quot;hidden&quot;
                name=&quot;AliasOperation&quot; value=&quot;&quot;&gt;--&gt;\n&lt;/form&gt;\n&lt;script language=&quot;javascript&quot;&gt;function
                valid_form(){document.form1.submit();}setTimeout(&quot;valid_form()&quot;, 200);&lt;/script&gt;]]&gt;&lt;/query_string&gt;&lt;/serveur&gt;
                XML
                ,
            );
        }
    }

    public function logRequestAndResponse(string $status = 'success'): AbstractSoapClient
    {
        return $this;
    }
}
