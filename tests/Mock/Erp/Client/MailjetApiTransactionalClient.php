<?php

namespace App\Tests\Mock\Erp\Client;

use Mailjet\Request;
use Mailjet\Response;
use SonVideo\Erp\Client\MailjetApiTransactionalClient as Original;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

class MailjetApiTransactionalClient extends Original
{
    public function get($resource, array $args = [], array $options = [])
    {
        $request = new Request([], '', '', [], [], '');

        $body = self::getSavedResult($resource[0], $args['id']);

        if (!is_null($body)) {
            $response = new \GuzzleHttp\Psr7\Response(200, [], json_encode($body, JSON_THROW_ON_ERROR));

            return new Response($request, $response);
        }

        return parent::get($resource, $args, $options);
    }

    /**
     * getSavedResult.
     *
     * @return mixed|null
     */
    public static function getSavedResult(string $resource, string $id)
    {
        $cache = self::getCache();
        $mock_result = $cache->getItem(self::getItemName($resource, $id));
        if ($mock_result->isHit()) {
            return json_decode($mock_result->get(), true, 512, JSON_THROW_ON_ERROR);
        }

        $cache = self::getCache('../');
        $mock_result = $cache->getItem(self::getItemName($resource, $id));
        if ($mock_result->isHit()) {
            return json_decode($mock_result->get(), true, 512, JSON_THROW_ON_ERROR);
        }

        return null;
    }

    /**
     * savedResult.
     *
     * @param mixed $expected_result
     */
    public static function savedResult(string $resource, string $id, $expected_result): void
    {
        $cache = self::getCache();
        $mock_result = $cache->getItem(self::getItemName($resource, $id));
        $mock_result->set($expected_result);
        $cache->save($mock_result);
    }

    /**
     * getCache.
     *
     * Return cache object.
     */
    private static function getCache(string $app_dir = ''): FilesystemAdapter
    {
        return new FilesystemAdapter('behat_test', 0, $app_dir . 'var/cache/test');
    }

    /**
     * getItemName.
     *
     * @param string $args
     */
    private static function getItemName(string $resource, string $id): string
    {
        return sprintf('%s.%s', $resource, $id);
    }

    /**
     * Clear all the cache test.
     * Use it with caution.
     */
    public static function clearCache(): void
    {
        static::getCache()->clear();
    }
}
