<?php

namespace App\Tests\Mock\Aws\Sns;

use Aws\Result;
use Aws\Sns\SnsClient;

/**
 * Mock pour le client SNS d'AWS.
 * Cette classe permet de simuler les appels au service SNS d'AWS dans les tests.
 */
class SnsClientMock extends SnsClient
{
    private static array $mock_responses = [];

    public function __construct(array $config = [])
    {
    }

    /**
     * Configure une réponse mock pour une méthode spécifique.
     *
     * @param string          $method_name Le nom de la méthode à mocker
     * @param Result|callable $response    La réponse à retourner ou une fonction de callback
     */
    public static function setMockResponse(string $method_name, $response): void
    {
        self::$mock_responses[$method_name] = $response;
    }

    /** {@inheritDoc} */
    public function __call($name, array $args)
    {
        if (isset(self::$mock_responses[$name])) {
            $response = self::$mock_responses[$name];

            if ($response instanceof Result) {
                return $response;
            }

            if (is_callable($response)) {
                return $response($args);
            }
        }

        return new Result([]);
    }

    /**
     * Crée un topic SNS.
     *
     * @param array $args Les arguments de la méthode createTopic
     */
    public function createTopic(array $args): Result
    {
        return $this->__call('createTopic', [$args]);
    }

    /**
     * Publie un message sur un topic SNS.
     *
     * @param array $args Les arguments de la méthode publish
     */
    public function publish(array $args): Result
    {
        return $this->__call('publish', [$args]);
    }

    /**
     * S'abonne à un topic SNS.
     *
     * @param array $args Les arguments de la méthode subscribe
     */
    public function subscribe(array $args): Result
    {
        return $this->__call('subscribe', [$args]);
    }
}
