<?php

namespace App\Tests\Mock\Aws\Sqs;

use Aws\Result;
use Aws\Sqs\SqsClient;

/**
 * Mock pour le client SQS d'AWS.
 * Cette classe permet de simuler les appels au service SQS d'AWS dans les tests.
 */
class SqsClientMock extends SqsClient
{
    private static array $mock_responses = [];

    public function __construct(array $config = [])
    {
    }

    /**
     * Configure une réponse mock pour une méthode spécifique.
     *
     * @param string          $method_name Le nom de la méthode à mocker
     * @param Result|callable $response    La réponse à retourner ou une fonction de callback
     */
    public static function setMockResponse(string $method_name, $response): void
    {
        self::$mock_responses[$method_name] = $response;
    }

    /** {@inheritDoc} */
    public function __call($name, array $args)
    {
        if (isset(self::$mock_responses[$name])) {
            $response = self::$mock_responses[$name];

            if ($response instanceof Result) {
                return $response;
            }

            if (is_callable($response)) {
                return $response($args);
            }
        }

        return new Result([]);
    }

    /**
     * Récupère l'URL d'une file SQS.
     *
     * @param array $args Les arguments de la méthode getQueueUrl
     */
    public function getQueueUrl(array $args): Result
    {
        return $this->__call('getQueueUrl', [$args]);
    }

    /**
     * Crée une nouvelle file SQS.
     *
     * @param array $args Les arguments de la méthode createQueue
     */
    public function createQueue(array $args): Result
    {
        return $this->__call('createQueue', [$args]);
    }

    /**
     * Récupère les attributs d'une file SQS.
     *
     * @param array $args Les arguments de la méthode getQueueAttributes
     */
    public function getQueueAttributes(array $args): Result
    {
        return $this->__call('getQueueAttributes', [$args]);
    }

    /**
     * Définit les attributs d'une file SQS.
     *
     * @param array $args Les arguments de la méthode setQueueAttributes
     */
    public function setQueueAttributes(array $args): Result
    {
        return $this->__call('setQueueAttributes', [$args]);
    }

    /**
     * Reçoit des messages d'une file SQS.
     *
     * @param array $args Les arguments de la méthode receiveMessage
     */
    public function receiveMessage(array $args): Result
    {
        return $this->__call('receiveMessage', [$args]);
    }

    /**
     * Supprime un message d'une file SQS.
     *
     * @param array $args Les arguments de la méthode deleteMessage
     */
    public function deleteMessage(array $args): Result
    {
        return $this->__call('deleteMessage', [$args]);
    }

    /**
     * Modifie la visibilité d'un message dans une file SQS.
     *
     * @param array $args Les arguments de la méthode changeMessageVisibility
     */
    public function changeMessageVisibility(array $args): Result
    {
        return $this->__call('changeMessageVisibility', [$args]);
    }
}
