<?php
/*
 * This file is part of front-cms package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock;

use Psr\Cache\InvalidArgumentException;
use SonVideo\Synapps\Client\RpcClientService as BaseRpcClientService;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

/**
 * Class RpcClientServiceMock.
 *
 * <AUTHOR> <<EMAIL>>
 */
class RpcClientServiceMock extends BaseRpcClientService
{
    /**
     * call.
     *
     * If result is found in saved result, method returns it.
     * Otherwise a real call is done.
     */
    public function call(string $service, string $method, array $args = []): array
    {
        self::saveCall($service, $method, $args);

        $result = self::getSavedResult($service, $method);
        if (!is_null($result)) {
            return ['result' => $result];
        }

        return parent::call($service, $method, $args);
    }

    /**
     * getSavedResult.
     *
     * @return mixed|null
     */
    public static function getSavedResult(string $service, string $method)
    {
        $cache = self::getCache();
        $mock_result = $cache->getItem(self::getItemName($service, $method));
        if ($mock_result->isHit()) {
            return json_decode($mock_result->get(), true, 512, JSON_THROW_ON_ERROR);
        }

        $cache = self::getCache('../');
        $mock_result = $cache->getItem(self::getItemName($service, $method));
        if ($mock_result->isHit()) {
            return json_decode($mock_result->get(), true, 512, JSON_THROW_ON_ERROR);
        }

        return null;
    }

    /**
     * savedResult.
     *
     * @param mixed $expected_result
     */
    public static function savedResult(string $service, string $method, string $expected_result): void
    {
        $cache = self::getCache();
        $mock_result = $cache->getItem(self::getItemName($service, $method));
        $mock_result->set($expected_result);
        $cache->save($mock_result);
    }

    /** getItemName */
    private static function getItemName(string $service, string $method): string
    {
        return sprintf('%s.%s', $service, str_replace(':', '.', $method));
    }

    /**
     * getCache.
     *
     * Return cache object.
     */
    private static function getCache(string $app_dir = ''): FilesystemAdapter
    {
        return new FilesystemAdapter('behat_test', 0, $app_dir . 'var/cache/test');
    }

    /**
     * Clear all the cache test.
     * Use it with caution.
     */
    public static function clearCache(): void
    {
        static::getCache()->clear();
    }

    /** @throws InvalidArgumentException */
    protected static function saveCall(string $service, string $method, array $args)
    {
        $cache = self::getCache();
        $payload = ['service' => $service, 'method' => $method, 'args' => $args];

        $last_call = $cache->getItem('last_rpc_call');
        $last_call->set($payload);
        $cache->save($last_call);

        $last_service_call = $cache->getItem(sprintf('last_rpc_call__%s', $service));
        $last_service_call->set($payload);
        $cache->save($last_service_call);

        $last_method_call = $cache->getItem(sprintf('last_rpc_call__%s_%s', $service, str_replace(':', '_', $method)));
        $last_method_call->set($payload);
        $cache->save($last_method_call);
    }

    /** @throws InvalidArgumentException */
    public static function getLastCall(string $service = '', string $method = ''): ?array
    {
        $item_key = 'last_rpc_call';
        if ('' !== $service) {
            $item_key .= '__' . $service;
        }

        if ('' !== $method) {
            $item_key .= '_' . str_replace(':', '_', $method);
        }

        $cache = self::getCache();
        $last_call = $cache->getItem($item_key);

        return $last_call->isHit() ? $last_call->get() : null;
    }
}
