<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock;

use SonVideo\Erp\Document\Manager\HtmlToPdfGenerator;
use Twig\Environment;

class HtmlToPdfGeneratorMock extends HtmlToPdfGenerator
{
    public function __construct(Environment $twig)
    {
        // autowiring of parameter declared in service.yaml does not work
        // just override it as it's not a hard requirement for the test to succeed
        parent::__construct($twig, 'dummy');
    }

    public function generate(array $content, string $template_path, array $options = []): string
    {
        return 'ok';
    }
}
