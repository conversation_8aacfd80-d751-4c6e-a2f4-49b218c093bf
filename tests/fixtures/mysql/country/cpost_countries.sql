INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste, cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport, transport_offre_speciale)
VALUES
  (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1),
  (77, 'GUADELOUPE', 'GP', 'GLP', 312, 'France', 'oui', 'oui', 'non', 'DOM', 4, '^971[0-9]{2}$', '971 suivi de 2 chiffres', 1, 0),
  (84, 'GUYANE FRANÇAISE', 'GF', 'GUF', 254, 'France', 'oui', 'oui', 'non', 'DOM', 5, '^973[0-9]{2}$', '973 suivi de 2 chiffres', 1, 0),
  (125, 'MARTINIQUE', 'MQ', 'MTQ', 474, 'France', 'oui', 'oui', 'non', 'D<PERSON>', 6, '^972[0-9]{2}$', '972 suivi de 2 chiffres', 1, 0),
  (128, 'MAYOTTE', 'YT', 'MYT', 175, 'France', 'oui', 'oui', 'non', 'TOM', 7, '^976[0-9]{2}$', '976 suivi de 2 chiffres', 1, 0),
  (141, 'NOUVELLE CALÉDONIE', 'NC', 'NCL', 540, 'France', 'oui', 'oui', 'non', 'TOM', 8, '^988[0-9]{2}$', '988 suivi de 2 chiffres', 1, 0),
  (156, 'POLYNÉSIE FRANÇAISE', 'PF', 'PYF', 258, 'France', 'oui', 'oui', 'non', 'TOM', 9, '^987[0-9]{2}$', '987 suivi de 2 chiffres', 1, 0),
  (165, 'RÉUNION', 'RE', 'REU', 638, 'France', 'oui', 'oui', 'non', 'DOM', 10, '^974[0-9]{2}$', '974 suivi de 2 chiffres', 1, 0),
  (168, 'SAINT PIERRE ET MIQUELON', 'PM', 'SPM', 666, 'France', 'oui', 'oui', 'non', 'TOM', 11, '^975[0-9]{2}$', '975 suivi de 2 chiffres', 1, 0),
  (210, 'WALLIS ET FUTUNA (Îles)', 'WF', 'WLF', 876, 'France', 'oui', 'oui', 'non', 'TOM', 12, '^986[0-9]{2}$', '986 suivi de 2 chiffres', 1, 0),
  (19, 'BELGIQUE', 'BE', 'BEL', 56, 'Europe', 'oui', 'oui', 'oui', null, 13, '^[0-9]{4}$', '4 chiffres', 1, 0),
  (113, 'LUXEMBOURG', 'LU', 'LUX', 442, 'Europe', 'oui', 'oui', 'oui', null, 14, '^[0-9]{4}$', '4 chiffres', 1, 1),
  (132, 'MONACO', 'MC', 'MCO', 492, 'France', 'oui', 'oui', 'oui', null, 999, '^98000$', '98000 obligatoirement', 1, 1),
  (196, 'TERRITOIRES ANTARCTIQUES FRANÇAIS', 'TF', 'ATF', 260, 'France', 'oui', 'oui', 'non', 'TOM', 999, '^984[0-9]{2}$', '984 suivi de 2 chiffres', 0, 0),
  (215, 'SAINT-MARTIN (PARTIE FRANÇAISE)', 'MF', 'MAF', 663, 'France', 'oui', 'oui', 'non', null, 999, null, '', 1, 0),
  (217, 'SAINT-BARTHELEMY', 'BL', 'BLM', 652, 'France', 'oui', 'oui', 'non', null, 999, null, '', 1, 0),
  (5, 'ALLEMAGNE', 'DE', 'DEU', 276, 'Europe', 'oui', 'oui', 'oui', null, 999, '^[0-9]{5}$', '5 chiffres', 1, 0),
  (6, 'ANDORRE', 'AD', 'AND', 20, 'Europe', 'oui', 'oui', 'non', null, 999, '^AD[0-9]{3}$', 'AD suivi de 3 chiffres', 1, 1),
  (14, 'AUTRICHE', 'AT', 'AUT', 40, 'Europe', 'oui', 'oui', 'oui', null, 999, '^[0-9]{4}$', '4 chiffres', 1, 0),
  (29, 'BULGARIE', 'BG', 'BGR', 100, 'Europe', 'oui', 'oui', 'oui', null, 999, '^[0-9]{4}$', '4 chiffres', 0, 0),
  (41, 'CHYPRE', 'CY', 'CYP', 196, 'Europe', 'oui', 'oui', 'oui', null, 999, null, '', 0, 0),
  (49, 'CROATIE', 'HR', 'HRV', 191, 'Europe', 'oui', 'oui', 'oui', null, 999, '^[0-9]{5}$', '5 chiffres', 0, 0),
  (52, 'DANEMARK', 'DK', 'DNK', 208, 'Europe', 'oui', 'oui', 'oui', null, 999, '^[0-9]{4}$', '4 chiffres', 1, 0),
  (62, 'ESPAGNE', 'ES', 'ESP', 724, 'Europe', 'oui', 'oui', 'oui', null, 999, '^[0-9]{5}$', '5 chiffres', 1, 0),
  (63, 'ESTONIE', 'EE', 'EST', 233, 'Europe', 'oui', 'oui', 'oui', null, 999, null, '', 0, 0),
  (65, 'FINLANDE', 'FI', 'FIN', 246, 'Europe', 'oui', 'oui', 'oui', null, 999, '^[0-9]{5}$', '5 chiffres', 1, 0)
;
