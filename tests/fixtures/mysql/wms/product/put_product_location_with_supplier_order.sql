INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, id_produit, reference, type, derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva, V_taux_marge, V_taux_marque, V_marge)
VALUES
  (1, 1, 81079, 'ARCAMRBLINKNR2', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
  (1, 1, 81080, 'ARCAMRBLINKNR3', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80)
;

INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status, prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente, prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids, poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur, id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur, V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit, V_qte_dispo_resa, V_delai_lvr, etat_statut, etat_devalorisation, etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert, V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page, url_image, comparateur, recherche, description_panier, description_courte, diagonale, description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max, prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine, code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage, compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants, fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre, rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost, is_main)
 VALUES 
  ('2019-09-03 01:06:38', 1, 0, 81079, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1),
  ('2019-09-03 01:06:38', 1, 0, 81080, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1)
;

INSERT INTO backOffice.paiement_fournisseur (id_paiement_fournisseur, paiement, description)
  VALUES
    (1, 'Virement', 'Virement')
;

INSERT INTO backOffice.delai_paiement_fournisseur (id_delai, delai, description)
  VALUES
    (1, 'Comptant', 'Comptant')
;

INSERT INTO backOffice.commande_fournisseur (id_commande_fournisseur, id_fournisseur, id_depot, date_creation, status, id_paiement_fournisseur, id_delai_paiement_fournisseur, escompte, commentaire)
  VALUES
    (1, 162, 1, DATE(NOW() - INTERVAL 3 MONTH), 'annulee', 1, 1, 'non', ''),
    (2, 162, 1, DATE(NOW() - INTERVAL 3 MONTH), 'en cours', 1, 1, 'non', ''),
    (3, 162, 1, DATE(NOW() - INTERVAL 3 MONTH), 'en cours', 1, 1, 'non', '')
;

INSERT INTO backOffice.produit_commande_fournisseur (id_commande_fournisseur, id_produit, tva, quantite_commandee, quantite_livree, prix_achat, date_livraison_prevue, date_livraison_effective)
  VALUES
    (1, 81079, 0.200, 1, 1.00, 48.54, DATE(NOW() - INTERVAL 3 MONTH), NULL),
    (2, 81080, 0.200, 1, 1.00, 48.54, DATE(NOW() - INTERVAL 3 MONTH), DATE(NOW() - INTERVAL 3 MONTH)),
    (3, 81079, 0.200, 1, 1.00, 48.54, DATE(NOW() - INTERVAL 3 MONTH), DATE(NOW() - INTERVAL 3 MONTH))
;

INSERT INTO backOffice.WMS_product_location (location_id, product_id, delivery_ticket_id, move_mission_id, quantity)
VALUES
  (1, 81079, null, null, 2),
  (1, 81080, null, null, 1)
;

INSERT INTO backOffice.mouvement_stock (type, id_produit, id_emplacement, quantite, commentaire, utilisateur, move_mission_id, id_commande_fournisseur, trigger_actif)
VALUES
  ('entree', 81079, 1, 1, 'initialisation du stock pour les tests', 'backoffice', null, 1, 0),
  ('entree', 81079, 1, 1, 'initialisation du stock pour les tests', 'backoffice', null, 3, 0),
  ('entree', 81080, 1, 1, 'initialisation du stock pour les tests', 'backoffice', null, 2, 0)
;
