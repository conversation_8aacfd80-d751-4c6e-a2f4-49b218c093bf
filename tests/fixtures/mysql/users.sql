-- DEBUG_QUERY
INSERT INTO backOffice.sf_guard_user (id, username, algorithm, salt, password, created_at, last_login, is_active, is_super_admin)
VALUES
    (1, 'admin', 'none', '', '', now(), now(), 1, 1),
    (2, 'gege', 'none', '', '', now(), now(), 1, 1),
    (5, 'jojo', 'none', '', '', now(), now(), 1, 1),
    (1628, 'henri.baeyens', 'none', '', '', now(), now(), 1, 1)
;

-- DEBUG_QUERY
INSERT INTO backOffice.sf_guard_user_profile (id, societe, site, usr_societe, titre, civilite, nom, prenom, email, poste, poste_sda, employe, signature, seller_commission_role)
VALUES
    (1, 'Son Video Distribution', 'Champigny', 1, 'Administrateur', '<PERSON>.', 'Admin', 'Seigneur', '<EMAIL>', '6666', 1, 1, 'Le roi du système', 'RETAIL_STORE_MANAGER'),
    (2, 'Son Video Distribution', 'Champigny', 1, 'Stagiaire', 'M.', 'Manvussa', 'Gérard', '<EMAIL>', '', 0, 1, 'Le roi du vélo', 'RETAIL_STORE_SELLER'),
    (5, 'Son Video Distribution', 'Champigny', 1, 'Commercial', 'M.', 'Térieur', 'Alex', 'alex.té*********************', '', 0, 1, 'Le roi du pétrole', 'BUSINESS_DEPARTMENT_SELLER'),
    (1628, 'Son Video Distribution', 'Champigny', 1, '', 'Mme', 'Baeyens', 'Henri', '<EMAIL>', '', 0, 1, '', null)
;
