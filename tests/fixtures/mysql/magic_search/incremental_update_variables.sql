UPDATE backOffice.BO_SYS_variable
SET BO_SYS_datetime = now() - INTERVAL 12800 SECOND
WHERE id = 29;

UPDATE backOffice.BO_SYS_variable
SET BO_SYS_datetime = now() - INTERVAL 25600 SECOND
WHERE id = 30;

UPDATE backOffice.article
SET date_creation = now() - INTERVAL 16 SECOND
WHERE id_produit = 81078;

UPDATE backOffice.article
SET modif_date = now() - INTERVAL 64 SECOND
WHERE id_produit = 81123;

UPDATE backOffice.produit
SET derniere_actualisation = now() - INTERVAL 8 SECOND
WHERE id_produit = 13895;
