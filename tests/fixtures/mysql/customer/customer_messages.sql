INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste, cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport, transport_offre_speciale)
VALUES
    (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;

INSERT INTO backOffice.prospect (id_prospect, cnt_email, cnt_lvr_email, email, cnt_prenom, cnt_nom, cnt_societe, cnt_code_postal, cnt_telephone, cnt_mobile)
VALUES
    (2, '<EMAIL>', '<EMAIL>', '<EMAIL>', '<PERSON>', 'Tabouillot', 'SVD', '44100', '0123456789', '0678912345'),
    (3, '<EMAIL>', '<EMAIL>', '<EMAIL>', '<PERSON>', 'Terrie<PERSON>', '<PERSON><PERSON>', '44100', '0123456789', '0678912345'),
    (4, '<EMAIL>', '<EMAIL>', '<EMAIL>', 'Clea', 'Molette', 'SVD', '44100', '0123456789', '0678912345'),
    (5, '<EMAIL>', '<EMAIL>', '<EMAIL>', 'Gérard', 'Menfin', 'SVD', '44100', '0123456789', '0678912345')
;

INSERT INTO backOffice.TC_type (id, nom, id_utilisateur_defaut, limite)
VALUES
    (6, 'Un problème sur le site ?', 1, 24),
    (16, 'Suivi commande', 2, 24)
;


INSERT INTO backOffice.PCT_message_prospect (id, id_prospect, origine, date_creation, destinataire, expediteur, sujet, corps, derniere_modification, id_type, parent_id)
VALUES (1, 2, 'client', '2009-01-09 12:06:37', '<EMAIL>', '<EMAIL>', 'Un problème sur le site ?', 'Ceci est un test Florian pour l\'équipe informatique :-)', '2009-01-09 12:06:37', 16, null),
       (2, 2, 'svd', '2009-01-09 12:10:27', '<EMAIL>', '<EMAIL>', 'Une question home-cinéma ?', 'Bonjour, Je voudrais transformer mon système HIFI e', '2009-01-09 12:10:27', 6, null),
       (3, 2, 'svd', '2009-01-09 12:25:26', '<EMAIL>', '<EMAIL>', 'Un problème sur le site ?', 'Ceci est un test', '2009-01-09 12:25:26', 6, 1),
       (4, 3, 'svd', '2009-01-09 12:26:29', '<EMAIL>', '<EMAIL>', 'Re : Un problème sur le site ?', 'Ceci est un test Ok bien reçu', '2009-01-09 12:26:29', 6, null);

