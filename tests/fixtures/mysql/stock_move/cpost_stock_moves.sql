INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste,
                             cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport,
                             transport_offre_speciale)
  VALUES (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;

INSERT INTO backOffice.CTG_TXN_domaine (id, modified_at, trigger_actif, trigger_actif2, domaine, espace, domaine_btq, rang, url_page, menu, comprendre_pour_choisir, meta_description, presentation)
  VALUES
  (3, '2022-01-13 16:50:27', 1, 0, 'Haute-fidélité', 0, 'Audio Vidéo', 2, '/Conseil/Hifi/Hifi.html', 1, 1, 'Amplis hi-fi, enceintes et haut-parleurs, DAC Audio et lecteurs CD mais aussi les platines vinyle : tout l''univers de la haute-fidélité, les conseils en prime.', ''),
  (6, '2022-01-13 16:50:27', 1, 0, 'Accessoires', 0, 'Accessoires', 8, '/Rayons/Accessoires/index.html', 1, 1, 'Télécommandes, antennes, casques sans fil, câbles, adaptateurs et meubles TV/Video mais aussi les support muraux et écrans de projection.', ''),
  (12, '2022-01-13 16:50:27', 1, 0, 'Services', 0, 'Services', 14, null, 0, 1, '', null),
  (13, '2022-01-13 16:50:27', 1, 0, 'Indéfini', 0, 'Indéfini', 15, null, 0, 1, '', null)
;

INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, modified_at, trigger_actif, trigger_actif2, neteven_couleur, neteven_poids, neteven_televiseur, neteven_type1, categorie, url_categorie, dft_domaine_id, garantie_5, videoprojecteur, diagonale, id_bbac_categorie, port_facture, port_facture_tva, export, section, url_section, typologie, critere_section_sommaire, export_amazon, keyword_amazon, categorie_amazon, hors_gabarit, hors_gabarit_poids_seuil, deb_nomenclature, url_page, pixmania_segment_id, mesure_diagonale, mesure_longueur, code_type_produit_presto, code_douanier)
 VALUES 
  (18, '2022-01-13 16:50:27', 1, 0, 1, 0, 0, 1, 'Câbles audio', '', 6, 'non', 'non', 'non', 1, 7.90, 0.200, 'oui', '', '', '', 'marque', '1', 'câble', 'MP3, Audio portable, Hi-fi', 'N', null, '85444991', '/Rayons/Cables-audio.html', 118, 0, 1, '610', '85444991'),
  (70, '2022-01-13 16:50:27', 1, 0, 1, 0, 0, 1, 'Services', '', 12, 'non', 'non', 'non', 1, 0.00, 0.200, 'non', '', '', '', 'marque', '0', '', '', 'N', null, '', null, null, 0, 0, '610', '49111090')
;

INSERT INTO backOffice.warranty_type (type, label, description)
VALUES
    ('NON', 'Aucune', 'A utiliser pour forcer le fait de ne pas avoir de garantie et ne pas utiliser la règle parente'),
    ('SON', 'Son', 'Garantie pour les produits appartenant à la famille du son'),
    ('VIDEO', 'Vidéo', 'Garantie pour les produits de diffusion vidéo')
;

INSERT INTO backOffice.CTG_TXN_souscategorie (id, modified_at, trigger_actif, trigger_actif2, souscategorie, url_page, port_facture, port_facture_tva, dft_categorie_id, reevoo, rue_du_commerce, url_nav, id_domaine, id_domaine_2, id_categorie_ebay, id_categorie_boutique_ebay, pixmania_segment_id, hors_gabarit, illustration, redoute_nomenclature_node)
  VALUES
    (57, '2022-01-13 16:50:27', 1, 1, 'Câbles d''enceintes montés', '/Rayons/Cables/EspaceCable/CablesA_Enc_Airloc.html', 7.99, 0.200, 18, 1, null, '/Parts/Nav/NavR_Cable_EnceintesPlug.html', 3, 13, '137917', '1', 118, 0, '', ''),
    (296, '2022-01-13 16:50:27', 1, 0, 'Catalogues et parutions', null, 0.00, 0.200, 70, 0, null, null, null, null, null, null, null, 0, 'http://www.son-video.com/images/', null)
;

INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type, derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva, V_taux_marge, V_taux_marque, V_marge)
  VALUES
    (1, 1, 636728278954260, 84048, 'VIARPREMHDHP3M', 'article', '2022-01-24 20:42:35', 57, 18, 6, 0.200, 1.000, 0.500, 47.90),
    (1, 1, 718924882559967, 143169, 'SVCATAPREMHIVER', 'generique', '2022-02-16 16:29:12', 296, 70, 12, 0.200, null, null, -0.01)
;

INSERT INTO backOffice.fournisseur (semaphore, id_fournisseur, fournisseur, status, taux_escompte, id_paiement_fournisseur, id_delai_paiement_fournisseur, remise_sur_tarif, en_compte, encours_maximum, encours_consomme, marque_disponible, marque_en_vente, siege_contact, siege_telephone, siege_mobile, siege_societe, siege_email, siege_fax, siege_site, siege_ville, siege_code_postal, siege_pays, siege_adresse, siege_adresse1, commercial_contact, commercial_telephone, commercial_mobile, commercial_email, comptabilite_contact, comptabilite_telephone, comptabilite_mobile, comptabilite_email, technique_contact, technique_telephone, technique_mobile, technique_email, commentaire, id_pays_origine, franco, V_delai_lvr_moyen, frais_port, numero_compte, login, pass, fermeture, SIREN, siret, intracom, ape, hors_delai_auto, reliquat_attente_auto)
VALUES
  (943050784937153, 1, 'Indefini', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'RiRi', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0)
;

INSERT INTO backOffice.marque (semaphore, id_marque, marque, logo, status, importateur, histoire, url_source_doc, url_source_image, specialite, produit_de_reference, gamme_qualite, public, type_distribution, avis, a_savoir, id_pays, tarif_base_prix_achat_tarif, prix_achat_tarif_prix_vente, etiquetage, en_compte, id_marque_pixmania, V_nb_avis, V_moyenne_avis, V_nb_recommandation, id_redoute, keyword, garanti, meta_description)
VALUES
  (244836806547676, 1219, 'SVD Boutique', null, 'oui', '', '', '', '', '', '', 0, '', '', '', '', 40, 0.000, 0.000, 'non', 'true', null, 0, null, 0, null, '', '', '')
;

INSERT INTO backOffice.couleur (semaphore, id_couleur, code, couleur, url_image, rang, id_parent, parent, updated_at)
VALUES
  (914281290876010, 1, 'XXX', 'indéfinie', '', 0, 0, 0, '2019-03-01 10:35:41')
;

INSERT INTO backOffice. article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status, prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente, prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids, poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur, id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur, V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit, V_qte_dispo_resa, V_delai_lvr, etat_statut, etat_devalorisation, etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert, V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page, url_image, comparateur, recherche, description_panier, description_courte, diagonale, description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max, prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine, code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage, compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants, fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre, rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost, is_main, is_auto_picked)
 VALUES 
  ('2022-01-25 00:56:01', 1, 0, 84048, '2013-07-10', 'oui', 47.92, 47.92, 0.00, 115.00, 115.00, 0.02, 0.00, 0.00, 0.800, 'N', 1, 1, 'N', null, 'VIPREMHDHP', 1, 1219, 'Premium HD HP (2 x 3 m)', 'Premium', 1, 3.00, 9, 12, 'a', null, 9, 0, null, null, null, null, 0, 1, 1, 5, 7, 3, '', 'http://www.son-video.com/Rayons/Cable-enceinte-monte/Viard-Audio-Design-Premium-HD-HP.html', 'http://www.son-video.com/images/dynamic/Cables_audio/articles/Viard_Audio/VIARPREMHDHP3M/Viard-Audio-Premium-HD-HP-2-x-3-m-_P_180.jpg', 'oui', 'oui', 'Paire de câbles d''enceintes montés Viard Audio Design Premium HD HP - Longueur 2 x 3 m', 'Viard Audio Design Premium HD HP (2 x 3 m)', 0, '', 0.000, 0.000, 0, 0, 0.00, 115.00, '2021-10-25', 67, '85444991', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 6, 16, 33, 1, 0, 1, 1, 0),
  ('2022-02-16 16:29:12', 1, 1, 143169, '2017-11-20', 'oui', 0.01, 0.01, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.350, 'N', 1, 1, 'Y', null, null, 1, 1219, 'Catalogue 2021/22', 'Catalogue 2020', 1, 0.00, 18312, 0, 'a', null, 17418, 0, null, null, null, null, 51, 0, 843, 0, 1723, 0, '', '', '', 'non', 'oui', 'SV Catalogue 2021/22', 'SV Catalogue 2021/22', 0, null, 0.000, 0.000, 0, 0, 0.00, 0.01, '2017-12-15', null, '85189000', null, 0.00, 0.000, 0.00, 0.00, 0, 0, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 738, 2839, 7216, 1, 0, 1, 0, 0)
;

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible, generation_transfert_auto,
                                     generation_bl_transfert_auto, expedition_UE_possible, expedition_hors_UE_possible, adresse,
                                     code_postal, ville, id_pays, telephone, email, id_user, description, surcout_emport,
                                     product_emport, nom_transfert, nom_panier, adresse_panier, ville_panier, localisation,
                                     horaire, ordre, horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo,
                                     code, tpe_id, abreviation, use_auto_picking, fiche_url, fiche_url_courte)
  VALUES
    (21, 'Champigny 2', null, 1, 1, 1, 1, 1, '309 avenue Général de Gaulle', '94506', 'Champigny sur Marne', 67, '', '', 2, '', null, null, 'Son-Vidéo.com', '', '', '', '', '', 1, '', '', null, 1, 1, '21', null, 'Cha2', 0, '', ''),
    (10, 'Grenoble', null, 1, 1, 1, 1, 1, '2 boulevard Gambetta', '38000', 'Grenoble', 67, '', '', 2, '', null, null, 'Magasin Son-Vidéo.com', '', '', '', '', '', 1, '', '', null, 1, 1, '10', null, 'Gre', 0, '', '')
;

INSERT INTO backOffice.WMS_area_type (area_type_id, label)
  VALUES
    (3, 'Sortie'),
    (4, 'Stock'),
    (5, 'Prépa'),
    (7, 'Magasin'),
    (8, 'Stock virtuel')
;

INSERT INTO backOffice.WMS_area (area_id, area_type_id, warehouse_id, code, label)
  VALUES
    (21, 7, 10, '10.store', 'Zone magasin (Grenoble)'),
    (33, 4, 21, '21.02', 'Mezzanine 2'),
    (38, 5, 21, '21.prepa', 'Zone de préparation de commande (Champigny 2)'),
    (40, 3, 21, '21.exit', 'Zone de sortie (Champigny 2)'),
    (41, 8, 21, '21.virtual_stock', 'Stock virtuel (Champigny 2)')
;

INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
  VALUES
    (2065, '10.store', 21, 'Emplacement par défaut (Grenoble)', 1),
    (3418, '21.02.e.07.02.01', 33, '21.M2.E$07.02.01', 1),
    (7197, '21.exit', 40, 'Zone de sortie', 1),
    (7198, '21.virtual_stock', 41, 'Stock virtuel', 1),
    (7199, '21.prepa.virtual', 38, 'Zone de préparation virtuelle', 1)
;

INSERT INTO backOffice.transporteur (semaphore, id_transporteur, code, transporteur, liste, is_expressiste,
                                     ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai,
                                     prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
  VALUES
    (20030702191152, 2, 'COLSV', 'Colissimo', 'oui', 0, 9, 1, 'colissimo suivi national ; étiquette à double code barre (SEI)', '', 'FR', 0.000, 50.000, '48 heures', 0.000, 0, 'http://www.coliposte.net/gp/services/main.jsp?m=10003005&colispart=', 'Au depart de champ', 5)
;

INSERT INTO backOffice.prospect (semaphore, id_prospect, identifiant, mot_passe, mot_passe_crypte, date_creation,
                                 date_modification, prescripteur, origine, origine_date, cnt_type, cnt_email,
                                 cnt_societe, cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal,
                                 cnt_ville, cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax,
                                 cnt_numero_tva, cnt_lvr_type, cnt_lvr_email, cnt_lvr_societe, cnt_lvr_civilite,
                                 cnt_lvr_nom, cnt_lvr_prenom, cnt_lvr_adresse, cnt_lvr_code_postal, cnt_lvr_ville,
                                 cnt_lvr_id_pays, cnt_lvr_telephone, cnt_lvr_telephone_bureau, cnt_lvr_mobile,
                                 cnt_lvr_fax, cnt_lvr_numero_tva, site_web, blacklist, date_naissance, profession_id,
                                 envoi_email, email, type, societe, civilite, nom, prenom, envoi_identifiants,
                                 encours_interne, encours_sfac, id_mode_paiement, classification, acceptation_relicat,
                                 franco, RIB, nom_banque, ville_banque, BIC, IBAN, atradius, siren, incoterm,
                                 is_premium, passion, installation, style_musique, genre_cinema, musiques_preferees,
                                 cinemas_preferes, npai)
VALUES
    (0, 1500003, null, 'xxxxxxxx', '0b0cfc07fca81c956ab9181d8576f4a8', '2018-12-24 20:15:03', '2019-09-03 08:32:21', null, null, null, 'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', '4 CHEMIN TRAVERS DES CAILLOUX', '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '', null, 'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', '4 CHEMIN TRAVERS DES CAILLOUX', '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '', null, null, 0, null, null, 1, '<EMAIL>', 'particulier', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', 0, 0, 0, null, null, 0, null, null, null, null, null, null, 'Pas soumis', null, null, 0, null, null, null, null, null, null, 0)
;


INSERT INTO backOffice.commande (semaphore, trigger_actif, trigger_actif2, id_commande, no_commande_origine, creation_origine, date_creation, id_boutique, vendeur, flux, V_statut_traitement, en_attente_de_livraison, facturation_mode, ip, ip_pays, validite_fianet, rappel_client, emport_depot, lvr_particulier, lvr_assurance, detaxe_export, cmd_intragroupe, id_prospect, id_devis, commentaire_facture, expedition_diff_date, cloture_date, cloture_usr, tradedoubler_id, nombre_visite, clef, date_export_status, compteur_paiement, cnt_fct_type, cnt_fct_email, cnt_fct_societe, cnt_fct_civilite, cnt_fct_nom, cnt_fct_prenom, cnt_fct_adresse, cnt_fct_code_postal, cnt_fct_ville, cnt_fct_id_pays, cnt_fct_telephone, cnt_fct_telephone_bureau, cnt_fct_mobile, cnt_fct_fax, cnt_fct_numero_tva, cnt_fct_no_tva_validite, cnt_lvr_type, cnt_lvr_email, cnt_lvr_societe, cnt_lvr_civilite, cnt_lvr_nom, cnt_lvr_prenom, cnt_lvr_adresse, cnt_lvr_code_postal, cnt_lvr_ville, cnt_lvr_id_pays, cnt_lvr_telephone, cnt_lvr_telephone_bureau, cnt_lvr_mobile, cnt_lvr_fax, cnt_lvr_numero_tva, V_montant_ttc, V_montant_ht, V_trcns_montant, V_trcns_montant_accepte, V_trcns_montant_remise, V_pmts_montant, V_pmts_montant_accepte, V_pmts_montant_remise, V_rmbts_montant, V_rmbts_montant_accepte, V_rmbts_montant_remise, V_date_lvr_prevue_max, email_confirmation, sms_confirmation, promotion_id, entete_svd, emport_depot_paris, rdv_socol, id_transporteur, id_pdt_transporteur, tpt_option_code, atradius, relance_compta, id_commande_mere, refacturation, depot_emport, sms_emport, last_modified_at, sales_channel_id)
VALUES
    (894434378384086, 1, 1, 1878297, '1724028', 'backoffice.sonvideopro.com', '2019-09-26 11:14:13', null, null, 'traitement', 'trcn_acceptation_directe', 0, 'bl', '195.7.108.10', null, 'attente', 'N', 'N', 'Y', 'N', 'non', 0, 1500003, null, '', null, null, null, null, 0, null, '1990-01-01', 3, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, null, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, 708.99, 590.83, 708.99, 0.00, 0.00, 708.99, 0.00, 0.00, 0.00, 0.00, 0.00, null, null, null, null, 1, 0, 0, 2, 1, null, 0, 0, null, 0, null, null, '2019-09-26 11:18:33', 1)
;

INSERT INTO backOffice.bon_livraison (id_bon_livraison, id_depot, id_transporteur, id_commande)
  VALUES
    (1, 21, 2, 1878297)
;

INSERT INTO backOffice.produit_bon_livraison(id, id_bon_livraison, id_produit, id_unique, description, quantite,
                                             prix_vente)
  VALUES
    (1, 1, 143169, 1, 'superbe produit', 1, 52.2)
;

INSERT INTO backOffice.facture (id_facture, commentaire, type, id_commande)
  VALUES
    (1, 'superbe facture', 'avoir', 1878297)
;

INSERT INTO backOffice.PMT_carte_cadeau (id, type, no_carte, reference, montant, creation_date, creation_id_commande,
                                         creation_commande_id_unique, expiration_date, impression_date,
                                         utilisation_date, utilisation_id_commande)
  VALUES
    (1, 'carte', '77345364361619879088', 'SVCATAPREMHIVER', 100, '2008-12-02 16:03:09', 1878297, null, '2010-02-02', '2008-12-02 22:36:41', '2008-12-04 17:19:46', 285499)
;


INSERT INTO backOffice.mouvement_stock (id_mouvement_stock, id_produit, id_emplacement, id_emplacement_origine, id_depot,
                                        date_creation, utilisateur, quantite, prix_achat, type, commentaire, id_bon_livraison,
                                        id_commande_fournisseur, id_transfert, id_avoir, id_bon_retour, id_carte_cadeau,
                                        trigger_actif, trigger_actif2, move_mission_id)
  VALUES
    (7551205, 143169, 7198, null, 21, '2022-02-16 16:28:38', 'admin', 1, 42, 'interne', 'test refs externes BL / avoir / carte cadeau', 1, null, null, 1, null, 1, 0, 1, null),
    (7551204, 143169, 7199, null, 21, '2022-02-16 16:28:38', 'admin', -1, null, 'interne', 'Déplacement interne pour le BL: 4718611', null, null, null, null, null, null, 0, 1, null),
    (7551203, 143169, 7199, null, 21, '2022-02-16 16:23:37', 'admin', 1, null, 'interne', 'Déplacement interne pour le BL: 4718611', null, null, null, null, null, null, 0, 1, null),
    (7551202, 84048, 2065, null, 10, '2022-02-16 16:23:37', 'admin', 1, 50, 'interne', 'test filtre depot Grenoble', null, null, null, null, null, null, 0, 1, null),
    (7551201, 143169, 7198, null, 21, '2022-02-16 16:23:24', 'admin', 1, null, 'interne', 'Déplacement interne pour le BL: 4718611', null, null, null, null, null, null, 0, 1, null),
    (7551200, 143169, 7199, null, 21, '2022-02-16 16:23:24', 'admin', -1, null, 'interne', 'Déplacement interne pour le BL: 4718611', null, null, null, null, null, null, 0, 1, null),
    (7551199, 143169, 7199, null, 21, '2022-02-16 16:23:12', 'admin', 1, null, 'interne', 'Déplacement interne pour le BL: 4718611', null, null, null, null, null, null, 0, 1, null),
    (7551198, 143169, 7198, null, 21, '2022-02-16 16:23:12', 'admin', -1, null, 'interne', 'Déplacement interne pour le BL: 4718611', null, null, null, null, null, null, 0, 1, null),
    (7551197, 143169, 3418, null, 21, '2022-02-16 16:22:30', 'gege', 10000, 0.01, 'correction', 'correction stock', null, null, null, null, null, null, 0, 1, null),
    (7551196, 84048, 7197, null, 21, '2022-01-24 20:42:35', 'admin', -1, null, 'sortie', 'Sortie stock lors de la validation du Bon de Livraison.', null, null, null, null, null, null, 0, 1, null)
;

INSERT backOffice.BO_STK_produit_depot (id_produit, id_depot, quantite_stock)
VALUES
    (143169, 21, 10001)
;
