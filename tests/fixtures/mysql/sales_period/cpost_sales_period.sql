INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (1, '2007-06-27 00:00:01', '2007-08-04 23:59:59');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (2, '2008-01-09 08:00:00', '2008-02-16 23:59:59');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (3, '2008-06-25 08:00:00', '2008-08-02 23:59:59');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (4, '2009-01-07 00:00:00', '2009-02-10 23:59:59');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (5, '2009-05-14 00:01:00', '2009-05-20 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (6, '2009-06-24 08:00:00', '2009-07-28 23:15:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (7, '2009-10-02 00:01:00', '2009-10-08 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (8, '2010-01-06 08:00:00', '2010-02-09 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (9, '2010-04-30 00:01:00', '2010-05-06 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (10, '2010-06-30 08:00:00', '2010-08-03 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (11, '2010-11-03 00:01:00', '2010-11-09 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (12, '2011-01-12 08:00:00', '2011-02-15 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (13, '2011-05-13 00:00:00', '2011-05-19 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (14, '2011-06-22 08:00:00', '2011-07-26 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (15, '2011-11-03 00:00:00', '2011-11-09 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (16, '2012-01-11 08:00:00', '2012-02-14 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (17, '2012-04-19 00:00:01', '2012-04-25 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (18, '2012-06-27 00:00:01', '2012-07-31 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (19, '2012-11-07 00:00:01', '2012-11-13 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (20, '2013-01-09 08:00:00', '2013-02-12 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (21, '2013-04-17 00:00:01', '2013-04-23 23:59:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (22, '2013-06-26 08:00:00', '2013-07-30 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (23, '2013-11-05 23:45:00', '2013-11-12 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (24, '2014-01-08 07:45:00', '2014-02-11 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (25, '2014-04-08 23:45:00', '2014-04-22 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (26, '2014-06-25 07:45:00', '2014-07-29 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (27, '2015-01-07 08:00:00', '2015-02-17 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (28, '2015-06-24 08:00:00', '2015-08-04 23:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (29, '2016-01-06 08:00:00', '2016-02-16 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (30, '2016-06-22 08:00:00', '2016-08-02 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (31, '2017-01-11 00:00:01', '2017-02-21 23:59:59');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (32, '2017-06-28 08:00:00', '2017-08-08 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (33, '2018-01-10 08:00:00', '2018-02-20 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (34, '2018-06-27 08:00:00', '2018-08-07 23:30:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (35, '2019-01-09 08:00:00', '2019-02-19 23:59:59');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (36, '2019-06-26 08:00:00', '2019-08-06 22:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (37, '2020-01-08 08:00:00', '2020-02-04 23:50:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (38, '2020-07-15 08:00:00', '2020-08-11 22:00:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (39, '2021-01-20 08:00:00', '2021-03-02 23:45:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (40, '2021-06-30 06:00:00', '2021-07-27 23:45:00');
INSERT INTO backOffice.solde (id, date_debut, date_fin) VALUES (41, '2022-01-12 07:59:00', '2022-02-08 22:44:00');
