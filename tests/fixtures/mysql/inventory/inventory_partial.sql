INSERT INTO backOffice.BO_INV_inventaire (id, inv_date, inv_date_validation, id_depot, collecte_active_id, inv_id_utilisateur_validation, statut, type, name)
VALUES
    (2, '2019-08-08 11:38:32', '2019-08-15 09:43:56', 1, 2, 1000, 'created', 'partial', null),
    (3, '2019-08-08 11:38:32', null, 1, 2, 1000, 'created', 'partial', null),
    (4, '2019-08-08 11:38:32', null, 2, 2, 1000, 'created', 'full', null),
    (5, '2019-08-08 11:38:32', null, 1, 2, 1000, 'on-going', 'full', null),
    (6, '2019-08-08 11:38:32', null, 1, 2, 1000, 'created', 'partial', null),
    (7, '2019-08-08 11:38:32', null, 2, 2, 1000, 'created', 'full', null);

INSERT INTO backOffice.BO_INV_zone_inventory (inventory_id, zone_id)
VALUES
    (2, 1),
    (2, 3),
    (3, 1),
    (3, 3),
    (3, 4)
;

UPDATE backOffice.BO_INV_inventaire SET statut = 'closed' WHERE id = 2;
