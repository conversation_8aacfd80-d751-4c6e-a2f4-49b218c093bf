INSERT INTO backOffice.BO_INV_inventaire (id, inv_date, inv_date_validation, id_depot, collecte_active_id, inv_id_utilisateur_validation)
  VALUES
  (2, '2020-08-08 11:38:32', NULL, 1, 3, NULL),
  (3, '2020-08-08 11:38:32', NULL, 1, NULL, NULL)
;

INSERT INTO backOffice.BO_INV_collecte (id, BO_INV_inventaire_id, collecte_type, numero)
VALUES
  (3, 2, 'global', 1)
;

INSERT INTO backOffice.BO_INV_differential (inventory_id, product_id, brand_id, buy_price, expected_quantity, expected_total_value, counted_quantity, counted_total_value, computed_stock_difference, computed_total_value_difference, is_validated_manually)
VALUES
  (2, 81078, 262, 131.58, 8, 1052.64, 0, 0.00, 0, 0.00, 1)
  ;
