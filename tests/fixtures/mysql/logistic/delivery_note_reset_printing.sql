

INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste,
                             cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport,
                             transport_offre_speciale)
VALUES (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;
INSERT INTO backOffice.CTG_TXN_domaine (id, trigger_actif, trigger_actif2, domaine, espace, domaine_btq, rang, url_page,
                                        menu, comprendre_pour_choisir, meta_description, presentation)
VALUES (3, 1, 0, 'Haute-fidélité', 0, 'Audio Vidéo', 2, '/Conseil/Hifi/Hifi.html', 1, 1, '', null),
       (6, 1, 0, 'Accessoires', 0, 'Accessoires', 8, '/Rayons/Accessoires/index.html', 1, 1, '', ''),
       (13, 1, 0, 'Indéfini', 0, 'Indéfini', 15, null, 0, 1, '', null),
       (15, 1, 0, 'Enceintes', 0, 'tmp', 3, '/Enceintes', 1, 1, '', '')
;
INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, trigger_actif, trigger_actif2, neteven_couleur, neteven_poids, neteven_televiseur, neteven_type1, categorie, url_categorie, dft_domaine_id, garantie_5, videoprojecteur, diagonale, id_bbac_categorie, port_facture, port_facture_tva, export, section, url_section, typologie, critere_section_sommaire, export_amazon, keyword_amazon, categorie_amazon, hors_gabarit, hors_gabarit_poids_seuil, deb_nomenclature, url_page, pixmania_segment_id, mesure_diagonale, mesure_longueur, code_type_produit_presto, code_douanier)
 VALUES 
  (59, 1, 0, 1, 0, 0, 1, 'Meubles et supports', '', 6, 'non', 'non', 'non', 1, 15.00, 0.200, 'oui', '', '', '', 'marque', '1', 'home cinema', 'TV, DVD, Home Cinéma', 'Y', 0, '85299041', '/Rayons/Accessoires/MeubleHifi.html', 3760, 0, 0, '610', '8529904900'),
  (96, 1, 0, 1, 0, 0, 1, 'Distributeurs et transmetteurs', '', 6, 'non', 'non', 'non', 11, 9.90, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'N', null, '85229080', '/Rayons/HomeCinema/Telecommandes/RelaisCGV.html', 3050, 0, 0, '610', '85229080'),
  (137, 1, 1, 1, 1, 0, 1, 'Enceintes', '', 15, 'oui', 'non', 'non', 1, 0.00, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'Y', 17, null, '/Rayons/Hifi/Enceintes/CatEnceintes1.html', null, 0, 0, '613', '85182200'),
  (18, 1, 0, 1, 0, 0, 1, 'Câbles audio', '', 6, 'non', 'non', 'non', 1, 7.90, 0.200, 'oui', '', '', '', 'marque', '1', 'câble', 'MP3, Audio portable, Hi-fi', 'N', null, '85444991', '/Rayons/Cables-audio.html', 118, 0, 1, '610', '85444991')
;
INSERT INTO backOffice.warranty_type (type, label, description)
VALUES
    ('NON', 'Aucune', 'A utiliser pour forcer le fait de ne pas avoir de garantie et ne pas utiliser la règle parente'),
    ('SON', 'Son', 'Garantie pour les produits appartenant à la famille du son'),
    ('VIDEO', 'Vidéo', 'Garantie pour les produits de diffusion vidéo')
;
INSERT INTO backOffice.CTG_TXN_souscategorie (id, trigger_actif, trigger_actif2, souscategorie, url_page, port_facture,
                                              port_facture_tva, dft_categorie_id, reevoo, rue_du_commerce, url_nav,
                                              id_domaine, id_domaine_2, id_categorie_ebay, id_categorie_boutique_ebay,
                                              pixmania_segment_id, hors_gabarit, illustration,
                                              redoute_nomenclature_node)
VALUES (95, 1, 1, 'Enceintes encastrables', '/Rayons/HomeCinema/EnceintesAV/Inwall.html', 7.99, 0.200, 137, 1,
        'MC-11226', '/Parts/Nav/NavR_EnceintesEncastrees.html', 3, 13, '93382', '3', 106, 0,
        'http://www.son-video.com/images/dynamic/Enceintes_encastrables/articles/Artsound/ARTSFL101/Artsound-FL101_P_140.jpg',
        ''),
       (144, 1, 1, 'Pieds d''enceintes', '/Rayons/Accessoires/PiedsEnceinte.html', 9.99, 0.200, 59, 1, 'MC-5335',
        '/Parts/Nav/NavR_Pied_Enceinte.html', 3, 13, '137923', '3', 3764, 0,
        'http://www.son-video.com/images/dynamic/Supports/articles/NorStone/NORSTSTYLUM2NR/NorStone-Stylum-2-Noir_P_180.jpg',
        ''),
       (258, 1, 1, 'Récepteurs Bluetooth', '/systeme-audio-sans-fil/recepteur-bluetooth.html', 3.99, 0.200, 96, 0, null,
        null, null, null, '79323', '1', 9630, 0,
        'http://www.son-video.com/images/dynamic/Distributeurs_et_transmetteurs/articles/Focal/FOCALAPTXUWREC/Focal-Universal-Wireless-APTX-Receiver_P_140.jpg',
        null),
       (56, 1, 1, 'Câbles d''enceintes', '/Rayons/Cables/EspaceCable/CablesA_Enceintes.html', 5.99, 0.200, 18, 1,
        'MC-4702', '/Parts/Nav/NavR_Cable_Enceintes.html', 3, 13, '137917', '1', 118, 0,
        'http://www.son-video.com/images/dynamic/Cables_audio/composes/NORSTCL40010M/NorStone-CL400-Classic-2-x-4-mm2-10-m-_P_180.jpg',
        '')
;
INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type,
                                derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva,
                                V_taux_marge, V_taux_marque, V_marge)
VALUES (1, 1, 305167965875164, 8123, 'FRAIS-PORT', 'generique', '2014-01-02 09:42:07', 95, 137, 12, 0.200, null, null,
        0.00),
       (1, 1, 759744877703250, 50360, 'CARTECADEAUSVD10', 'article', '2019-11-28 11:05:37', 144, 59, 6, 0.200, 7.330,
        0.880, 7.33),
       (1, 1, 404452377375637, 70520, 'YAMEPH100SI', 'article', '2019-12-03 09:26:21', 258, 96, 4, 0.200, 0.721, 0.419,
        34.53),
       (1, 1, 449698107735904, 143088, 'QEDQE6119', 'article', '2019-11-16 13:22:03', 56, 18, 16, 0.200, 1.206, 0.547,
        6.37),
       (1, 1, 36802065593006, 143086, 'QEDQE1455', 'article', '2019-11-16 13:13:34', 56, 18, 6, 0.200, 1.209, 0.547,
        127.22),
       (1, 1, 362767379137471, 5635, 'GRUNMW702700', 'article', '2018-05-16 14:31:56', 144, 59, 6, 0.200, 0.431, 0.301,
        150.84)
;
INSERT INTO backOffice.fournisseur (semaphore, id_fournisseur, fournisseur, status, taux_escompte,
                                    id_paiement_fournisseur, id_delai_paiement_fournisseur, remise_sur_tarif, en_compte,
                                    encours_maximum, encours_consomme, marque_disponible, marque_en_vente,
                                    siege_contact, siege_telephone, siege_mobile, siege_societe, siege_email, siege_fax,
                                    siege_site, siege_ville, siege_code_postal, siege_pays, siege_adresse,
                                    siege_adresse1, commercial_contact, commercial_telephone, commercial_mobile,
                                    commercial_email, comptabilite_contact, comptabilite_telephone, comptabilite_mobile,
                                    comptabilite_email, technique_contact, technique_telephone, technique_mobile,
                                    technique_email, commentaire, id_pays_origine, franco, V_delai_lvr_moyen,
                                    frais_port, numero_compte, login, pass, fermeture, SIREN, siret, intracom, ape,
                                    hors_delai_auto, reliquat_attente_auto)
VALUES (943050784937153, 1, 'Indefini', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN',
        '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
       (943050784937153, 162, 'PPL', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN',
        '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
       (943050784937153, 400, 'LA BOITE CONCEPT', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN',
        '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0)
;
INSERT INTO backOffice.marque (semaphore, id_marque, marque, logo, status, importateur, histoire, url_source_doc,
                               url_source_image, specialite, produit_de_reference, gamme_qualite, public,
                               type_distribution, avis, a_savoir, id_pays, tarif_base_prix_achat_tarif,
                               prix_achat_tarif_prix_vente, etiquetage, en_compte, id_marque_pixmania, V_nb_avis,
                               V_moyenne_avis, V_nb_recommandation, id_redoute, keyword, garanti, meta_description)
VALUES (864984545730971, 262, 'Arcam', 'http://www.son-video.com/images/static/marques/Arcam.gif', 'oui', 'Cabasse', '',
        '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', '')
;
INSERT INTO backOffice.couleur (semaphore, id_couleur, code, couleur, url_image, rang, id_parent, parent, updated_at)
VALUES (914281290876010, 1, 'XXX', 'indéfinie', '', 0, 0, 0, '2019-03-01 10:35:41'),
       (725043410179246, 5, 'NR', 'Noir', 'http://www.son-video.com/images/static/Coloris/Noir.gif', 48, 5, 1,
        '2019-03-01 10:35:41')
;
INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status, prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente, prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids, poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur, id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur, V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit, V_qte_dispo_resa, V_delai_lvr, etat_statut, etat_devalorisation, etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert, V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page, url_image, comparateur, recherche, description_panier, description_courte, diagonale, description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max, prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine, code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage, compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants, fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre, rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost, is_main)
 VALUES 
  ('2019-12-03 09:25:18', 1, 0, 50360, '2008-11-18', 'last', 1.00, 1.00, 0.00, 10.00, 10.00, 0.00, 0.00, 0.00, 0.030, 'Y', 1, 1, 'N', null, null, 1, 262, '10 euros', '10 euros', 1, 0.00, 902, 0, 'a', null, 600, 0, null, null, null, null, 301, 0, 0, 0, 0, 0, '', 'http://www.son-video.com/fo-transition/produits/cartes_cadeau/choix.php', 'http://www.son-video.com/images/dynamic/Carte_cadeau/articles/Carte_Cadeau/CARTECADEAUSVD95/Carte-Cadeau-95-euros_P_180.jpg', 'non', 'non', 'Carte cadeau Son-Vidéo.com d''un montant de 10 euros TTC à valoir sur l''ensemble du site Internet http://www.son-video.com', 'Carte cadeau 10 euros', 0, '', 0.000, 0.000, 0, 0, 0.00, 10.00, '2018-01-19', null, '85235290', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 1, 7, 4, 2, 0, 1, 1),
  ('2019-12-03 09:26:22', 1, 0, 70520, '2011-09-06', 'last', 50.45, 47.89, 0.00, 99.00, 149.00, 0.10, 0.00, 0.00, 0.150, 'N', 1, 1, 'N', null, null, 162, 262, 'EPH-100', 'EPH100', 5, 0.00, 570, 2, 'a', null, 556, 0, null, null, null, null, 12, 1, 3, 0, 17, 2, 'attente confirmation livraison', 'http://www.son-video.com/Rayons/Hifi/Casques/Yamaha-EPH100.html', 'http://www.son-video.com/images/dynamic/Casques/articles/Yamaha/YAMEPH100SI/Yamaha-EPH-100_P_180.jpg', 'oui', 'oui', 'Écouteurs intras Yamaha EPH-100', 'Yamaha EPH-100', 0, '', 0.000, 0.000, 0, 0, 0.00, 145.00, '2014-01-17', 67, '85183095', null, 0.00, 0.000, 0.00, 0.00, 0, 1, 'http://www.i-comparateur.com/search.aspx?q=yamaha+eph-100', 1, null, 0, 17092462, null, null, null, 0, 0.0, 1, 6, 44, 110, 2, 0, 1, 1),
  ('2019-11-17 00:34:01', 1, 0, 143088, '2019-11-16', 'last', 5.28, null, 0.00, 14.00, 14.00, 0.02, 0.00, 0.00, 0.460, 'Y', 1, 1, 'N', null, 'QE6119', 162, 262, 'Performance Audio 40i (1 m)', 'performance', 1, 1.00, 0, 25, 'a', null, 0, null, null, null, null, null, 0, 0, 0, 0, 0, 99, '', 'http://www.son-video.com/Rayons/Cable-Enceinte/QED-Performance-Audio-40.html', '', 'oui', 'oui', 'Câble d''enceinte QED Performance Audio 40i, longueur 1 m', 'QED Performance Audio 40i (1 m)', 0, '', 0.000, 0.000, 0, 0, 0.00, 0.00, null, 67, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 1),
  ('2019-11-17 00:34:01', 1, 0, 143086, '2019-11-16', 'last', 105.26, null, 0.00, 279.00, 279.00, 0.02, 0.00, 0.00, 1.480, 'Y', 1, 1, 'Y', null, 'QE1455', 400, 262, 'XT40i (2 x 5 m)', 'XT40i', 1, 5.00, 0, 1, 'a', null, 0, null, null, null, null, null, 0, 0, 0, 0, 0, 99, '', 'http://www.son-video.com/Rayons/Cable-Enceintes-Montes/QED-XT40.html', '', 'non', 'oui', 'Paire de câbles d''enceintes montés QED XT40i, longueur 5m', 'QED XT40i (2 x 5m)', 0, '', 0.000, 0.000, 0, 0, 0.00, 0.00, null, null, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 1),
  ('2014-07-03 09:06:53', 1, 0, 5635, '2003-01-24', 'last', 350.00, null, 350.00, 599.00, 0.00, 0.00, 0.00, 0.00, 0.000, 'Y', 1, 1, 'N', null, null, 400, 262, 'Sedance 70 MW 70-2700 blanc', null, 1, 70.00, 0, 0, 'a', null, 0, null, null, null, null, null, 0, 0, 0, 0, 0, 1, '', 'http://www.son-video.com/Rayons/Video/TV169/CatTV169.html', 'http://www.son-video.com/images/static/Rayons/Video/TV169/Grundig/MW702700.jpg', 'non', 'non', 'Téléviseur 70 cm - 16/9 Grundig Sedance MW70-2700 gris polaire', '', 70, '', 0.000, 0.000, 0, 0, 0.00, 599.00, null, null, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 2, 0, 1, 1)
;
INSERT INTO backOffice.transporteur (semaphore, id_transporteur, code, transporteur, liste, is_expressiste,
                                     ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai,
                                     prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
VALUES (20030702191152, 2, 'COLSV', 'Colissimo', 'oui', 0, 9, 1,
        'colissimo suivi national ; étiquette à double code barre (SEI)', '', 'FR', 0.000, 50.000, '48 heures', 0.000,
        0, 'http://www.coliposte.net/gp/services/main.jsp?m=10003005&colispart=', '', 5),
       (20030702191206, 5, 'EMPT', 'Emport dépôt', 'oui', 0, 1, 1, '', '', '', 0.000, 0.000, '0', 0.000, 0, null, null,
        1),
       (0, 31, 'EDNA', 'Emport Depot Nantes', 'oui', 0, 2, 0, 'Emport Depot Nantes', '', 'SVD MAG', 0.000, 0.000, '0',
        0.000, 0, null, null, 1),
       (0, 30, 'FREXP', 'France Express', 'oui', 0, 53, 1, 'Geodis / France Express', '', 'France', 0.000, 0.000,
        '48 heures', 0.000, 0, null, null, 1)
;

INSERT INTO backOffice.BO_TPT_PDT_liste (id, transporteur_id, code_produit, libelle_produit, actif, commentaire, type,
                                         mono_colis, spidy_tracking_number_mask)
VALUES (34, 30, 'XPK', 'Express', 1,
        'France Express Livraison le lendemain partout en France. Vous pouvez changer le jour de livraison en répondant au mail/sms de notre transporteur.',
        'messagerie', 0, null)
;

INSERT INTO backOffice.sf_guard_user(id, username, algorithm, salt, password, created_at, last_login, is_active,
                                     is_super_admin)
VALUES (1000, 'backoffice', 'none', '', '', now(), now(), 1, 1)
;
INSERT INTO backOffice.prospect (semaphore, id_prospect, identifiant, mot_passe, mot_passe_crypte, date_creation,
                                 date_modification, prescripteur, origine, origine_date, cnt_type, cnt_email,
                                 cnt_societe, cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal,
                                 cnt_ville, cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax,
                                 cnt_numero_tva, cnt_lvr_type, cnt_lvr_email, cnt_lvr_societe, cnt_lvr_civilite,
                                 cnt_lvr_nom, cnt_lvr_prenom, cnt_lvr_adresse, cnt_lvr_code_postal, cnt_lvr_ville,
                                 cnt_lvr_id_pays, cnt_lvr_telephone, cnt_lvr_telephone_bureau, cnt_lvr_mobile,
                                 cnt_lvr_fax, cnt_lvr_numero_tva, site_web, blacklist, date_naissance, profession_id,
                                 envoi_email, email, type, societe, civilite, nom, prenom, envoi_identifiants,
                                 encours_interne, encours_sfac, id_mode_paiement, classification, acceptation_relicat,
                                 franco, RIB, nom_banque, ville_banque, BIC, IBAN, atradius, siren, incoterm,
                                 is_premium, passion, installation, style_musique, genre_cinema, musiques_preferees,
                                 cinemas_preferes, npai)
VALUES (0, 1500003, null, 'xxxxxxxx', '0b0cfc07fca81c956ab9181d8576f4a8', '2018-12-24 20:15:03', '2019-09-03 08:32:21',
        null, null, null, 'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER',
        '4 CHEMIN TRAVERS DES CAILLOUX', '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '', null,
        'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', '4 CHEMIN TRAVERS DES CAILLOUX',
        '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '', null, null, 0, null, null, 1,
        '<EMAIL>', 'particulier', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', 0,
        0, 0, null, null, 0, null, null, null, null, null, null, 'Pas soumis', null, null, 0, null, null, null, null, null, null,
        0)
;
INSERT INTO backOffice.commande (semaphore, trigger_actif, trigger_actif2, id_commande, no_commande_origine,
                                 creation_origine, date_creation, id_boutique, vendeur, flux, V_statut_traitement,
                                 en_attente_de_livraison, facturation_mode, ip, ip_pays, validite_fianet, rappel_client,
                                 emport_depot, lvr_particulier, lvr_assurance, detaxe_export, cmd_intragroupe,
                                 id_prospect, id_devis, commentaire_facture, expedition_diff_date, cloture_date,
                                 cloture_usr, tradedoubler_id, nombre_visite, clef, date_export_status,
                                 compteur_paiement, cnt_fct_type, cnt_fct_email, cnt_fct_societe, cnt_fct_civilite,
                                 cnt_fct_nom, cnt_fct_prenom, cnt_fct_adresse, cnt_fct_code_postal, cnt_fct_ville,
                                 cnt_fct_id_pays, cnt_fct_telephone, cnt_fct_telephone_bureau, cnt_fct_mobile,
                                 cnt_fct_fax, cnt_fct_numero_tva, cnt_fct_no_tva_validite, cnt_lvr_type, cnt_lvr_email,
                                 cnt_lvr_societe, cnt_lvr_civilite, cnt_lvr_nom, cnt_lvr_prenom, cnt_lvr_adresse,
                                 cnt_lvr_code_postal, cnt_lvr_ville, cnt_lvr_id_pays, cnt_lvr_telephone,
                                 cnt_lvr_telephone_bureau, cnt_lvr_mobile, cnt_lvr_fax, cnt_lvr_numero_tva,
                                 V_montant_ttc, V_montant_ht, V_trcns_montant, V_trcns_montant_accepte,
                                 V_trcns_montant_remise, V_pmts_montant, V_pmts_montant_accepte, V_pmts_montant_remise,
                                 V_rmbts_montant, V_rmbts_montant_accepte, V_rmbts_montant_remise,
                                 V_date_lvr_prevue_max, email_confirmation, sms_confirmation, promotion_id, entete_svd,
                                 emport_depot_paris, rdv_socol, id_transporteur, id_pdt_transporteur, tpt_option_code,
                                 atradius, relance_compta, id_commande_mere, refacturation, depot_emport, sms_emport,
                                 last_modified_at, sales_channel_id)
VALUES (894434378384086, 1, 1, 1712826, '1724028', 'backoffice.sonvideopro.com', '2019-09-26 11:14:13', null, null,
        'traitement', 'trcn_acceptation_directe', 0, 'bl', '195.7.108.10', null, 'attente', 'N', 'N', 'Y', 'N', 'non',
        0, 1500003, null, '', null, null, null, null, 0, null, '1990-01-01', 3, 'particulier', '<EMAIL>', '',
        'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '',
        '0561887801', '', null, null, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond',
        '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, 708.99,
        590.83, 708.99, 0.00, 0.00, 708.99, 0.00, 0.00, 0.00, 0.00, 0.00, null, null, null, null, 1, 0, 0, 2, 1, null,
        0, 0, null, 0, null, null, '2019-09-26 11:18:33', 1)
;

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible,
                                     generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible,
                                     expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone,
                                     email, id_user, description, surcout_emport, product_emport, nom_transfert,
                                     nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre,
                                     horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code,
                                     tpe_id, abreviation)
VALUES (21, 'Champigny', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67,
        '0155091779', '<EMAIL>', 1,
        'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.',
        null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne',
        'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi',
        'de 9h &agrave; 18h30', '', 1, 1, '03', 6100907, 'Cha'),
       (18, 'Nantes', 31, 0, 0, 0, 0, 0, '9 place de la Bourse', '44100', 'Nantes', 67, '0249442402',
        '<EMAIL>', 1,
        'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.',
        null, null, 'Magasin Son-Vidéo.com', 'magasin (Nantes)', '9 place de la Bourse', 'Nantes',
        'https://goo.gl/maps/Cucfvro99n22', 'ouvert du mardi au samedi, 10h - 19h', 99, 'du mardi au samedi',
        'de 10h &agrave; 19h', null, 1, 1, '05', 6297732, 'Nan')
;
INSERT INTO backOffice.WMS_area_type (area_type_id, label)
VALUES (4, 'stock')
;
INSERT INTO backOffice.WMS_area (area_id, area_type_id, warehouse_id, code, label)
VALUES (1, 4, 21, '03.01', 'Petit stock haut'),
       (2, 4, 21, '04.01', 'Tout le stock')
;
INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES (1, '03.01.a.01.01.01', 1, '03.01.A$01.01.01', 1),
       (2, '03.01.a.01.01.02', 1, '03.01.A$01.01.02', 1),
       (3, '04.01.a.01.01.01', 2, '04.01.A$01.01.01', 1)
;
INSERT INTO backOffice.WMS_move_mission_type (move_mission_type_id, code, label)
VALUES (1, 'arrangement', 'rangement')
;
INSERT INTO backOffice.WMS_move_mission (move_mission_id, move_mission_type_id, created_at, assigned_to, assigned_at,
                                         started_at, ended_at, canceled_at, canceled_by, product_id, quantity)
VALUES (1, 1, '2019-11-27 10:38:59', 1000, '2019-11-27 12:47:27', null, null, null, null, 143088, 1)
;
INSERT INTO backOffice.WMS_product_location (location_id, product_id, delivery_ticket_id, move_mission_id, quantity)
VALUES (2, 50360, null, null, 3),
       (3, 70520, null, null, 6),
       (3, 143088, null, 1, 5),
       (1, 143086, null, null, 4)
;

-- [TRANSFERT] Must be created in "tmp" status due to trigger constraint
INSERT INTO backOffice.BO_STK_transfert (id, id_depot_depart, id_depot_arrivee, id_commande, date_creation,
                                         date_cloture, utilisateur_creation, type_transfert, statut, commentaire,
                                         id_pdt_transporteur, bypass, has_been_forced)
VALUES (123456, 21, 18, null, '2019-02-24 11:51:34', null, 'admin', null, 'tmp', null, 34, 0, 0);
-- [TRANSFERT] Then update the status after the row is inserted
UPDATE backOffice.BO_STK_transfert
SET statut = 'au depart'
WHERE id = 123456;

INSERT INTO backOffice.bon_livraison (semaphore, id_bon_livraison, id_depot, id_commande, id_transfert, id_facture,
                                      is_petit_stock, montant_contre_remboursement, date_creation, utilisateur_creation,
                                      status, detaxe_export, id_transporteur, id_pdt_transporteur,
                                      date_export_transporteur, utilisateur_export_transporteur, date_validation,
                                      utilisateur_validation, email_validation, sms_validation, numero_enquete,
                                      date_declaration_perte, utilisateur_declaration_perte, motif_reexpedition,
                                      impression_expedition, impression_date, cnt_type, cnt_email, cnt_societe,
                                      cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal, cnt_ville,
                                      cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax,
                                      cnt_numero_tva, lvr_particulier, lvr_assurance, contact, sav, entete_svd,
                                      scan_depart_c, scan_arrive_p, scan_depart_p, scan_retour_c, numero_cni,
                                      numero_kbis, numero_dbc)
VALUES (125191468862064, 123, 21, 1712826, null, null, 0, null, NOW(), 'backoffice', 'au depart', 'non', 2, 1, null,
        'backoffice', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier',
        '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris',
        67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null),
       (125191468862065, 456, 21, 1712826, null, null, 0, null, NOW(), 'backoffice', 'au depart', 'non', 2, 1, null,
        'backoffice', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier',
        '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris',
        67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null),
       (125191468862066, 789, 21, null, 123456, null, 0, null, NOW(), 'backoffice', 'expedie', 'non', 2, 1, null,
        'backoffice', null, null, null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier',
        '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris',
        67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null)
;

INSERT INTO backOffice.produit_bon_livraison (id, id_bon_livraison, id_produit, id_unique, description, quantite,
                                              prix_vente, tva, remise_type, remise_montant, remise_description,
                                              duree_garantie_ext, prix_garantie_ext, tva_garantie_ext,
                                              commission_garantie_ext, tva_commission_garantie_ext,
                                              vendeur_garantie_ext, duree_garantie_vc, prix_garantie_vc,
                                              commission_garantie_vc, tva_commission_garantie_vc, vendeur_garantie_vc,
                                              poids, nombre_colis)
VALUES (3268077, 123, 8123, 2, 'Frais de port facturés', 1, 5.99, 0.200, null, 0.00, null, null, 0.00, 0.000, 0.00,
        0.000, null, null, 0.00, 0.00, 0.000, null, null, null),
       (3268079, 123, 50360, 2,
        'Carte cadeau Son-Vidéo.com d''un montant de 10 euros TTC à valoir sur l''ensemble du site Internet http://www.son-video.com',
        1, 10.00, 0.200, null, 0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.030,
        1),
       (3268078, 123, 70520, 2, 'Câble d''enceinte QED Performance Audio 40i, longueur 1 m', 1, 99.00, 0.200, null,
        0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1),
       (3268080, 123, 143088, 2, 'Câble d''enceinte QED Performance Audio 40i, longueur 1 m', 15, 99.00, 0.200, null,
        0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1),
       (3268081, 123, 143086, 2, 'Paire de câbles d''enceintes montés QED XT40i, longueur 5m', 1, 99.00, 0.200, null,
        0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1),
       (3268082, 789, 5635, 2, 'Téléviseur 70 cm - 16/9 Grundig Sedance MW70-2700 gris polaire', 1, 99.00, 0.200, null,
        0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1),
       (3268083, 456, 70520, 2, 'Câble d''enceinte QED Performance Audio 40i, longueur 1 m', 1, 99.00, 0.200, null,
        0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.150, 1)
;

INSERT INTO backOffice.delivery_ticket_activity_log (delivery_ticket_id, delivery_ticket_id_without_fk, action,
                                                     description, created_at, created_by, preparation_location)
VALUES (456, 456, 'VALIDATED', null, NOW() + INTERVAL 1 HOUR, 2, null),
       (456, 5635, 'PREPARATION_FINISHED', null, NOW() + INTERVAL 1 HOUR, 2, 7222)
;
