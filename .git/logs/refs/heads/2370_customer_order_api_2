0000000000000000000000000000000000000000 daae441d388a53ff709ca4a575c75cc6403a7592 RomainM <<EMAIL>> 1745424504 +0200	branch: Created from origin/2370_customer_order_api_2
daae441d388a53ff709ca4a575c75cc6403a7592 ab72eb3db515a30950759c081ab0e40cbf584a03 RomainM <<EMAIL>> 1745424544 +0200	commit: feat[support#2370]: use const
ab72eb3db515a30950759c081ab0e40cbf584a03 550bfea7b079d0f416f02091b93c66b3dac87173 RomainM <<EMAIL>> 1745489749 +0200	commit: feat[support#2370]: add relay id
550bfea7b079d0f416f02091b93c66b3dac87173 9b504ceaa1ac15dccc13ba1328cca61ee36e4ca0 RomainM <<EMAIL>> 1745497933 +0200	commit: feat[support#2370]: catch throwable + don't clone chrono precise shipment product
9b504ceaa1ac15dccc13ba1328cca61ee36e4ca0 402b84e6f6b77af966ab04689bbb9b41043ee0b3 RomainM <<EMAIL>> 1745506330 +0200	commit: feat[support#2370]: remove logger
402b84e6f6b77af966ab04689bbb9b41043ee0b3 019037f5e9f0455ba687079b6ec0e2f929e599df RomainM <<EMAIL>> 1745506363 +0200	commit: feat[support#2370]: change log from backoffice to user who cloned order + mock CurrentUser
019037f5e9f0455ba687079b6ec0e2f929e599df 3a79673ca2de45f4c8f5241750b84a631be5aa85 RomainM <<EMAIL>> 1745506693 +0200	commit: feat[support#2370]: add relay_id in tests
3a79673ca2de45f4c8f5241750b84a631be5aa85 645c5aac6b092301794c7b0b8155af7db92c3077 RomainM <<EMAIL>> 1745591355 +0200	commit: feat[support#2370]: remove mock CurrentUser
645c5aac6b092301794c7b0b8155af7db92c3077 f714c98f1cab9434739b0d76e749a36a925647de RomainM <<EMAIL>> 1745594686 +0200	commit (merge): Merge branch 'master' into 2370_customer_order_api_2
