; https://symfony.com/doc/current/performance.html

expose_php = Off
track_errors = Off
html_errors = On
error_log = /dev/stderr
error_reporting = E_ALL
display_errors = On
apc.enable_cli = 1
zend.detect_unicode = 0
realpath_cache_size = 4096K
realpath_cache_ttl = 600
memory_limit = 1024M
short_open_tag = false
upload_max_filesize = 30M
post_max_size = 30M

[Date]
date.timezone = Europe/Paris

[Curl]
; Older debian use an old version of open ssl that fail to correctly use the shared CARoot
; Hence all RPC call fails between apps due to unrecognized SSL certificate issuer
; This does not affect Debian 12 (bookworm) on PHP8
; Beware: This should not ne use on prod
openssl.cafile = "/etc/ssl/certs/ca-certificates.crt"

[SQL]
sql.safe_mode = Off

[mail function]
sendmail_path = /usr/sbin/sendmail -t -i
mail.add_x_header = On

[Pcre]
pcre.jit = 0

[Interbase]
ibase.allow_persistent = 1
ibase.max_persistent = -1
ibase.max_links = -1
ibase.timestampformat = "%Y-%m-%d %H:%M:%S"
ibase.dateformat = "%Y-%m-%d"
ibase.timeformat = "%H:%M:%S"

[Opcache]
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 20000
opcache.memory_consumption = 256
opcache.enable_file_override = 1
