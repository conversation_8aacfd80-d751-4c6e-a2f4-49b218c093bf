ServerName {APP_NAME}
<VirtualHost *>
  ServerAdmin webmaster@localhost
  DocumentRoot /var/www/{APP_NAME}/public

  RewriteEngine On
  RewriteCond /var/www/{APP_NAME}/public%{REQUEST_FILENAME} !-f
  RewriteCond /var/www/{APP_NAME}/public%{REQUEST_FILENAME} !-d
  RewriteRule .* /index.php [QSA]

  RewriteCond %{HTTP:Authorization} ^(.*)
  RewriteRule .* - [e=HTTP_AUTHORIZATION:%1]

  ErrorLog ${APACHE_LOG_DIR}/error.log
  CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
