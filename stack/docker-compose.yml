# Supported by docker compose v2 but bugged in PHPStorm
# https://youtrack.jetbrains.com/issue/WI-73240/Interpreter-from-docker-compose-doesnt-work-if-docker-compose.yml-has-name-but-COMPOSEPROJECTNAME-isnt-set
# Add the environment variable in Interpreter settings: "COMPOSE_PROJECT_NAME=erp-server"
name: 'erp-server'

services:
    # No traefik as MySQL does support SNI until version 8.0.3
    erp-server-mysql-test:
        container_name: erp-server-mysql-test
        restart: unless-stopped
        image: registry.gitlab.com/son-video/docker-stack/mysql:5.7.31
        environment:
            - MYSQL_ROOT_PASSWORD=root
        volumes:
            - erp-server-mysql-test-data:/var/lib/mysql
            - ./mysql/my.cnf:/etc/mysql/my.cnf
        working_dir: /var/log/mysql
        networks:
            - docker-stack_webgateway
        ports:
            - "33061:3306"

    erp-server-php:
        container_name: erp-server-php
        restart: unless-stopped
        # @see https://gitlab.com/son-video/erp-server/container_registry
        image: registry.gitlab.com/son-video/erp-server/dev:7.4
        build:
            context: .
            # Dynamic arguments that can be changed on build
            # @see DockerFile
            # args:
            # PHP_VERSION: 7.4
            # DEBIAN_VERSION: buster
            # PSQL_VERSION: 10
        user: $USER_ID:$GROUP_ID
        volumes:
            - ./../:/var/www/erp-server
            - /etc/passwd:/etc/passwd:ro
            - /etc/group:/etc/group:ro
            - /etc/ssl/certs/ca-certificates.crt:/etc/ssl/certs/ca-certificates.crt:ro
        environment:
            PHP_IDE_CONFIG: ${PHP_IDE_CONFIG:-serverName=docker}
            XDEBUG_MODE: ${XDEBUG_MODE:-off}
            XDEBUG_CONFIG: "client_host=host.docker.internal"
            XDEBUG_TRIGGER: 'yes'
            # Older debian use an old version of open ssl that fail to correctly use the shared CARoot
            # Hence all RPC call fails between apps due to unrecognized SSL certificate issuer
            # This does not affect Debian 12 (bookworm) on PHP8
            # Beware: This should not be use on prod
            CURL_CA_BUNDLE: "/etc/ssl/certs/ca-certificates.crt"
            COMPOSER_HOME: "/var/www/erp-server/.composer"
        working_dir: /var/www/erp-server
        labels:
            - traefik.enable=true
            ## HTTPS
            - traefik.http.middlewares.erp-server-https.redirectscheme.scheme=https
            - traefik.http.routers.erp-server-https.entrypoints=ssl
            - traefik.http.routers.erp-server-https.rule=Host(`erp-server.lxc`)
            - traefik.http.routers.erp-server-https.tls=true
            ## HTTP
            - traefik.http.routers.erp-server.entrypoints=web
            - traefik.http.routers.erp-server.rule=Host(`erp-server.lxc`)
            ## Redirect HTTP -> HTTPS
            - traefik.http.routers.erp-server.middlewares=erp-server-https@docker
            ## PHCSTACK
            - phcstack.main=true
            - phcstack.synapps_app_name=erp
            - phcstack.synapps_app_id=0b80d4e7-69a4-4f3d-84ce-50c8c4d4d5fa
            - phcstack.synapps_app_rpc=https://erp-server.lxc/rpc
        networks:
            - docker-stack_webgateway

    gotenberg:
        container_name: gotenberg
        restart: unless-stopped
        image: thecodingmachine/gotenberg:6
        networks:
            - docker-stack_webgateway

    # You may need to load the database metadata available in ERP Server project in `sql/hasura` directory
    # Metadata can be loaded via the UI here: https://erp-graphql.lxc/console/settings/metadata-actions
    erp-graphql:
        container_name: erp-graphql
        restart: unless-stopped
        image: hasura/graphql-engine:v2.29.0-ce
        environment:
            DEBUG: "true"
            HASURA_GRAPHQL_ENABLE_CONSOLE: "true"
            HASURA_GRAPHQL_DEV_MODE: "true"
            HASURA_GRAPHQL_DATABASE_URL: ****************************************************/svd_erp
            HASURA_GRAPHQL_METADATA_DATABASE_URL: ****************************************************/svd_erp
            HASURA_GRAPHQL_ENABLED_LOG_TYPES: startup, http-log, webhook-log, websocket-log, query-log
            HASURA_GRAPHQL_AUTH_HOOK: https://erp-server.lxc/api/v1/authenticate/hasura
            HASURA_GRAPHQL_ADMIN_SECRET: SuperSecret
            HASURA_GRAPHQL_LOG_LEVEL: warn
            ERP_DATABASE_URL: ****************************************************/svd_erp
            CMS_DATABASE_URL: ****************************************************/svd_cms
            ERP_WEBHOOK_URL: https://erp-server.lxc/api/v1/webhook/hasura
            ERP_WEBHOOK_SECRET: 123soleil
            HAL_WEBHOOK_URL: https://hal.lxc/api/v1/webhook/hasura
            HAL_WEBHOOK_SECRET: 123soleil
        command:
            - graphql-engine
            - serve
        volumes:
            - /etc/ssl/certs/ca-certificates.crt:/etc/ssl/certs/ca-certificates.crt:ro
        labels:
            - traefik.enable=true
            ## HTTPS
            - traefik.http.middlewares.erp-graphql-https.redirectscheme.scheme=https
            - traefik.http.routers.erp-graphql-https.entrypoints=ssl
            - traefik.http.routers.erp-graphql-https.rule=Host(`erp-graphql.lxc`)
            - traefik.http.routers.erp-graphql-https.tls=true
            ## HTTP
            - traefik.http.routers.erp-graphql.entrypoints=web
            - traefik.http.routers.erp-graphql.rule=Host(`erp-graphql.lxc`)
            - traefik.http.services.erp-graphql.loadbalancer.server.port=8080
            ## Redirect HTTP -> HTTPS
            - traefik.http.routers.erp-graphql.middlewares=erp-graphql-https@docker
        networks:
            - docker-stack_webgateway

    erp-server-cups:
        container_name: erp-server-cups
        restart: unless-stopped
        image: registry.gitlab.com/son-video/docker-stack/cups
        volumes:
            - ./../var/pdf:/var/spool/cups-pdf/ANONYMOUS
            - /etc/ssl/certs/ca-certificates.crt:/etc/ssl/certs/ca-certificates.crt:ro
        working_dir: /var/spool/cups-pdf/ANONYMOUS
        labels:
            - traefik.enable=true
            - traefik.http.routers.erp-cups.entrypoints=web
            - traefik.http.routers.erp-cups.rule=Host(`erp-cups.lxc`)
            - traefik.http.services.erp-cups.loadbalancer.server.port=631
        networks:
            - docker-stack_webgateway

# use apps network
networks:
    docker-stack_webgateway:
        external: true

volumes:
    erp-server-mysql-test-data:
        external: true
