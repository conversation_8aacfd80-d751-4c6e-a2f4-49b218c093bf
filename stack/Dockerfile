# Global arguments (exposed/overridable)
ARG APP_NAME="erp-server"
ARG PHP_VERSION="7.4"
ARG DEBIAN_VERSION="bullseye"
ARG COMPOSER_VERSION="2.6.2"
ARG PSQL_VERSION="15"
ARG TZ="Europe/Paris"
ARG NODEJS_VERSION="20"

FROM php:${PHP_VERSION}-apache-${DEBIAN_VERSION}
ADD https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/
RUN chmod +x /usr/local/bin/install-php-extensions

ARG APP_NAME
ARG PHP_VERSION
ARG DEBIAN_VERSION
ARG COMPOSER_VERSION
ARG PSQL_VERSION
ARG TZ
ARG NODEJS_VERSION

ENV TZ ${TZ}
ENV APP_DEBUG=1
ENV DEBIAN_FRONTEND=noninteractive
ENV APT_KEY_DONT_WARN_ON_DANGEROUS_USAGE=DontWarn
### good colors for most applications ###
ENV TERM xterm
# avoid million NPM install messages
ENV npm_config_loglevel warn

# Configuration
COPY ./php $PHP_INI_DIR
COPY ./apache2 /etc/apache2

# PHP Packages
RUN \
    # Node.js (fetch only, the library is installed via apt-get install after)
    curl -sL https://deb.nodesource.com/setup_$NODEJS_VERSION.x -o nodesource_setup.sh && \
    bash nodesource_setup.sh && \
    ### Install librairies from APT
    apt-get update && \
    apt-get install -y --no-install-recommends \
        ca-certificates \
        cups-client \
        curl \
        default-mysql-client \
        git \
        gnupg2 \
        jq \
        less \
        make \
        mcrypt \
        net-tools \
        nodejs \
        openssl \
        ssh \
        unzip \
        vim \
        wget \
    && \
    # Install pg client
    echo "deb http://apt.postgresql.org/pub/repos/apt/ $DEBIAN_VERSION-pgdg main" > /etc/apt/sources.list.d/dotdeb.list \
    && wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - \
    && apt-get update -y \
    && apt-get  install -y --no-install-recommends postgresql-client-$PSQL_VERSION \
    && \
    ### PHP extensions \
    # https://github.com/mlocati/docker-php-extension-installer/blob/master/README.md#supported-php-extensions
    install-php-extensions \
        apcu \
        bz2 \
        gd \
        imap \
        intl \
        mbstring \
        mysqli \
        opcache \
        pdo_mysql \
        pdo_pgsql \
        pgsql \
        soap \
        zip \
        @composer-$COMPOSER_VERSION \
        xdebug \
    && \
    ### Clean APT
    apt-get clean && \
    apt-get autoremove -y && \
    apt-get autoclean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* && \
    ### Apache configuration \
    a2enmod rewrite env && \
    a2dismod -f access_compat authz_user filter alias autoindex auth_basic deflate setenvif authn_file status && \
    sed -i "s/{APP_NAME}/${APP_NAME}/g" /etc/apache2/sites-available/000-default.conf && \
    cat /etc/apache2/sites-available/000-default.conf && \
    # ### Create project folder \
    mkdir -p /var/www/$APP_NAME/public && \
    ### Fix home permission (for .composer/cache)
    chmod 777 /home -R

WORKDIR /var/www/$APP_NAME
